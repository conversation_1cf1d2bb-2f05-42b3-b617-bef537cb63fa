{"data": {"ruleId": "WCAG-066", "ruleName": "Error Prevention Enhanced", "category": "robust", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0305, "automated": true, "evidence": [{"type": "interaction", "description": "Error prevention analysis", "value": "Found 2 forms/inputs with inadequate error prevention mechanisms", "elementCount": 1, "affectedSelectors": ["Found", "forms", "inputs", "with", "inadequate", "error", "prevention", "mechanisms"], "severity": "error", "metadata": {"scanDuration": 1289, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-066", "ruleName": "Error Prevention Enhanced", "timestamp": "2025-07-11T11:39:15.101Z"}}}, {"type": "interaction", "description": "Form lacks error prevention (low risk)", "value": "1 fields, missing: validation", "selector": "#trial-scan", "severity": "info", "metadata": {"scanDuration": 1289, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-066", "ruleName": "Error Prevention Enhanced", "timestamp": "2025-07-11T11:39:15.101Z"}}, "elementCount": 1, "affectedSelectors": ["#trial-scan", "fields", "missing", "validation"]}], "recommendations": ["Implement validation for all user inputs", "Add confirmation dialogs for high-risk operations", "Provide review/preview functionality for critical forms", "Include undo mechanisms where possible", "CRITICAL: Add multiple error prevention mechanisms for critical operations", "Require confirmation fields for password changes and deletions"], "executionTime": 4, "originalScore": 75}, "timestamp": 1752233955101, "hash": "91631ee0d6870c9a19b851c20bc9f509", "accessCount": 1, "lastAccessed": 1752233955101, "size": 1640}
{"data": {"ruleId": "WCAG-019", "ruleName": "Keyboard Focus 3.0", "category": "operable", "wcagVersion": "2.2", "successCriterion": "0.0.0", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.08, "automated": false, "evidence": [{"type": "text", "description": "Enhanced keyboard focus analysis summary", "value": "4/218 checks pass automated tests, 0 require manual review", "severity": "error", "elementCount": 1, "affectedSelectors": ["checks", "pass", "automated", "tests", "require", "manual", "review"], "metadata": {"scanDuration": 1581, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-019", "ruleName": "Keyboard Focus 3.0", "timestamp": "2025-07-11T11:38:04.956Z"}}}, {"type": "text", "description": "Expandable content found (collapsed)", "value": "Content is collapsed - focus management needs testing when expanded", "selector": "#headlessui-menu-button-:Rspmm:", "severity": "info", "elementCount": 1, "affectedSelectors": ["#headlessui-menu-button-:Rspmm:", "Content", "is", "collapsed", "focus", "management", "needs", "testing", "when", "expanded"], "metadata": {"scanDuration": 1581, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-019", "ruleName": "Keyboard Focus 3.0", "timestamp": "2025-07-11T11:38:04.956Z"}}}, {"type": "text", "description": "Expandable content found (collapsed)", "value": "Content is collapsed - focus management needs testing when expanded", "selector": "#headlessui-menu-button-:Ru9mm:", "severity": "info", "elementCount": 1, "affectedSelectors": ["#headlessui-menu-button-:Ru9mm:", "Content", "is", "collapsed", "focus", "management", "needs", "testing", "when", "expanded"], "metadata": {"scanDuration": 1581, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-019", "ruleName": "Keyboard Focus 3.0", "timestamp": "2025-07-11T11:38:04.956Z"}}}, {"type": "text", "description": "Expandable content found (collapsed)", "value": "Content is collapsed - focus management needs testing when expanded", "selector": "#headlessui-menu-button-:Rfdmm:", "severity": "info", "elementCount": 1, "affectedSelectors": ["#headlessui-menu-button-:Rfdmm:", "Content", "is", "collapsed", "focus", "management", "needs", "testing", "when", "expanded"], "metadata": {"scanDuration": 1581, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-019", "ruleName": "Keyboard Focus 3.0", "timestamp": "2025-07-11T11:38:04.956Z"}}}, {"type": "text", "description": "Tab navigation sequence appears logical", "value": "Tested 10 elements", "severity": "info", "elementCount": 10, "affectedSelectors": ["Tested", "elements"], "metadata": {"scanDuration": 1581, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-019", "ruleName": "Keyboard Focus 3.0", "timestamp": "2025-07-11T11:38:04.956Z"}}}], "recommendations": ["Add visible focus indicator to a.flex", "Add visible focus indicator to #headlessui-menu-button-:Rspmm:", "Add visible focus indicator to a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "Add visible focus indicator to a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "Add visible focus indicator to #headlessui-menu-button-:Ru9mm:", "Add visible focus indicator to a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "Add visible focus indicator to a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.order-[0.5px].hidden.flex-none.rounded-md.border-white/50.bg-white.!text-dark-blue.sm:block.text-base.font-bold", "Add visible focus indicator to a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.hidden.flex-none.whitespace-nowrap.rounded-md.border-[0.5px].border-dark-blue/75.bg-dark-blue.font-formadjrtext.tracking-[0.45px].text-white.shadow-[0_4px_16px_0_rgba(0,0,0,0.25)].sm:block.text-base.font-bold", "Add visible focus indicator to #headlessui-menu-button-:Rfdmm:", "Add visible focus indicator to input.grow.rounded-lg.border-[0.5px].border-black-v2-50.bg-white.px-6.py-3.5.font-formadjrtext.text-lg.leading-7.text-dark-blue.placeholder:text-[#bcbec4].sm:pr-44", "Add visible focus indicator to button.right-1.flex.w-full.flex-row.items-center.justify-center.whitespace-nowrap.rounded-lg.border-[0.5px].border-green-highlight-dark.bg-green-highlight.px-4.py-3.font-formadjrtext.text-base.font-bold.leading-5.tracking-[0.4px].text-dark-blue.sm:absolute.sm:w-auto.sm:rounded", "Add visible focus indicator to a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Add visible focus indicator to a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Add visible focus indicator to a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Add visible focus indicator to a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Add visible focus indicator to a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px].!bg-dark-blue.!text-white", "Add visible focus indicator to #demo-account-login-button", "Add visible focus indicator to a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "Add visible focus indicator to a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "Add visible focus indicator to button.control-arrow.control-prev.control-disabled", "Add visible focus indicator to button.control-arrow.control-next.control-disabled", "Add visible focus indicator to button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0", "Add visible focus indicator to button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "Add visible focus indicator to a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Add visible focus indicator to a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Add visible focus indicator to a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Add visible focus indicator to a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Add visible focus indicator to a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Add visible focus indicator to a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Add visible focus indicator to a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Add visible focus indicator to a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Add visible focus indicator to a.flex.flex-row.items-center.font-medium.leading-6.hover:underline", "Add visible focus indicator to button.control-arrow.control-prev.control-disabled", "Add visible focus indicator to a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Add visible focus indicator to a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Add visible focus indicator to a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Add visible focus indicator to a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Add visible focus indicator to a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Add visible focus indicator to button.control-arrow.control-next.control-disabled", "Add visible focus indicator to button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0", "Add visible focus indicator to button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "Add visible focus indicator to a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px]", "Add visible focus indicator to #demo-account-login-button", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline", "Add visible focus indicator to a.text-sm.leading-5.!text-white.hover:underline"], "executionTime": 0, "errorMessage": "Element missing focus indicator: a.flex; Element missing focus indicator: #headlessui-menu-button-:Rspmm:; Element missing focus indicator: a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30; Element missing focus indicator: a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30; Element missing focus indicator: #headlessui-menu-button-:Ru9mm:; Element missing focus indicator: a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30; Element missing focus indicator: a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.order-[0.5px].hidden.flex-none.rounded-md.border-white/50.bg-white.!text-dark-blue.sm:block.text-base.font-bold; Element missing focus indicator: a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.hidden.flex-none.whitespace-nowrap.rounded-md.border-[0.5px].border-dark-blue/75.bg-dark-blue.font-formadjrtext.tracking-[0.45px].text-white.shadow-[0_4px_16px_0_rgba(0,0,0,0.25)].sm:block.text-base.font-bold; Element missing focus indicator: #headlessui-menu-button-:Rfdmm:; Element missing focus indicator: input.grow.rounded-lg.border-[0.5px].border-black-v2-50.bg-white.px-6.py-3.5.font-formadjrtext.text-lg.leading-7.text-dark-blue.placeholder:text-[#bcbec4].sm:pr-44; Element missing focus indicator: button.right-1.flex.w-full.flex-row.items-center.justify-center.whitespace-nowrap.rounded-lg.border-[0.5px].border-green-highlight-dark.bg-green-highlight.px-4.py-3.font-formadjrtext.text-base.font-bold.leading-5.tracking-[0.4px].text-dark-blue.sm:absolute.sm:w-auto.sm:rounded; Element missing focus indicator: a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline; Element missing focus indicator: a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline; Element missing focus indicator: a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline; Element missing focus indicator: a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline; Element missing focus indicator: a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px].!bg-dark-blue.!text-white; Element missing focus indicator: #demo-account-login-button; Element missing focus indicator: a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline; Element missing focus indicator: a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline; Element missing focus indicator: button.control-arrow.control-prev.control-disabled; Element missing focus indicator: button.control-arrow.control-next.control-disabled; Element missing focus indicator: button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0; Element missing focus indicator: button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15; Element missing focus indicator: a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6; Element missing focus indicator: a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6; Element missing focus indicator: a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6; Element missing focus indicator: a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6; Element missing focus indicator: a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6; Element missing focus indicator: a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6; Element missing focus indicator: a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6; Element missing focus indicator: a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6; Element missing focus indicator: a.flex.flex-row.items-center.font-medium.leading-6.hover:underline; Element missing focus indicator: button.control-arrow.control-prev.control-disabled; Element missing focus indicator: a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]; Element missing focus indicator: a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]; Element missing focus indicator: a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]; Element missing focus indicator: a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]; Element missing focus indicator: a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]; Element missing focus indicator: button.control-arrow.control-next.control-disabled; Element missing focus indicator: button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0; Element missing focus indicator: button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15; Element missing focus indicator: a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px]; Element missing focus indicator: #demo-account-login-button; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline; Element missing focus indicator: a.text-sm.leading-5.!text-white.hover:underline", "manualReviewItems": []}, "timestamp": 1752233884956, "hash": "6cc8b6981a58ae8a6462512af9089276", "accessCount": 1, "lastAccessed": 1752233884956, "size": 42888}
{"data": [{"element": "html.no-js > body.home.wp-singular.page-template-default.page.page-id-15335.wp-custom-logo.wp-embed-responsive.wp-theme-kadence.stk--is-kadence-theme.kadence.footer-on-bottom.hide-focus-outline.link-style-standard.content-title-style-hide.content-width-normal.content-style-unboxed.content-vertical-padding-hide.non-transparent-header.mobile-non-transparent-header > noscript", "text": {"text": "<iframe src=https://www.googletagmanager.com/ns.html?id=GTM-N8SQL3V\nheight=\"0\" width=\"0\" style=\"display:none;visibility:hidden\"></iframe>", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "html.no-js > body.home.wp-singular.page-template-default.page.page-id-15335.wp-custom-logo.wp-embed-responsive.wp-theme-kadence.stk--is-kadence-theme.kadence.footer-on-bottom.hide-focus-outline.link-style-standard.content-title-style-hide.content-width-normal.content-style-unboxed.content-vertical-padding-hide.non-transparent-header.mobile-non-transparent-header > div.site.wp-site-blocks > a.skip-link.screen-reader-text.scroll-ignore", "text": {"text": "Skip to content", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.site-header-item.site-header-focus-item.site-header-item-main-navigation.header-navigation-layout-stretch-false.header-navigation-layout-fill-stretch-false > nav.secondary-navigation.header-navigation.nav--toggle-sub.header-navigation-style-standard.header-navigation-dropdown-animation-none > div.secondary-menu-container.header-menu-container > ul.menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-17149.kadence-menu-has-icon.kadence-menu-icon-side-left > a", "text": {"text": "Contact Sales: (*************", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 12, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.site-header-item.site-header-focus-item.site-header-item-main-navigation.header-navigation-layout-stretch-false.header-navigation-layout-fill-stretch-false > nav.secondary-navigation.header-navigation.nav--toggle-sub.header-navigation-style-standard.header-navigation-dropdown-animation-none > div.secondary-menu-container.header-menu-container > ul.menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-115.kadence-menu-has-icon.kadence-menu-icon-side-left > a", "text": {"text": "Contact Support", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 12, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "nav.secondary-navigation.header-navigation.nav--toggle-sub.header-navigation-style-standard.header-navigation-dropdown-animation-none > div.secondary-menu-container.header-menu-container > ul.menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-92.kadence-menu-has-icon.kadence-menu-icon-side-left > a > span.nav-drop-title-wrap", "text": {"text": "LoginExpand", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 12, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "a > span.nav-drop-title-wrap > span.dropdown-nav-toggle > span.kadence-svg-iconset.svg-baseline > svg > title", "text": {"text": "Expand", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 10.8, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.secondary-menu-container.header-menu-container > ul.menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-92.kadence-menu-has-icon.kadence-menu-icon-side-left > ul.sub-menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-94 > a", "text": {"text": "TigerConnect", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 12, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "confidence": 0.5}, "contrast": {"ratio": 11.49, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.secondary-menu-container.header-menu-container > ul.menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-92.kadence-menu-has-icon.kadence-menu-icon-side-left > ul.sub-menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-96 > a", "text": {"text": "Physician <PERSON>", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 12, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "confidence": 0.5}, "contrast": {"ratio": 11.49, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.secondary-menu-container.header-menu-container > ul.menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-92.kadence-menu-has-icon.kadence-menu-icon-side-left > ul.sub-menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-95 > a", "text": {"text": "TigerConnect Community", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 12, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "confidence": 0.5}, "contrast": {"ratio": 11.49, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.secondary-menu-container.header-menu-container > ul.menu > li.astm-search-menu.is-menu.is-dropdown.menu-item > form.is-search-form.is-form-style.is-form-style-3.is-form-id-3779.is-ajax-search > label > span.is-screen-reader-text", "text": {"text": "Search for:", "color": {"r": 0, "g": 0, "b": 0, "a": 1, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 0, "g": 0, "b": 0, "a": 1, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 19.09, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(0, 0, 0)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.secondary-menu-container.header-menu-container > ul.menu > li.astm-search-menu.is-menu.is-dropdown.menu-item > form.is-search-form.is-form-style.is-form-style-3.is-form-id-3779.is-ajax-search > button.is-search-submit > span.is-screen-reader-text", "text": {"text": "Search Button", "color": {"r": 0, "g": 0, "b": 0, "a": 1, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "fontSize": 18, "fontWeight": "400", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 0, "g": 0, "b": 0, "a": 1, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 19.09, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(0, 0, 0)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.site-header-item.site-header-focus-item.site-header-item-main-navigation.header-navigation-layout-stretch-false.header-navigation-layout-fill-stretch-false > nav.main-navigation.header-navigation.nav--toggle-sub.header-navigation-style-underline.header-navigation-dropdown-animation-none > div.primary-menu-container.header-menu-container > ul.menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.current-menu-item.current_page_item.menu-item-home.menu-item-15577 > a", "text": {"text": "Home", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "nav.main-navigation.header-navigation.nav--toggle-sub.header-navigation-style-underline.header-navigation-dropdown-animation-none > div.primary-menu-container.header-menu-container > ul.menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-105.kadence-menu-mega-enabled.kadence-menu-mega-width-content.kadence-menu-mega-columns-1.kadence-menu-mega-layout-equal > a > span.nav-drop-title-wrap", "text": {"text": "Who We ServeExpand", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "a > span.nav-drop-title-wrap > span.dropdown-nav-toggle > span.kadence-svg-iconset.svg-baseline > svg > title", "text": {"text": "Expand", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 10.8, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column419_53a1b5-b0.kb-section-has-link > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column419_dbfa0f-fe > div.kt-inside-inner-col > h3.kt-adv-heading419_269ffe-a6.wp-block-kadence-advancedheading", "text": {"text": "Who We Serve​", "color": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "fontSize": 20, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(50, 84, 124)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column419_53a1b5-b0.kb-section-has-link > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column419_dbfa0f-fe > div.kt-inside-inner-col > p.kt-adv-heading419_cb424e-6c.wp-block-kadence-advancedheading", "text": {"text": "See how secure clinical collaboration software can help improve patient care and your bottom line.", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column419_dbfa0f-fe > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns419_3002fb-bc > span.kb-button.kt-button.button.kb-btn419_1ac3e8-0e.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Learn More", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column419_701932-88 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box419_3ae7e9-b1 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Services", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 18, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column419_701932-88 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box419_98e6a6-59.btn > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Contact Us", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column419_adffe2-5d > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box419_6cf45b-23 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Healthcare Professionals", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 18, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column419_adffe2-5d > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box419_33f4db-aa.btn > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Resources", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column419_6453ce-b8 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box419_67dd94-90 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Healthcare Organizations", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 18, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column419_6453ce-b8 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box419_2c31a8-99.btn > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Get a Demo", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "nav.main-navigation.header-navigation.nav--toggle-sub.header-navigation-style-underline.header-navigation-dropdown-animation-none > div.primary-menu-container.header-menu-container > ul.menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-106.kadence-menu-mega-enabled.kadence-menu-mega-width-content.kadence-menu-mega-columns-1.kadence-menu-mega-layout-equal > a > span.nav-drop-title-wrap", "text": {"text": "SolutionsExpand", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "a > span.nav-drop-title-wrap > span.dropdown-nav-toggle > span.kadence-svg-iconset.svg-baseline > svg > title", "text": {"text": "Expand", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 10.8, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column551_954935-98.kb-section-has-link > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column551_494d60-05 > div.kt-inside-inner-col > h3.kt-adv-heading551_3d8df0-a8.wp-block-kadence-advancedheading", "text": {"text": "Solutions​", "color": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "fontSize": 20, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(50, 84, 124)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column551_954935-98.kb-section-has-link > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column551_494d60-05 > div.kt-inside-inner-col > p.kt-adv-heading551_b6ed5d-3e.wp-block-kadence-advancedheading", "text": {"text": "Coordinate care across teams and facilities with a full suite of collaboration products to accelerate care delivery.", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column551_494d60-05 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns551_5f0ee9-aa > span.kb-button.kt-button.button.kb-btn551_461c8a-1d.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Learn More", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column551_b65614-61 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box551_7840ad-35.btn > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Contact Us", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column551_af7bd1-1d > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box551_45697e-ae > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Solutions", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 18, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column551_af7bd1-1d > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box551_ca4691-89.btn > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Resources", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column551_204336-ce > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box551_5b0a69-04 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Workflows", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 18, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column551_204336-ce > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box551_87f46e-fe.btn > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Get a Demo", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "nav.main-navigation.header-navigation.nav--toggle-sub.header-navigation-style-underline.header-navigation-dropdown-animation-none > div.primary-menu-container.header-menu-container > ul.menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-107.kadence-menu-mega-enabled.kadence-menu-mega-width-content.kadence-menu-mega-columns-1.kadence-menu-mega-layout-equal > a > span.nav-drop-title-wrap", "text": {"text": "ResourcesExpand", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "a > span.nav-drop-title-wrap > span.dropdown-nav-toggle > span.kadence-svg-iconset.svg-baseline > svg > title", "text": {"text": "Expand", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 10.8, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column553_d903bc-90.kb-section-has-link > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column553_3c5abb-5a > div.kt-inside-inner-col > h3.kt-adv-heading553_520000-de.wp-block-kadence-advancedheading", "text": {"text": "Resources", "color": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "fontSize": 20, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(50, 84, 124)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column553_d903bc-90.kb-section-has-link > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column553_3c5abb-5a > div.kt-inside-inner-col > p.kt-adv-heading553_0100d0-e2.wp-block-kadence-advancedheading", "text": {"text": "It’s all here! Explore our latest ebooks, blog articles, guides, videos, checklists and more.", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column553_3c5abb-5a > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns553_b19ef2-c9 > span.kb-button.kt-button.button.kb-btn553_df0944-31.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Learn More", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column553_b1e06b-01 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box553_1bac95-aa > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Support", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 18, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column553_b1e06b-01 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box553_2e24be-7c.btn > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Contact Us", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column553_2cdd17-92 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box553_aad343-a4 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Resources", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 18, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column553_2cdd17-92 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box553_5c751e-df.btn > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Resources", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column553_e5e812-96 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box553_40da56-09 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Resources", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 18, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column553_e5e812-96 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box553_5a09c5-dc.btn > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Get a Demo", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "nav.main-navigation.header-navigation.nav--toggle-sub.header-navigation-style-underline.header-navigation-dropdown-animation-none > div.primary-menu-container.header-menu-container > ul.menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-108.kadence-menu-mega-enabled.kadence-menu-mega-width-content.kadence-menu-mega-columns-1.kadence-menu-mega-layout-equal > a > span.nav-drop-title-wrap", "text": {"text": "Why TigerConnect?Expand", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "a > span.nav-drop-title-wrap > span.dropdown-nav-toggle > span.kadence-svg-iconset.svg-baseline > svg > title", "text": {"text": "Expand", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 10.8, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column555_b45fd4-81.kb-section-has-link > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column555_23ef65-b7 > div.kt-inside-inner-col > h3.kt-adv-heading555_0f5d93-e9.wp-block-kadence-advancedheading", "text": {"text": "Why TigerConnnect?", "color": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "fontSize": 20, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(50, 84, 124)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column555_b45fd4-81.kb-section-has-link > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column555_23ef65-b7 > div.kt-inside-inner-col > p.kt-adv-heading555_6fbe52-00.wp-block-kadence-advancedheading", "text": {"text": "Since 2010 TigerConnect has been transforming healthcare communications by enabling our customers to automate clinical workflows, speed decisions, and improve patient outcomes.", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column555_23ef65-b7 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns555_39caac-5f > span.kb-button.kt-button.button.kb-btn555_c22b62-61.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Learn More", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column555_4e9d2a-b5 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box555_1c1cc3-d0.btn > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Contact Us", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "700", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column555_8cda35-fa > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box555_45a944-16 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Resources", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 18, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column555_8cda35-fa > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box555_d5ed37-60.btn > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Resources", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column555_d16a1d-3d > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box555_32be0a-5f > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Why TigerConnect?", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 18, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column555_d16a1d-3d > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box555_9ee756-6b.btn > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Get a Demo", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 50, "g": 84, "b": 124, "a": 1, "hex": "#32547c", "hsl": {"h": 212.43243243243242, "s": 42.5287356321839, "l": 34.11764705882353}, "luminance": 0.08473993346686254}, "confidence": 0.5}, "contrast": {"ratio": 7.79, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.site-main-header-inner-wrap.site-header-row.site-header-row-has-sides.site-header-row-center-column > div.site-header-main-section-right.site-header-section.site-header-section-right > div.site-header-item.site-header-focus-item > div.header-button-wrap > div.header-button-inner-wrap > a.button.header-button.button-size-custom.button-style-filled", "text": {"text": "Get a Demo", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "hasTransparency": false, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "confidence": 0.9}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.9900000000000001, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgb(234, 0, 41) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-toggle-open-container > button.menu-toggle-open.drawer-toggle.menu-toggle-style-default > span.menu-toggle-icon > span.kadence-svg-iconset > svg > title", "text": {"text": "Toggle <PERSON>", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 20, "fontWeight": "400", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.entry-content.single-content > div.kb-row-layout-wrap.kb-row-layout-id15335_ae5525-53.alignfull.has-theme-palette7-background-color.kt-row-has-bg.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-1-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_f4af22-75 > div.kt-inside-inner-col > p.has-theme-palette-4-color.has-text-color.has-link-color.wp-elements-194ec291757c6acc0a6de6b800c49d6e", "text": {"text": "Unified Healthcare Communication", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.entry-content.single-content > div.kb-row-layout-wrap.kb-row-layout-id15335_ae5525-53.alignfull.has-theme-palette7-background-color.kt-row-has-bg.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-1-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_f4af22-75 > div.kt-inside-inner-col > h1.kt-adv-heading15335_d2c3f3-b9.wp-block-kadence-advancedheading.has-theme-palette-3-color.has-text-color", "text": {"text": "One Platform to Unify Communications", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 56, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 11.49, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.entry-content.single-content > div.kb-row-layout-wrap.kb-row-layout-id15335_ae5525-53.alignfull.has-theme-palette7-background-color.kt-row-has-bg.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-1-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_f4af22-75 > div.kt-inside-inner-col > h1.kt-adv-heading15335_d2c3f3-b9.wp-block-kadence-advancedheading.has-theme-palette-3-color.has-text-color", "text": {"text": "One Platform to Unify Communications", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 56, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 11.49, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.entry-content.single-content > div.kb-row-layout-wrap.kb-row-layout-id15335_ae5525-53.alignfull.has-theme-palette7-background-color.kt-row-has-bg.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-1-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_f4af22-75 > div.kt-inside-inner-col > p.kt-adv-heading15335_835bbc-ee.wp-block-kadence-advancedheading.has-theme-palette-3-color.has-text-color", "text": {"text": "Discover how leading hospitals and health systems rely on TigerConnect to enhance patient throughput across the care continuum.", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 20, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 11.49, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-1-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_f4af22-75 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_8011a0-53 > a.kb-button.kt-button.button.kb-btn15335_361967-05.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Watch Video", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14.4, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "confidence": 0.5}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-1-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_f4af22-75 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_8011a0-53 > a.kb-button.kt-button.button.kb-btn15335_099863-04.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Take a Tour", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 14.4, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_55f70b-f8 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_871db6-42 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "text": {"text": "Pre-Hospital", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 0, "g": 50, "b": 82, "a": 1, "hex": "#003252", "hsl": {"h": 203.41463414634148, "s": 100, "l": 16.07843137254902}, "luminance": 0.02890400532730539}, "confidence": 0.5}, "contrast": {"ratio": 13.31, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_f68ba5-9a.kb-section-has-link > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_79fe83-7c > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "text": {"text": "Clinical Collaboration", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 0, "g": 50, "b": 82, "a": 1, "hex": "#003252", "hsl": {"h": 203.41463414634148, "s": 100, "l": 16.07843137254902}, "luminance": 0.02890400532730539}, "confidence": 0.5}, "contrast": {"ratio": 13.31, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_3b7f0c-ff.kb-section-has-link > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_09a7c5-e2 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "text": {"text": "Physician<PERSON><PERSON>uling", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 0, "g": 50, "b": 82, "a": 1, "hex": "#003252", "hsl": {"h": 203.41463414634148, "s": 100, "l": 16.07843137254902}, "luminance": 0.02890400532730539}, "confidence": 0.5}, "contrast": {"ratio": 13.31, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_3b7f0c-ff.kb-section-has-link > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_09a7c5-e2 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "text": {"text": "Physician<PERSON><PERSON>uling", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 0, "g": 50, "b": 82, "a": 1, "hex": "#003252", "hsl": {"h": 203.41463414634148, "s": 100, "l": 16.07843137254902}, "luminance": 0.02890400532730539}, "confidence": 0.5}, "contrast": {"ratio": 13.31, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_accf99-e8.kb-section-has-link > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_d4d585-5c > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "text": {"text": "Alarm Management & Event Notification", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 0, "g": 50, "b": 82, "a": 1, "hex": "#003252", "hsl": {"h": 203.41463414634148, "s": 100, "l": 16.07843137254902}, "luminance": 0.02890400532730539}, "confidence": 0.5}, "contrast": {"ratio": 13.31, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_b5f972-f5.kb-section-has-link > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_5d7cab-9c > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "text": {"text": "PatientEngagement", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 0, "g": 50, "b": 82, "a": 1, "hex": "#003252", "hsl": {"h": 203.41463414634148, "s": 100, "l": 16.07843137254902}, "luminance": 0.02890400532730539}, "confidence": 0.5}, "contrast": {"ratio": 13.31, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_b5f972-f5.kb-section-has-link > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_5d7cab-9c > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "text": {"text": "PatientEngagement", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 0, "g": 50, "b": 82, "a": 1, "hex": "#003252", "hsl": {"h": 203.41463414634148, "s": 100, "l": 16.07843137254902}, "luminance": 0.02890400532730539}, "confidence": 0.5}, "contrast": {"ratio": 13.31, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_fed9da-b4.kb-section-has-overlay > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_1c3187-77 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "text": {"text": "CareConduit", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 0, "g": 50, "b": 82, "a": 1, "hex": "#003252", "hsl": {"h": 203.41463414634148, "s": 100, "l": 16.07843137254902}, "luminance": 0.02890400532730539}, "confidence": 0.5}, "contrast": {"ratio": 13.31, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "article.entry.content-bg.single-entry.post-15335.page.type-page.status-publish.has-post-thumbnail.hentry > div.entry-content-wrap > div.entry-content.single-content > div.wp-block-kadence-column.kadence-column15335_ff5b9d-f6 > div.kt-inside-inner-col > h2.wp-block-heading.has-text-align-center", "text": {"text": "Streamline Workflows Across the Care Continuum", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 34, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "article.entry.content-bg.single-entry.post-15335.page.type-page.status-publish.has-post-thumbnail.hentry > div.entry-content-wrap > div.entry-content.single-content > div.wp-block-kadence-column.kadence-column15335_ff5b9d-f6 > div.kt-inside-inner-col > p.has-text-align-center", "text": {"text": "TigerConnect solutions make healthcare communication easy by putting information, data, and alerts in the hands of care teams.", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_d1792c-e0 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_50f6e7-57 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center > div.kt-infobox-textcontent > h5.kt-blocks-info-box-title", "text": {"text": "Critical Response", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 20, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_ad5697-6a > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_7e3e63-9b > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center > div.kt-infobox-textcontent > h5.kt-blocks-info-box-title", "text": {"text": "Emergency Department", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 20, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_5c8020-92 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_402b64-f7 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center > div.kt-infobox-textcontent > h5.kt-blocks-info-box-title", "text": {"text": "Inpatient Care", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 20, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_7a2d1d-7c > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_cbf15a-02 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center > div.kt-infobox-textcontent > h5.kt-blocks-info-box-title", "text": {"text": "Operating Room", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 20, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_8ef727-5b > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_e56051-e1 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-top.kt-info-halign-center > div.kt-infobox-textcontent > h5.kt-blocks-info-box-title", "text": {"text": "Post Acute/Ambulatory", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 20, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.entry-content.single-content > div.wp-block-kadence-column.kadence-column15335_ff5b9d-f6 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_7374b3-18 > a.kb-button.kt-button.button.kb-btn15335_dfd3b6-71.kt-btn-size-standard.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Learn More", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "confidence": 0.5}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.entry-content.single-content > div.kb-row-layout-wrap.kb-row-layout-id15335_605995-b4.alignnone.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-2-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_e6bf4b-2e > div.kt-inside-inner-col > h2.kt-adv-heading15335_c26c48-6d.wp-block-kadence-advancedheading", "text": {"text": "The Future of Unified Healthcare Communications", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 38, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.entry-content.single-content > div.kb-row-layout-wrap.kb-row-layout-id15335_605995-b4.alignnone.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-2-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_e6bf4b-2e > div.kt-inside-inner-col > p.kt-adv-heading15335_3827ba-71.wp-block-kadence-advancedheading", "text": {"text": "TigerConnect’s vision for the future is that care teams have the information they need at their fingertips, so they can communicate with more precision, treat patients faster, and improve patient outcomes.", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kadence-video-popup-wrap.kadence-video-shadow > div.kadence-video-intrinsic > a.kadence-video-popup-link.kadence-video-type-external > span.kb-svg-icon-wrap.kb-svg-icon-fas_play.kt-video-svg-icon.kt-video-svg-icon-style-default.kt-video-svg-icon-fas.play.kt-video-play-animation-none.kt-video-svg-icon-size-63 > svg > title", "text": {"text": "Play", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 63, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.entry-content.single-content > div.kb-row-layout-wrap.kb-row-layout-id15335_e78219-79.alignnone.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-1-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_9a192a-36 > div.kt-inside-inner-col > h2.kt-adv-heading15335_bc26ca-d1.wp-block-kadence-advancedheading", "text": {"text": "Improve Collaboration. <PERSON><PERSON><PERSON>ient Outcomes.", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 44, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.entry-content.single-content > div.kb-row-layout-wrap.kb-row-layout-id15335_e78219-79.alignnone.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-1-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_9a192a-36 > div.kt-inside-inner-col > h2.kt-adv-heading15335_bc26ca-d1.wp-block-kadence-advancedheading", "text": {"text": "Improve Collaboration. <PERSON><PERSON><PERSON>ient Outcomes.", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 44, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_d5e529-24 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_224dba-8d > div.kt-inside-inner-col > h3.kt-adv-heading15335_d64f68-08.wp-block-kadence-advancedheading", "text": {"text": "20% increase", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 26, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 11.49, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_d5e529-24 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_224dba-8d > div.kt-inside-inner-col > p.kt-adv-heading15335_58d46e-2d.wp-block-kadence-advancedheading", "text": {"text": "in ER capacity from faster transfers", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_224dba-8d > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_58c0da-0f > a.kb-button.kt-button.button.kb-btn15335_ade17d-c4.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "See How", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 4.21, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_d5e529-24 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_bf8f55-5d > div.kt-inside-inner-col > h3.kt-adv-heading15335_895116-2f.wp-block-kadence-advancedheading", "text": {"text": "50% reduction", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 26, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 11.49, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_d5e529-24 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_bf8f55-5d > div.kt-inside-inner-col > p.kt-adv-heading15335_3e8357-ce.wp-block-kadence-advancedheading", "text": {"text": "in patient readmissions", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_bf8f55-5d > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_0e657c-83 > a.kb-button.kt-button.button.kb-btn15335_affa7a-67.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "See How", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 4.21, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_d5e529-24 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_15a1e7-3a > div.kt-inside-inner-col > h3.kt-adv-heading15335_a33455-21.wp-block-kadence-advancedheading", "text": {"text": "50% faster", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 26, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 11.49, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_d5e529-24 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_15a1e7-3a > div.kt-inside-inner-col > p.kt-adv-heading15335_94f9d4-3a.wp-block-kadence-advancedheading", "text": {"text": "door-to-needle time", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_15a1e7-3a > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_571efd-06 > a.kb-button.kt-button.button.kb-btn15335_ff539f-b1.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "See How", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 4.21, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_d5e529-24 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_9ae7bf-ca > div.kt-inside-inner-col > h3.kt-adv-heading15335_6d345e-e7.wp-block-kadence-advancedheading", "text": {"text": "70% improvement", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 26, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 11.49, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_d5e529-24 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_9ae7bf-ca > div.kt-inside-inner-col > p.kt-adv-heading15335_1863b1-ac.wp-block-kadence-advancedheading", "text": {"text": "in first case on-time starts", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_9ae7bf-ca > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_477186-1f > a.kb-button.kt-button.button.kb-btn15335_3e7e82-8d.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "See How", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 4.21, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_7f890c-cc > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_6f8ab3-cc > div.kt-inside-inner-col > h3.kt-adv-heading15335_8b274d-54.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color", "text": {"text": "Improve the cost, quality, and overall experience of care.", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 28, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 1, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 3:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_a1eb2a-c7 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_cc427a-7d > div.kt-inside-inner-col > h3.kt-adv-heading15335_bb6806-a5.wp-block-kadence-advancedheading", "text": {"text": "2.5 min reduction", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 26, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 11.49, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_a1eb2a-c7 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_cc427a-7d > div.kt-inside-inner-col > p.kt-adv-heading15335_947cdf-f3.wp-block-kadence-advancedheading", "text": {"text": "in code blue response time", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_cc427a-7d > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_d3a61e-23 > a.kb-button.kt-button.button.kb-btn15335_981f0f-b1.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "See How", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 4.21, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_a1eb2a-c7 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_492a04-34 > div.kt-inside-inner-col > h3.kt-adv-heading15335_9f2f72-40.wp-block-kadence-advancedheading", "text": {"text": "72% improvement", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 26, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 11.49, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_a1eb2a-c7 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_492a04-34 > div.kt-inside-inner-col > p.kt-adv-heading15335_3dc98e-c1.wp-block-kadence-advancedheading", "text": {"text": "in ED consult response times", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_492a04-34 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_52f36f-d2 > a.kb-button.kt-button.button.kb-btn15335_637b91-f5.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "See How", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 4.21, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_a1eb2a-c7 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_2da758-37 > div.kt-inside-inner-col > h3.kt-adv-heading15335_8e6925-5c.wp-block-kadence-advancedheading", "text": {"text": "20% reduction", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 26, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 11.49, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_a1eb2a-c7 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_2da758-37 > div.kt-inside-inner-col > p.kt-adv-heading15335_0ba886-39.wp-block-kadence-advancedheading", "text": {"text": "in length of stay", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_2da758-37 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_d59dd0-4d > a.kb-button.kt-button.button.kb-btn15335_4b75e1-85.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "See How", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 4.21, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_a1eb2a-c7 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_ef2c29-cb > div.kt-inside-inner-col > h3.kt-adv-heading15335_562598-d7.wp-block-kadence-advancedheading", "text": {"text": "46% improvement", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 26, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 11.49, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-3-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full > div.wp-block-kadence-column.kadence-column15335_a1eb2a-c7 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_ef2c29-cb > div.kt-inside-inner-col > p.kt-adv-heading15335_3d2b2c-81.wp-block-kadence-advancedheading", "text": {"text": "in sepsis bundle compliance", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 6.62, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_ef2c29-cb > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_c9d554-3e > a.kb-button.kt-button.button.kb-btn15335_965685-cb.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "See How", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "confidence": 0.5}, "contrast": {"ratio": 4.21, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "article.entry.content-bg.single-entry.post-15335.page.type-page.status-publish.has-post-thumbnail.hentry > div.entry-content-wrap > div.entry-content.single-content > div.wp-block-kadence-column.kadence-column15335_8af4d0-e8 > div.kt-inside-inner-col > h2.wp-block-heading.has-text-align-center", "text": {"text": "Solve Communication Challenges with a Single Platform", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 34, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-tabs-wrap.kt-tabs-id15335_ba0a97-00.kt-tabs-has-5-tabs.kt-active-tab-5.kt-tabs-layout-vtabs.kt-tabs-tablet-layout-tabs.kt-tabs-mobile-layout-tabs.kt-tab-alignment-left > ul.kt-tabs-title-list > li.kt-title-item.kt-title-item-1.kt-tabs-svg-show-always.kt-tabs-icon-side-right.kt-tab-title-inactive.kb-tabs-have-subtitle > a.kt-tab-title.kt-tab-title-1 > div.kb-tab-titles-wrap > span.kt-title-text", "text": {"text": "Pre-Hospital", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 28, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-tabs-wrap.kt-tabs-id15335_ba0a97-00.kt-tabs-has-5-tabs.kt-active-tab-5.kt-tabs-layout-vtabs.kt-tabs-tablet-layout-tabs.kt-tabs-mobile-layout-tabs.kt-tab-alignment-left > ul.kt-tabs-title-list > li.kt-title-item.kt-title-item-1.kt-tabs-svg-show-always.kt-tabs-icon-side-right.kt-tab-title-inactive.kb-tabs-have-subtitle > a.kt-tab-title.kt-tab-title-1 > div.kb-tab-titles-wrap > span.kt-title-sub-text", "text": {"text": "Connect EMS to hospital teams to accelerate time to treatment.", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-tabs-wrap.kt-tabs-id15335_ba0a97-00.kt-tabs-has-5-tabs.kt-active-tab-5.kt-tabs-layout-vtabs.kt-tabs-tablet-layout-tabs.kt-tabs-mobile-layout-tabs.kt-tab-alignment-left > ul.kt-tabs-title-list > li.kt-title-item.kt-title-item-2.kt-tabs-svg-show-always.kt-tabs-icon-side-right.kt-tab-title-inactive.kb-tabs-have-subtitle > a.kt-tab-title.kt-tab-title-2 > div.kb-tab-titles-wrap > span.kt-title-text", "text": {"text": "Physician <PERSON>", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 28, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-tabs-wrap.kt-tabs-id15335_ba0a97-00.kt-tabs-has-5-tabs.kt-active-tab-5.kt-tabs-layout-vtabs.kt-tabs-tablet-layout-tabs.kt-tabs-mobile-layout-tabs.kt-tab-alignment-left > ul.kt-tabs-title-list > li.kt-title-item.kt-title-item-2.kt-tabs-svg-show-always.kt-tabs-icon-side-right.kt-tab-title-inactive.kb-tabs-have-subtitle > a.kt-tab-title.kt-tab-title-2 > div.kb-tab-titles-wrap > span.kt-title-sub-text", "text": {"text": "Simplify provider scheduling with an intuitive, rules-based solution.", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-tabs-wrap.kt-tabs-id15335_ba0a97-00.kt-tabs-has-5-tabs.kt-active-tab-5.kt-tabs-layout-vtabs.kt-tabs-tablet-layout-tabs.kt-tabs-mobile-layout-tabs.kt-tab-alignment-left > ul.kt-tabs-title-list > li.kt-title-item.kt-title-item-3.kt-tabs-svg-show-always.kt-tabs-icon-side-right.kt-tab-title-inactive.kb-tabs-have-subtitle > a.kt-tab-title.kt-tab-title-3 > div.kb-tab-titles-wrap > span.kt-title-text", "text": {"text": "Clinical Collaboration", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 28, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-tabs-wrap.kt-tabs-id15335_ba0a97-00.kt-tabs-has-5-tabs.kt-active-tab-5.kt-tabs-layout-vtabs.kt-tabs-tablet-layout-tabs.kt-tabs-mobile-layout-tabs.kt-tab-alignment-left > ul.kt-tabs-title-list > li.kt-title-item.kt-title-item-3.kt-tabs-svg-show-always.kt-tabs-icon-side-right.kt-tab-title-inactive.kb-tabs-have-subtitle > a.kt-tab-title.kt-tab-title-3 > div.kb-tab-titles-wrap > span.kt-title-sub-text", "text": {"text": "Coordinate care seamlessly across the care continuum.", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-tabs-wrap.kt-tabs-id15335_ba0a97-00.kt-tabs-has-5-tabs.kt-active-tab-5.kt-tabs-layout-vtabs.kt-tabs-tablet-layout-tabs.kt-tabs-mobile-layout-tabs.kt-tab-alignment-left > ul.kt-tabs-title-list > li.kt-title-item.kt-title-item-4.kt-tabs-svg-show-always.kt-tabs-icon-side-right.kt-tab-title-inactive.kb-tabs-have-subtitle > a.kt-tab-title.kt-tab-title-4 > div.kb-tab-titles-wrap > span.kt-title-text", "text": {"text": "Alarm Management & Event Notification", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 28, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-tabs-wrap.kt-tabs-id15335_ba0a97-00.kt-tabs-has-5-tabs.kt-active-tab-5.kt-tabs-layout-vtabs.kt-tabs-tablet-layout-tabs.kt-tabs-mobile-layout-tabs.kt-tab-alignment-left > ul.kt-tabs-title-list > li.kt-title-item.kt-title-item-4.kt-tabs-svg-show-always.kt-tabs-icon-side-right.kt-tab-title-inactive.kb-tabs-have-subtitle > a.kt-tab-title.kt-tab-title-4 > div.kb-tab-titles-wrap > span.kt-title-sub-text", "text": {"text": "Stay on top of patient needs with instant, intelligently routed clinical system alerts.", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-tabs-wrap.kt-tabs-id15335_ba0a97-00.kt-tabs-has-5-tabs.kt-active-tab-5.kt-tabs-layout-vtabs.kt-tabs-tablet-layout-tabs.kt-tabs-mobile-layout-tabs.kt-tab-alignment-left > ul.kt-tabs-title-list > li.kt-title-item.kt-title-item-5.kt-tabs-svg-show-always.kt-tabs-icon-side-right.kt-tab-title-active.kb-tabs-have-subtitle > a.kt-tab-title.kt-tab-title-5 > div.kb-tab-titles-wrap > span.kt-title-text", "text": {"text": "Patient Engagement", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 28, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-tabs-wrap.kt-tabs-id15335_ba0a97-00.kt-tabs-has-5-tabs.kt-active-tab-5.kt-tabs-layout-vtabs.kt-tabs-tablet-layout-tabs.kt-tabs-mobile-layout-tabs.kt-tab-alignment-left > ul.kt-tabs-title-list > li.kt-title-item.kt-title-item-5.kt-tabs-svg-show-always.kt-tabs-icon-side-right.kt-tab-title-active.kb-tabs-have-subtitle > a.kt-tab-title.kt-tab-title-5 > div.kb-tab-titles-wrap > span.kt-title-sub-text", "text": {"text": "Effortlessly engage with patients and families before, during, and after care.", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.entry-content.single-content > div.kb-row-layout-wrap.kb-row-layout-id15335_b17978-da.alignnone.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-1-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_6597cd-c5 > div.kt-inside-inner-col > h2.kt-adv-heading15335_99b136-f9.wp-block-kadence-advancedheading", "text": {"text": "Built With Your Care Teams in Mind", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 44, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.kb-row-layout-wrap.kb-row-layout-id15335_8f7c1d-36.alignnone.has-theme-palette8-background-color.kt-row-has-bg.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_db4e9d-bf > div.kt-inside-inner-col > p.kt-adv-heading15335_fb47f6-f5.wp-block-kadence-advancedheading", "text": {"text": "PHYSICIANS", "color": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 3.01, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(92, 159, 169)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-inside-inner-col > div.kb-row-layout-wrap.kb-row-layout-id15335_8f7c1d-36.alignnone.has-theme-palette8-background-color.kt-row-has-bg.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_db4e9d-bf > div.kt-inside-inner-col > h4.kt-adv-heading15335_21f0f1-46.wp-block-kadence-advancedheading", "text": {"text": "Empower Collaboration", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 24, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_db4e9d-bf > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_cd8668-0e > a.kb-button.kt-button.button.kb-btn15335_fd4e87-9f.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Read More", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.kb-row-layout-wrap.kb-row-layout-id15335_8f7c1d-36.alignnone.has-theme-palette8-background-color.kt-row-has-bg.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_7573a1-93 > div.kt-inside-inner-col > p.kt-adv-heading15335_714d5b-19.wp-block-kadence-advancedheading", "text": {"text": "IT TEAMS", "color": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 3.01, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(92, 159, 169)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-inside-inner-col > div.kb-row-layout-wrap.kb-row-layout-id15335_8f7c1d-36.alignnone.has-theme-palette8-background-color.kt-row-has-bg.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_7573a1-93 > div.kt-inside-inner-col > h4.kt-adv-heading15335_e70d49-d4.wp-block-kadence-advancedheading", "text": {"text": "Consolidate Your Technology", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 24, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_7573a1-93 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_a32546-b5 > a.kb-button.kt-button.button.kb-btn15335_ae35a4-06.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Read More", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.kb-row-layout-wrap.kb-row-layout-id15335_8f7c1d-36.alignnone.has-theme-palette8-background-color.kt-row-has-bg.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_b77a8f-16 > div.kt-inside-inner-col > p.kt-adv-heading15335_e53e30-56.wp-block-kadence-advancedheading", "text": {"text": "NURSE TEAMS", "color": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 3.01, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(92, 159, 169)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-inside-inner-col > div.kb-row-layout-wrap.kb-row-layout-id15335_8f7c1d-36.alignnone.has-theme-palette8-background-color.kt-row-has-bg.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_b77a8f-16 > div.kt-inside-inner-col > h4.kt-adv-heading15335_54f200-c7.wp-block-kadence-advancedheading", "text": {"text": "Minimize Administrative Tasks", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 24, "fontWeight": "500", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_b77a8f-16 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_ec83eb-50 > a.kb-button.kt-button.button.kb-btn15335_a005c1-2e.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Read More", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.kb-row-layout-wrap.kb-row-layout-id15335_8f7c1d-36.alignnone.has-theme-palette8-background-color.kt-row-has-bg.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_1e6dc7-f2 > div.kt-inside-inner-col > p.kt-adv-heading15335_610818-d6.wp-block-kadence-advancedheading", "text": {"text": "EXECUTIVES", "color": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 3.01, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(92, 159, 169)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-inside-inner-col > div.kb-row-layout-wrap.kb-row-layout-id15335_8f7c1d-36.alignnone.has-theme-palette8-background-color.kt-row-has-bg.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_1e6dc7-f2 > div.kt-inside-inner-col > h4.kt-adv-heading15335_a41191-b0.wp-block-kadence-advancedheading", "text": {"text": "Increase Efficiency", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 24, "fontWeight": "700", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.kb-row-layout-wrap.kb-row-layout-id15335_8f7c1d-36.alignnone.has-theme-palette8-background-color.kt-row-has-bg.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_1e6dc7-f2 > div.kt-inside-inner-col > h4.kt-adv-heading15335_a41191-b0.wp-block-kadence-advancedheading", "text": {"text": "Increase Efficiency", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 24, "fontWeight": "700", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-4-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_1e6dc7-f2 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_25fd2e-41 > a.kb-button.kt-button.button.kb-btn15335_57c281-41.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Read More", "color": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}, "fontSize": 14.4, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 234, "g": 0, "b": 41, "a": 1, "hex": "#ea0029", "hsl": {"h": 349.48717948717945, "s": 100, "l": 45.88235294117647}, "luminance": 0.17652520586673245}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 4.64, "passes": {"aa": true, "aaa": false, "aaLarge": true, "aaaLarge": true}, "recommendation": "Consider increasing contrast ratio to 7:1 for AAA compliance", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(234, 0, 41)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AA", "improvements": ["Consider increasing contrast for AAA compliance", "Test with users who have visual impairments", "Verify that text effects do not interfere with readability"]}}, {"element": "div.entry-content.single-content > div.kb-row-layout-wrap.kb-row-layout-id15335_66a53f-25.alignnone.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-1-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_f2d02e-f0 > div.kt-inside-inner-col > h2.kt-adv-heading15335_62f142-8a.wp-block-kadence-advancedheading", "text": {"text": "Trusted Partner in Unified Healthcare Communication", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 44, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-2-columns.kt-row-layout-right-golden.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_428883-a4 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_a169c7-c1 > div.kt-inside-inner-col > p.kt-adv-heading15335_d5f7d9-db.wp-block-kadence-advancedheading.hls-hover_underline", "text": {"text": "RAVE Reviews by", "color": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 3.01, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(92, 159, 169)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-row-column-wrap.kt-has-2-columns.kt-row-layout-right-golden.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_428883-a4 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_a169c7-c1 > div.kt-inside-inner-col > h3.kt-adv-heading15335_ff9b3d-7f.wp-block-kadence-advancedheading", "text": {"text": "Real Customers", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 28, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.splide__list > div.wp-block-kadence-slide.kb-advanced-slide-item.kb-slide-15335_5cb19e-ca > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_886eb1-39.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color", "text": {"text": "“TigerConnect Clinical Collaboration Platform in general is very easy to use. […] Going from what we had before, which was managing manual spreadsheets and sending faxes and emails to groups, allowed us to make updates on the fly. It also gave the ability for the clinical staff to get that data timely versus the old process. TigerConnect Clinical Collaboration Platform was a huge improvement for us.”", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 20, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 63, "g": 167, "b": 198, "a": 1, "hex": "#3fa7c6", "hsl": {"h": 193.77777777777774, "s": 54.21686746987952, "l": 51.17647058823529}, "luminance": 0.3277141176846188}, "confidence": 0.5}, "contrast": {"ratio": 2.78, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 3:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.splide__list > div.wp-block-kadence-slide.kb-advanced-slide-item.kb-slide-15335_5cb19e-ca > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_f0b6a3-dd.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color", "text": {"text": "Director", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 63, "g": 167, "b": 198, "a": 1, "hex": "#3fa7c6", "hsl": {"h": 193.77777777777774, "s": 54.21686746987952, "l": 51.17647058823529}, "luminance": 0.3277141176846188}, "confidence": 0.5}, "contrast": {"ratio": 2.78, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.splide__list > div.wp-block-kadence-slide.kb-advanced-slide-item.kb-slide-15335_5cb19e-ca > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_24f2f1-2c.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color.hls-none", "text": {"text": "KLAS Research, October 2024", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 63, "g": 167, "b": 198, "a": 1, "hex": "#3fa7c6", "hsl": {"h": 193.77777777777774, "s": 54.21686746987952, "l": 51.17647058823529}, "luminance": 0.3277141176846188}, "confidence": 0.5}, "contrast": {"ratio": 2.78, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.splide__list > div.wp-block-kadence-slide.kb-advanced-slide-item.kb-slide-15335_474923-0d > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_1286f6-2d.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color", "text": {"text": "“TigerConnect provides metrics so that we can monitor our utilization, and that is highly valuable. We recently deployed a functionality of roles, and that was very flexible. The vendor also has a messaging interface that we hope to integrate with our EHR so that we can send messages out of our EHR. Those functionalities are awesome.”", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 20, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 63, "g": 167, "b": 198, "a": 1, "hex": "#3fa7c6", "hsl": {"h": 193.77777777777774, "s": 54.21686746987952, "l": 51.17647058823529}, "luminance": 0.3277141176846188}, "confidence": 0.5}, "contrast": {"ratio": 2.78, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 3:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.splide__list > div.wp-block-kadence-slide.kb-advanced-slide-item.kb-slide-15335_474923-0d > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_2ba86a-5c.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color", "text": {"text": "Director", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 63, "g": 167, "b": 198, "a": 1, "hex": "#3fa7c6", "hsl": {"h": 193.77777777777774, "s": 54.21686746987952, "l": 51.17647058823529}, "luminance": 0.3277141176846188}, "confidence": 0.5}, "contrast": {"ratio": 2.78, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.splide__list > div.wp-block-kadence-slide.kb-advanced-slide-item.kb-slide-15335_474923-0d > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_a06166-ed.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color.hls-none", "text": {"text": "KLAS Research, September 2024", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 63, "g": 167, "b": 198, "a": 1, "hex": "#3fa7c6", "hsl": {"h": 193.77777777777774, "s": 54.21686746987952, "l": 51.17647058823529}, "luminance": 0.3277141176846188}, "confidence": 0.5}, "contrast": {"ratio": 2.78, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.splide__list > div.wp-block-kadence-slide.kb-advanced-slide-item.kb-slide-15335_647e27-ad > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_8fb36f-e9.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color", "text": {"text": "“One of the reasons TigerConnect was selected was that they have an ecosystem for middleware, alarm management, and things like that. I did not want to look at something that was only a messaging system. I wanted an ecosystem where things were integrated rather than bolted on.”", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 20, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 63, "g": 167, "b": 198, "a": 1, "hex": "#3fa7c6", "hsl": {"h": 193.77777777777774, "s": 54.21686746987952, "l": 51.17647058823529}, "luminance": 0.3277141176846188}, "confidence": 0.5}, "contrast": {"ratio": 2.78, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 3:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.splide__list > div.wp-block-kadence-slide.kb-advanced-slide-item.kb-slide-15335_647e27-ad > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_be5d64-75.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color", "text": {"text": "CIO", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 63, "g": 167, "b": 198, "a": 1, "hex": "#3fa7c6", "hsl": {"h": 193.77777777777774, "s": 54.21686746987952, "l": 51.17647058823529}, "luminance": 0.3277141176846188}, "confidence": 0.5}, "contrast": {"ratio": 2.78, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.splide__list > div.wp-block-kadence-slide.kb-advanced-slide-item.kb-slide-15335_647e27-ad > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_b7ce17-2d.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color.hls-none", "text": {"text": "KLAS Research, October 2024", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 63, "g": 167, "b": 198, "a": 1, "hex": "#3fa7c6", "hsl": {"h": 193.77777777777774, "s": 54.21686746987952, "l": 51.17647058823529}, "luminance": 0.3277141176846188}, "confidence": 0.5}, "contrast": {"ratio": 2.78, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.splide__list > div.wp-block-kadence-slide.kb-advanced-slide-item.kb-slide-15335_cdf375-e4 > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_32d850-01.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color", "text": {"text": "“I am an extremely happy TigerConnect customer. I would give the ease of use a rating above the scale if I could. I would rate my ability to receive my money’s worth higher than the top of the scale if I could. Compared to our previous product, we actually paid less for TigerConnect Clinical Collaboration Platform.”", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 20, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 63, "g": 167, "b": 198, "a": 1, "hex": "#3fa7c6", "hsl": {"h": 193.77777777777774, "s": 54.21686746987952, "l": 51.17647058823529}, "luminance": 0.3277141176846188}, "confidence": 0.5}, "contrast": {"ratio": 2.78, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 3:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.splide__list > div.wp-block-kadence-slide.kb-advanced-slide-item.kb-slide-15335_cdf375-e4 > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_eda79f-32.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color", "text": {"text": "CIO", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 63, "g": 167, "b": 198, "a": 1, "hex": "#3fa7c6", "hsl": {"h": 193.77777777777774, "s": 54.21686746987952, "l": 51.17647058823529}, "luminance": 0.3277141176846188}, "confidence": 0.5}, "contrast": {"ratio": 2.78, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.splide__list > div.wp-block-kadence-slide.kb-advanced-slide-item.kb-slide-15335_cdf375-e4 > div.kb-advanced-slide > div.kb-advanced-slide-inner-wrap > div.kb-advanced-slide-inner > h2.kt-adv-heading15335_8324a9-77.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color.hls-none", "text": {"text": "KLAS Research, October 2024", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 63, "g": 167, "b": 198, "a": 1, "hex": "#3fa7c6", "hsl": {"h": 193.77777777777774, "s": 54.21686746987952, "l": 51.17647058823529}, "luminance": 0.3277141176846188}, "confidence": 0.5}, "contrast": {"ratio": 2.78, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-row-column-wrap.kt-has-2-columns.kt-row-layout-right-golden.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_78d048-64.kb-section-dir-vertical > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_b21d59-71 > div.kt-inside-inner-col > p.kt-adv-heading15335_af6b6c-0f.wp-block-kadence-advancedheading", "text": {"text": "TOP RATED", "color": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 3.01, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(92, 159, 169)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-row-column-wrap.kt-has-2-columns.kt-row-layout-right-golden.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_78d048-64.kb-section-dir-vertical > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_b21d59-71 > div.kt-inside-inner-col > h3.kt-adv-heading15335_ab50f0-32.wp-block-kadence-advancedheading", "text": {"text": "Clinical Communication & Collaboration Solution", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 28, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_c867b4-54 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title > strong", "text": {"text": "2024 Best in KLAS Winner", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 25, "fontWeight": "700", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_b21d59-71 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_c867b4-54 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-text", "text": {"text": "for Clinical Communications in Ambulatory/Post-Acute Care​", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_c867b4-54 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > div.kt-blocks-info-box-learnmore-wrap > span.kt-blocks-info-box-learnmore", "text": {"text": "Read Verified Reviews >", "color": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 3.01, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(92, 159, 169)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_a586cb-ee > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title > strong", "text": {"text": "#1 Leader in Winter 2025 G2 Rankings", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 25, "fontWeight": "700", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_b21d59-71 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_a586cb-ee > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-text", "text": {"text": "for HIPAA-Compliant Messaging, Clinical Communication & Collaboration, and Medical Staff Scheduling​", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_a586cb-ee > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > div.kt-blocks-info-box-learnmore-wrap > span.kt-blocks-info-box-learnmore", "text": {"text": "Read Verified Reviews >", "color": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 3.01, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(92, 159, 169)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_dbae68-0c > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title > strong", "text": {"text": "Named as a Leader and Placed Highest for Ability to Execute", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 25, "fontWeight": "700", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_b21d59-71 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_dbae68-0c > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-text", "text": {"text": "in the 2024 Gartner® Magic Quadrant™ for Clinical Communication and Collaboration", "color": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 84, "g": 87, "b": 89, "a": 1, "hex": "#545759", "hsl": {"h": 203.99999999999994, "s": 2.890173410404622, "l": 33.92156862745098}, "luminance": 0.0942247659582629}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 7.28, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(84, 87, 89)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_dbae68-0c > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > div.kt-blocks-info-box-learnmore-wrap > span.kt-blocks-info-box-learnmore", "text": {"text": "Read the Report >", "color": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 92, "g": 159, "b": 169, "a": 1, "hex": "#5c9fa9", "hsl": {"h": 187.79220779220776, "s": 30.923694779116463, "l": 51.17647058823529}, "luminance": 0.2993615804566871}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 3.01, "passes": {"aa": false, "aaa": false, "aaLarge": true, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(92, 159, 169)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.entry-content.single-content > div.kb-row-layout-wrap.kb-row-layout-id15335_89d1fd-36.alignnone.kb-v-sm-hidden.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-1-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_de65c4-4d > div.kt-inside-inner-col > h2.kt-adv-heading15335_a691f3-a8.wp-block-kadence-advancedheading", "text": {"text": "Resources", "color": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}, "fontSize": 44, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 51, "g": 51, "b": 51, "a": 1, "hex": "#333333", "hsl": {"h": 0, "s": 0, "l": 20}, "luminance": 0.033104766570885055}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 12.63, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(51, 51, 51)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-2-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_dbf649-90.kb-section-dir-vertical.Resources-1 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_da15c6-4c.kb-section-dir-horizontal > div.kt-inside-inner-col > p.kt-adv-heading15335_8c301e-c4.wp-block-kadence-advancedheading", "text": {"text": "RESOURCES", "color": {"r": 110, "g": 184, "b": 194, "a": 1, "hex": "#6eb8c2", "hsl": {"h": 187.14285714285717, "s": 40.776699029126206, "l": 59.6078431372549}, "luminance": 0.4149101802446695}, "fontSize": 14, "fontWeight": "500", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 110, "g": 184, "b": 194, "a": 1, "hex": "#6eb8c2", "hsl": {"h": 187.14285714285717, "s": 40.776699029126206, "l": 59.6078431372549}, "luminance": 0.4149101802446695}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 2.26, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(110, 184, 194)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-row-column-wrap.kt-has-2-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_dbf649-90.kb-section-dir-vertical.Resources-1 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_da15c6-4c.kb-section-dir-horizontal > div.kt-inside-inner-col > h2.kt-adv-heading15335_16577e-ed.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color", "text": {"text": "Explore Industry Insights & Reports", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 32, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 1, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 3:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_61075e-17 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_7649ab-29 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "eBooks", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 1, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_61075e-17 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_0ea936-96 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Webinars", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 1, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_61075e-17 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_a7ff56-a3 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Infographics", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 1, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_12fc5c-3d.kb-section-dir-horizontal > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_1d907b-bb > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Case Studies", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 1, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_12fc5c-3d.kb-section-dir-horizontal > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_558ac8-0d > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Product Tours", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 1, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.wp-block-kadence-column.kadence-column15335_12fc5c-3d.kb-section-dir-horizontal > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box15335_ceeb5a-ad > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Blogs", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 1, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-row-column-wrap.kt-has-2-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kt-inner-column-height-full.kb-theme-content-width > div.wp-block-kadence-column.kadence-column15335_dbf649-90.kb-section-dir-vertical.Resources-1 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_48db7c-34 > a.kb-button.kt-button.button.kb-btn15335_2e8906-e0.kt-btn-size-standard.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Explore All Resources", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 1, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_18fe5a-77.kb-section-has-link.kb-section-dir-horizontal > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_abd83e-4b > div.kt-inside-inner-col > h2.kt-adv-heading15335_20123a-da.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color", "text": {"text": "State of Clinical Communications & Workflows", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 26, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 1, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 3:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_abd83e-4b > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_550bd9-91 > span.kb-button.kt-button.button.kb-btn15335_77fba0-ec.kt-btn-size-standard.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Read the Full Report", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 1, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_e470f6-09.kb-section-has-link.kb-section-dir-horizontal > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_210d5b-5d > div.kt-inside-inner-col > h2.kt-adv-heading15335_f868b2-10.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color", "text": {"text": "2024 Gartner Magic Quadrant", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 26, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 1, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 3:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_e470f6-09.kb-section-has-link.kb-section-dir-horizontal > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_210d5b-5d > div.kt-inside-inner-col > p.kt-adv-heading15335_5bd321-d6.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color", "text": {"text": "Clinical Communication & Collaboration", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 18, "fontWeight": "300", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 1, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 3:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column15335_210d5b-5d > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns15335_e29554-ba > a.kb-button.kt-button.button.kb-btn15335_9575d8-06.kt-btn-size-standard.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-true.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Read the Full Report", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "confidence": 0.5}, "contrast": {"ratio": 1, "passes": {"aa": false, "aaa": false, "aaLarge": false, "aaaLarge": false}, "recommendation": "Increase contrast ratio to at least 4.5:1 for AA compliance", "confidence": 0.55, "issues": ["Does not meet WCAG AA contrast requirements"]}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": false, "level": "fail", "improvements": ["Increase color contrast to meet WCAG AA standards", "Consider using darker text or lighter background colors"]}}, {"element": "div.kt-row-column-wrap.kt-has-5-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column22_ea6058-07 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column22_879323-31.kb-section-dir-horizontal > div.kt-inside-inner-col > h2.kt-adv-heading22_13bafe-ed.wp-block-kadence-advancedheading", "text": {"text": "Sales:", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column22_879323-31.kb-section-dir-horizontal > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box22_9e9d94-53 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "text": {"text": "(*************", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column22_879323-31.kb-section-dir-horizontal > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box22_e6603a-7c > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "text": {"text": "Email", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-5-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column22_ea6058-07 > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column22_44aec1-1a.kb-section-dir-horizontal > div.kt-inside-inner-col > h2.kt-adv-heading22_c8dd38-08.wp-block-kadence-advancedheading", "text": {"text": "Support:", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column22_44aec1-1a.kb-section-dir-horizontal > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box22_2fabc2-28 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > h2.kt-blocks-info-box-title", "text": {"text": "Email", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.site.wp-site-blocks > div.kb-row-layout-wrap.kb-row-layout-id22_75c14e-34.alignnone.kt-row-has-bg.wp-block-kadence-rowlayout > div.kt-row-column-wrap.kt-has-5-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column22_ea6058-07 > div.kt-inside-inner-col > p.kt-adv-heading22_92ad8b-c1.wp-block-kadence-advancedheading.has-theme-palette-9-color.has-text-color", "text": {"text": "Since 2010 TigerConnect has been transforming healthcare communications by enabling our customers to automate clinical workflows, speed decisions, and improve patient outcomes. Today our cloud-native Clinical Communication and Collaboration (CC&C) solutions are used by over 7,000 healthcare organizations and 700,000 care team members.​", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column22_db91a0-9a > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box22_cf8e2d-85 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "What We Do", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "700", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_e867e5-09.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_402f36-2d > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Clinical Collaboration", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_e867e5-09.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_231b40-61 > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Alarm Management and Event Notification", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_e867e5-09.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_4db8c5-ae > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Patient Engagement", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_e867e5-09.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_c71067-c4 > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Physician <PERSON>", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_e867e5-09.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_08a8eb-74 > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "CareConduit", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_e867e5-09.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_6e0c63-44 > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Workflows", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column22_064b56-12 > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box22_1bb9f8-b3 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Who We Serve", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "700", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_51abb9-00.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_571f4c-5c > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Healthcare Organizations", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_51abb9-00.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_81e4bf-16 > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Healthcare Professionals", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_51abb9-00.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_e802de-20 > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Physicians", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_51abb9-00.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_70d4b4-1b > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Nurses", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_51abb9-00.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_0d38ae-02 > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Executives", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_51abb9-00.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_917ab0-de > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "IT", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column22_23bb01-da > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box22_ee5809-28 > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "About Us", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "700", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_1137b1-8a.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_dda367-bf > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Why TigerConnect?", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_1137b1-8a.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_8d3471-15 > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Careers", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_1137b1-8a.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_42942a-d4 > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Leadership", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_1137b1-8a.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_453fb0-5d > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Media Coverage", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_1137b1-8a.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_49d665-5b > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Newsroom", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_1137b1-8a.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_d15cfe-9f > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Partners", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.wp-block-kadence-column.kadence-column22_77e82c-5c > div.kt-inside-inner-col > div.wp-block-kadence-infobox.kt-info-box22_8ca3f5-3a > a.kt-blocks-info-box-link-wrap.info-box-link.kt-blocks-info-box-media-align-left.kt-info-halign-left > div.kt-infobox-textcontent > p.kt-blocks-info-box-title", "text": {"text": "Resources", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 16, "fontWeight": "700", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_88b0ba-16.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_488aaa-b4 > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "App Download", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_88b0ba-16.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_67dc8e-a4 > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Blog Articles", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_88b0ba-16.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_ef0b10-ca > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Case Studies", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_88b0ba-16.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_11312d-d4 > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Demo Tours", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_88b0ba-16.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_246c39-0b > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Ebooks", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-iconlist.kt-svg-icon-list-items.kt-svg-icon-list-items22_88b0ba-16.kt-svg-icon-list-columns-1.alignnone.kt-list-icon-aligntop > ul.kt-svg-icon-list > li.wp-block-kadence-listitem.kt-svg-icon-list-item-wrap.kt-svg-icon-list-item-22_0ac4b9-52 > a.kt-svg-icon-link > span.kt-svg-icon-list-text", "text": {"text": "Webinars", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-row-column-wrap.kt-has-1-columns.kt-row-layout-equal.kt-tab-layout-inherit.kt-mobile-layout-row.kt-row-valign-top.kb-theme-content-width > div.wp-block-kadence-column.kadence-column22_fc7865-a6.kb-section-dir-horizontal.kb-section-sm-dir-vertical > div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column22_f964da-d6 > div.kt-inside-inner-col > p.kt-adv-heading22_20396d-6a.wp-block-kadence-advancedheading", "text": {"text": "©2025 TigerConnect. All Rights Reserved.", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 14, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column22_f964da-d6 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns22_165ce3-7e > a.kb-button.kt-button.button.kb-btn22_371904-91.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-false.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Sitemap", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 12, "fontWeight": "100", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column22_f964da-d6 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns22_165ce3-7e > a.kb-button.kt-button.button.kb-btn22_2adae4-fe.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-false.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Accessibility", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 12, "fontWeight": "100", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column22_f964da-d6 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns22_165ce3-7e > a.kb-button.kt-button.button.kb-btn22_a7b35b-ae.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-false.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Legal", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 12, "fontWeight": "100", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.kt-inside-inner-col > div.wp-block-kadence-column.kadence-column22_f964da-d6 > div.kt-inside-inner-col > div.wp-block-kadence-advancedbtn.kb-buttons-wrap.kb-btns22_165ce3-7e > a.kb-button.kt-button.button.kb-btn22_8e8c07-24.kt-btn-size-small.kt-btn-width-type-auto.kb-btn-global-fill.kt-btn-has-text-true.kt-btn-has-svg-false.wp-block-kadence-singlebtn > span.kt-btn-inner-text", "text": {"text": "Privacy", "color": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}, "fontSize": 12, "fontWeight": "100", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 255, "g": 255, "b": 255, "a": 1, "hex": "#ffffff", "hsl": {"h": 0, "s": 0, "l": 100}, "luminance": 1}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 13, "g": 38, "b": 67, "a": 1, "hex": "#0d2643", "hsl": {"h": 212.22222222222226, "s": 67.5, "l": 15.686274509803921}, "luminance": 0.01877039637611387}, "confidence": 0.5}, "contrast": {"ratio": 15.27, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(255, 255, 255)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.site-header-item.site-header-focus-item.site-header-item-mobile-navigation.mobile-navigation-layout-stretch-false > nav.mobile-navigation.drawer-navigation.drawer-navigation-parent-toggle-false > div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-home.current-menu-item.page_item.page-item-15335.current_page_item.menu-item-17238 > a", "text": {"text": "Home", "color": {"r": 121, "g": 197, "b": 183, "a": 1, "hex": "#79c5b7", "hsl": {"h": 168.94736842105263, "s": 39.583333333333336, "l": 62.35294117647059}, "luminance": 0.4741634984390069}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 121, "g": 197, "b": 183, "a": 1, "hex": "#79c5b7", "hsl": {"h": 168.94736842105263, "s": 39.583333333333336, "l": 62.35294117647059}, "luminance": 0.4741634984390069}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 9.78, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(121, 197, 183)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "nav.mobile-navigation.drawer-navigation.drawer-navigation-parent-toggle-false > div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11355 > div.drawer-nav-drop-wrap > a", "text": {"text": "Solutions", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11355 > div.drawer-nav-drop-wrap > button.drawer-sub-toggle > span.screen-reader-text", "text": {"text": "Toggle child menu", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 18, "fontWeight": "400", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11355 > div.drawer-nav-drop-wrap > button.drawer-sub-toggle > span.kadence-svg-iconset > svg > title", "text": {"text": "Expand", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 18, "fontWeight": "400", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11355 > ul.sub-menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-21731 > a", "text": {"text": "Pre-Hospital", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11355 > ul.sub-menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-11357 > a", "text": {"text": "Physician <PERSON>", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11355 > ul.sub-menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-18621 > a", "text": {"text": "Clinical Collaboration", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11355 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11328 > a", "text": {"text": "Alarm Management & Event Notification", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11355 > ul.sub-menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-11356 > a", "text": {"text": "Patient Engagement", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11355 > ul.sub-menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-22418 > a", "text": {"text": "CareConduit", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "nav.mobile-navigation.drawer-navigation.drawer-navigation-parent-toggle-false > div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11351 > div.drawer-nav-drop-wrap > a", "text": {"text": "Workflows", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11351 > div.drawer-nav-drop-wrap > button.drawer-sub-toggle > span.screen-reader-text", "text": {"text": "Toggle child menu", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 18, "fontWeight": "400", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11351 > div.drawer-nav-drop-wrap > button.drawer-sub-toggle > span.kadence-svg-iconset > svg > title", "text": {"text": "Expand", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 18, "fontWeight": "400", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11351 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11346 > a", "text": {"text": "Critical Response", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11351 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11347 > a", "text": {"text": "Emergency Department Workflows", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11351 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11348 > a", "text": {"text": "Inpatient Workflows", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11351 > ul.sub-menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-11358 > a", "text": {"text": "Operating Room Workflows", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11351 > ul.sub-menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-11359 > a", "text": {"text": "Post Acute & Ambulatory Workflows", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "nav.mobile-navigation.drawer-navigation.drawer-navigation-parent-toggle-false > div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11300 > div.drawer-nav-drop-wrap > a", "text": {"text": "Organizations", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11300 > div.drawer-nav-drop-wrap > button.drawer-sub-toggle > span.screen-reader-text", "text": {"text": "Toggle child menu", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 18, "fontWeight": "400", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11300 > div.drawer-nav-drop-wrap > button.drawer-sub-toggle > span.kadence-svg-iconset > svg > title", "text": {"text": "Expand", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 18, "fontWeight": "400", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11300 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11302 > a", "text": {"text": "Ambulatory Surgery Centers", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11300 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11303 > a", "text": {"text": "Behavioral Health", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11300 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11305 > a", "text": {"text": "Health Systems", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11300 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11306 > a", "text": {"text": "Home Health & Hospice", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11300 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11307 > a", "text": {"text": "Hospitals", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11300 > ul.sub-menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-11353 > a", "text": {"text": "Physician Groups", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11300 > ul.sub-menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-11354 > a", "text": {"text": "Skilled Nursing Facilities", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "nav.mobile-navigation.drawer-navigation.drawer-navigation-parent-toggle-false > div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11308 > div.drawer-nav-drop-wrap > a", "text": {"text": "Professionals", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11308 > div.drawer-nav-drop-wrap > button.drawer-sub-toggle > span.screen-reader-text", "text": {"text": "Toggle child menu", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 18, "fontWeight": "400", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11308 > div.drawer-nav-drop-wrap > button.drawer-sub-toggle > span.kadence-svg-iconset > svg > title", "text": {"text": "Expand", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 18, "fontWeight": "400", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11308 > ul.sub-menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-11352 > a", "text": {"text": "Physicians", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11308 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11311 > a", "text": {"text": "Nurses", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11308 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11309 > a", "text": {"text": "Executives", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-has-children.menu-item-11308 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11310 > a", "text": {"text": "IT", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "nav.mobile-navigation.drawer-navigation.drawer-navigation-parent-toggle-false > div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11349 > div.drawer-nav-drop-wrap > a", "text": {"text": "Resources", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11349 > div.drawer-nav-drop-wrap > button.drawer-sub-toggle > span.screen-reader-text", "text": {"text": "Toggle child menu", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 18, "fontWeight": "400", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11349 > div.drawer-nav-drop-wrap > button.drawer-sub-toggle > span.kadence-svg-iconset > svg > title", "text": {"text": "Expand", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 18, "fontWeight": "400", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11349 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11330 > a", "text": {"text": "Articles", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11349 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11331 > a", "text": {"text": "Blog", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11349 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11332 > a", "text": {"text": "Case Studies", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11349 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11333 > a", "text": {"text": "Checklists", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11349 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11334 > a", "text": {"text": "Datasheets", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11349 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11335 > a", "text": {"text": "eBooks", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11349 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11336 > a", "text": {"text": "Events", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11349 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11337 > a", "text": {"text": "Guides", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11349 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11338 > a", "text": {"text": "Infographics", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "nav.mobile-navigation.drawer-navigation.drawer-navigation-parent-toggle-false > div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11350 > div.drawer-nav-drop-wrap > a", "text": {"text": "Why TigerConnect", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11350 > div.drawer-nav-drop-wrap > button.drawer-sub-toggle > span.screen-reader-text", "text": {"text": "Toggle child menu", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 18, "fontWeight": "400", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11350 > div.drawer-nav-drop-wrap > button.drawer-sub-toggle > span.kadence-svg-iconset > svg > title", "text": {"text": "Expand", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 18, "fontWeight": "400", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11350 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11339 > a", "text": {"text": "Careers", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11350 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11340 > a", "text": {"text": "Contact Us", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11350 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11342 > a", "text": {"text": "Leadership", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11350 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11343 > a", "text": {"text": "Media Coverage", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11350 > ul.sub-menu > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11341 > a", "text": {"text": "Partners", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-has-children.menu-item-11350 > ul.sub-menu > li.menu-item.menu-item-type-custom.menu-item-object-custom.menu-item-11360 > a", "text": {"text": "Contact Us", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.site-header-item.site-header-focus-item.site-header-item-mobile-navigation.mobile-navigation-layout-stretch-false > nav.mobile-navigation.drawer-navigation.drawer-navigation-parent-toggle-false > div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11298 > a", "text": {"text": "Download Our App", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "div.site-header-item.site-header-focus-item.site-header-item-mobile-navigation.mobile-navigation-layout-stretch-false > nav.mobile-navigation.drawer-navigation.drawer-navigation-parent-toggle-false > div.mobile-menu-container.drawer-menu-container > ul.menu.has-collapse-sub-nav > li.menu-item.menu-item-type-post_type.menu-item-object-page.menu-item-11299 > a", "text": {"text": "Get a Demo", "color": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}, "fontSize": 15, "fontWeight": "300", "isLarge": false, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 244, "g": 244, "b": 244, "a": 1, "hex": "#f4f4f4", "hsl": {"h": 0, "s": 0, "l": 95.68627450980392}, "luminance": 0.9046611743911496}}, "background": {"type": "solid", "primaryColor": {"r": 0, "g": 0, "b": 0, "a": 0, "hex": "#000000", "hsl": {"h": 0, "s": 0, "l": 0}, "luminance": 0}, "hasTransparency": true, "effectiveColor": {"r": 9, "g": 12, "b": 16, "a": 1, "hex": "#090c10", "hsl": {"h": 214.28571428571428, "s": 28.000000000000004, "l": 4.901960784313726}, "luminance": 0.0035843120743899027}, "confidence": 0.5}, "contrast": {"ratio": 17.82, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.55, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(244, 244, 244)", "backgroundColor": "", "background": "rgba(0, 0, 0, 0) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}], "timestamp": 1752230700244, "hash": "a3494e6e6ddc7c3f8be6272eae35f710", "accessCount": 1, "lastAccessed": 1752230700244, "size": 455733}
{"data": {"ruleId": "WCAG-037", "ruleName": "Resize Text", "category": "perceivable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0535, "automated": true, "evidence": [{"type": "info", "description": "Advanced text resize analysis with layout adaptation and contrast preservation", "element": "text-elements", "value": "{\"resizeAnalysis\":{\"textScaling\":{\"scalingFactors\":[1.25,1.5,1.75,2],\"issues\":[\"At 125% scaling: 158 text overlaps detected\",\"At 150% scaling: 144 text overlaps detected\",\"At 175% scaling: 138 text overlaps detected\",\"At 200% scaling: 131 text overlaps detected\"],\"recommendations\":[\"Implement responsive typography with relative units\",\"Test layout at 200% text scaling\"],\"complianceScore\":40},\"contentReflow\":{\"viewportSizes\":[{\"width\":320,\"height\":568,\"hasHorizontalScroll\":false,\"contentOverflow\":true,\"score\":80},{\"width\":768,\"height\":1024,\"hasHorizontalScroll\":false,\"contentOverflow\":true,\"score\":80},{\"width\":1024,\"height\":768,\"hasHorizontalScroll\":false,\"contentOverflow\":true,\"score\":80}],\"overallReflowScore\":80,\"criticalIssues\":[]},\"adaptiveFeatures\":{\"hasResponsiveImages\":false,\"hasFlexibleLayouts\":true,\"hasAdaptiveTypography\":false,\"hasAccessibleBreakpoints\":true,\"score\":50}},\"contrastAnalysis\":{\"wideGamutElements\":0,\"colorSpaceDistribution\":{\"srgb\":1792,\"p3\":0,\"rec2020\":0,\"oklch\":0,\"lch\":0,\"unknown\":0},\"p3Coverage\":0,\"rec2020Coverage\":0}}", "severity": "error", "elementCount": 1, "affectedSelectors": ["resizeAnalysis", "textScaling", "scalingFactors", "issues", "At", "scaling", "text", "overlaps", "detected", "recommendations", "Implement", "responsive", "typography", "with", "relative", "units", "Test", "layout", "at", "complianceScore", "contentReflow", "viewportSizes", "width", "height", "hasHorizontalScroll", "false", "contentOverflow", "true", "score", "overallReflowScore", "criticalIssues", "adaptiveFeatures", "hasResponsiveImages", "hasFlexibleLayouts", "hasAdaptiveTypography", "hasAccessibleBreakpoints", "contrastAnalysis", "wideGamutElements", "colorSpaceDistribution", "srgb", "p3", "rec2020", "oklch", "lch", "unknown", "p3Coverage", "rec2020Coverage"], "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"Scan your websites, servers, networks, and APIs.\"", "selector": "p:nth-of-type(14)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(14)", "Font", "px", "Text", "<PERSON><PERSON>", "your", "websites", "servers", "networks", "and", "APIs"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"View dashboards, get threat alerts, and generate audit-ready reports.\"", "selector": "p:nth-of-type(15)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(15)", "Font", "px", "Text", "View", "dashboards", "get", "threat", "alerts", "and", "generate", "audit-ready", "reports"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Absolute/fixed positioning may cause resize issues", "value": "Font: 16px, Text: \"<PERSON>an now\"", "selector": "button:nth-of-type(17)", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(17)", "Font", "px", "Text", "<PERSON><PERSON>", "now"], "severity": "info", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"OUR CUSTOMERS\"", "selector": "p:nth-of-type(18)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(18)", "Font", "px", "Text", "OUR", "CUSTOMERS"], "severity": "error", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"What we do\"", "selector": "p:nth-of-type(21)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(21)", "Font", "px", "Text", "What", "we", "do"], "severity": "error", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"HostedScan is simple and effective. Run a wide set of industry-leading tools to uncover vulnerabilit\"", "selector": "p:nth-of-type(22)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(22)", "Font", "px", "Text", "HostedScan", "is", "simple", "and", "effective", "Run", "a", "wide", "set", "of", "industry-leading", "tools", "to", "uncover", "vulnerabilit"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"PROVEN TOOLS\"", "selector": "p:nth-of-type(23)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(23)", "Font", "px", "Text", "PROVEN", "TOOLS"], "severity": "error", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 48px, Text: \"Level up your company's cybersecurity\"", "selector": "h2:nth-of-type(24)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(24)", "Font", "px", "Text", "Level", "up", "your", "company", "s", "cybersecurity"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 28px, Text: \"Meet compliance requirements\"", "selector": "h3:nth-of-type(26)", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(26)", "Font", "px", "Text", "Meet", "compliance", "requirements"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Vulnerability scanning is essential for your compliance with SOC 2, ISO 27001, cyber insurance, and \"", "selector": "p:nth-of-type(27)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(27)", "Font", "px", "Text", "Vulnerability", "scanning", "is", "essential", "for", "your", "compliance", "with", "SOC", "ISO", "cyber", "insurance", "and"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"With regulations such as GDPR and CCPA, failure to maintain reasonable security procedures is ground\"", "selector": "p:nth-of-type(30)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(30)", "Font", "px", "Text", "With", "regulations", "such", "as", "GDPR", "and", "CCPA", "failure", "to", "maintain", "reasonable", "security", "procedures", "is", "ground"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 28px, Text: \"Mitigate security vulnerabilities\"", "selector": "h3:nth-of-type(32)", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(32)", "Font", "px", "Text", "Mitigate", "security", "vulnerabilities"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Identify CVEs and OWASP Top 10 issues in your systems. Prioritize remediation using industry-standar\"", "selector": "p:nth-of-type(33)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(33)", "Font", "px", "Text", "Identify", "CVEs", "and", "OWASP", "Top", "issues", "in", "your", "systems", "Prioritize", "remediation", "using", "industry-standar"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 28px, Text: \"Detect misconfigurations\"", "selector": "h3:nth-of-type(35)", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(35)", "Font", "px", "Text", "Detect", "misconfigurations"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Many breaches and hacks happen not through highly sophisticated attacks, but by exploiting simple mi\"", "selector": "p:nth-of-type(36)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(36)", "Font", "px", "Text", "Many", "breaches", "and", "hacks", "happen", "not", "through", "highly", "sophisticated", "attacks", "but", "by", "exploiting", "simple", "mi"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px, Fixed dimensions may prevent text resizing", "value": "Font: 10px, Text: \"CUSTOM REPORTING ENGINE\"", "selector": "p:nth-of-type(40)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(40)", "Font", "px", "Text", "CUSTOM", "REPORTING", "ENGINE"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 48px, Text: \"Comprehensive reports, that always look good\"", "selector": "h2:nth-of-type(41)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(41)", "Font", "px", "Text", "Comprehensive", "reports", "that", "always", "look", "good"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"Generate polished, branded reports for your executives, clients, or auditors\"", "selector": "p:nth-of-type(42)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(42)", "Font", "px", "Text", "Generate", "polished", "branded", "reports", "for", "your", "executives", "clients", "or", "auditors"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"Communicate vulnerability risks\"", "selector": "p:nth-of-type(43)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(43)", "Font", "px", "Text", "Communicate", "vulnerability", "risks"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Get an executive PDF to share. See at a glance the vulnerabilities detected across all your targets \"", "selector": "p:nth-of-type(44)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(44)", "Font", "px", "Text", "Get", "an", "executive", "PDF", "to", "share", "See", "at", "a", "glance", "the", "vulnerabilities", "detected", "across", "all", "your", "targets"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"Export for your business intelligence\"", "selector": "p:nth-of-type(45)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(45)", "Font", "px", "Text", "Export", "for", "your", "business", "intelligence"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Use our built in reporting or export as CSV, JSON and XML to take into your BI tools, for full custo\"", "selector": "p:nth-of-type(46)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(46)", "Font", "px", "Text", "Use", "our", "built", "in", "reporting", "or", "export", "as", "CSV", "JSON", "and", "XML", "to", "take", "into", "your", "BI", "tools", "for", "full", "custo"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"White label reporting\"", "selector": "p:nth-of-type(47)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(47)", "Font", "px", "Text", "White", "label", "reporting"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Prepare custom white label reports for your clients. Make your logo and brand exclusive on the repor\"", "selector": "p:nth-of-type(48)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(48)", "Font", "px", "Text", "Prepare", "custom", "white", "label", "reports", "for", "your", "clients", "Make", "logo", "and", "brand", "exclusive", "on", "the", "repor"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px, Fixed dimensions may prevent text resizing", "value": "Font: 10px, Text: \"Scheduling & notifications\"", "selector": "p:nth-of-type(49)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(49)", "Font", "px", "Text", "Scheduling", "notifications"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 32px, Text: \"Always-on protection, without the noise\"", "selector": "p:nth-of-type(50)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(50)", "Font", "px", "Text", "Always-on", "protection", "without", "the", "noise"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"When a new port is open, or a new risk is detected, automatically alert your team. Cut out the noise\"", "selector": "p:nth-of-type(51)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(51)", "Font", "px", "Text", "When", "a", "new", "port", "is", "open", "or", "risk", "detected", "automatically", "alert", "your", "team", "Cut", "out", "the", "noise"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"REST API & WEBHOOKS\"", "selector": "p:nth-of-type(53)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(53)", "Font", "px", "Text", "REST", "API", "WEBHOOKS"], "severity": "error", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 32px, Text: \"Developer APIs + Webhooks\"", "selector": "p:nth-of-type(54)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(54)", "Font", "px", "Text", "Developer", "APIs", "Webhooks"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Add targets, run scans, and get results programmatically. Embed HostedScan into your own products an\"", "selector": "p:nth-of-type(55)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(55)", "Font", "px", "Text", "Add", "targets", "run", "scans", "and", "get", "results", "programmatically", "Embed", "HostedScan", "into", "your", "own", "products", "an"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"FROM OUR CUSTOMERS\"", "selector": "p:nth-of-type(57)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(57)", "Font", "px", "Text", "FROM", "OUR", "CUSTOMERS"], "severity": "error", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 48px, Text: \"What our customers are saying\"", "selector": "h2:nth-of-type(58)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(58)", "Font", "px", "Text", "What", "our", "customers", "are", "saying"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"“We highly recommend HostedScan as the definite choice for anyone seeking a reliable and efficient v\"", "selector": "p:nth-of-type(59)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(59)", "Font", "px", "Text", "We", "highly", "recommend", "HostedScan", "as", "the", "definite", "choice", "for", "anyone", "seeking", "a", "reliable", "and", "efficient", "v"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"“We use daily vulnerability testing from HostedScan to guarantee our security and support our ISO ce\"", "selector": "p:nth-of-type(63)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(63)", "Font", "px", "Text", "We", "use", "daily", "vulnerability", "testing", "from", "HostedScan", "to", "guarantee", "our", "security", "and", "support", "ISO", "ce"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"Technical Director at Principle Networks\"", "selector": "span:nth-of-type(66)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(66)", "Font", "px", "Text", "Technical", "Director", "at", "Principle", "Networks"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"“Every time I log in, it absolutely amazes me how much work the HostedScan team has gotten done on t\"", "selector": "p:nth-of-type(67)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(67)", "Font", "px", "Text", "Every", "time", "I", "log", "in", "it", "absolutely", "amazes", "me", "how", "much", "work", "the", "HostedScan", "team", "has", "gotten", "done", "on", "t"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"Operations and Security Manager at Ferguson Computer Services\"", "selector": "span:nth-of-type(70)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(70)", "Font", "px", "Text", "Operations", "and", "Security", "Manager", "at", "<PERSON>", "Computer", "Services"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"“We partnered with HostedScan to solve a critical need for our business: continuous, effective, and \"", "selector": "p:nth-of-type(71)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(71)", "Font", "px", "Text", "We", "partnered", "with", "HostedScan", "to", "solve", "a", "critical", "need", "for", "our", "business", "continuous", "effective", "and"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px, Fixed dimensions may prevent text resizing", "value": "Font: 10px, Text: \"YOUR ENTIRE ATTACK SURFACE\"", "selector": "p:nth-of-type(76)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(76)", "Font", "px", "Text", "YOUR", "ENTIRE", "ATTACK", "SURFACE"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 48px, Text: \"Leverage the industry's  most-trusted security tools\"", "selector": "h2:nth-of-type(77)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(77)", "Font", "px", "Text", "Leverage", "the", "industry", "s", "most-trusted", "security", "tools"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"HostedScan includes a comprehensive suite of industry-trusted vulnerability scanners.The advantage? \"", "selector": "p:nth-of-type(79)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(79)", "Font", "px", "Text", "HostedScan", "includes", "a", "comprehensive", "suite", "of", "industry-trusted", "vulnerability", "scanners.The", "advantage"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Find insecure software and scan for Common Vulnerabilities and Exposures (CVEs).\"", "selector": "p:nth-of-type(81)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(81)", "Font", "px", "Text", "Find", "insecure", "software", "and", "scan", "for", "Common", "Vulnerabilities", "Exposures", "CVEs"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Ensure your firewall and network are configured correctly with nothing unintentionally exposed.\"", "selector": "p:nth-of-type(84)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(84)", "Font", "px", "Text", "Ensure", "your", "firewall", "and", "network", "are", "configured", "correctly", "with", "nothing", "unintentionally", "exposed"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Focus on your web application, and detects front-end and API vulnerabilities.\"", "selector": "p:nth-of-type(87)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(87)", "Font", "px", "Text", "Focus", "on", "your", "web", "application", "and", "detects", "front-end", "API", "vulnerabilities"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.307Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Learn about OWASP Zap\"", "selector": "a:nth-of-type(88)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(88)", "Font", "px", "Text", "Learn", "about", "OWASP", "Zap"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Analyzes your SSL/TLS configuration and detects bad certificates, weak ciphers etc.\"", "selector": "p:nth-of-type(90)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(90)", "Font", "px", "Text", "Analyzes", "your", "SSL", "TLS", "configuration", "and", "detects", "bad", "certificates", "weak", "ciphers", "etc"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Manage Dependabot vulnerabilities in one place.\"", "selector": "p:nth-of-type(93)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(93)", "Font", "px", "Text", "Manage", "<PERSON>pen<PERSON><PERSON>", "vulnerabilities", "in", "one", "place"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Scan your APIs for potential weaknesses and security risks.\"", "selector": "p:nth-of-type(96)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(96)", "Font", "px", "Text", "<PERSON><PERSON>", "your", "APIs", "for", "potential", "weaknesses", "and", "security", "risks"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Centralize risks for your containers alongside your scans.\"", "selector": "p:nth-of-type(99)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(99)", "Font", "px", "Text", "Centralize", "risks", "for", "your", "containers", "alongside", "scans"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Bring in any third party risk source using our API.\"", "selector": "p:nth-of-type(102)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(102)", "Font", "px", "Text", "Bring", "in", "any", "third", "party", "risk", "source", "using", "our", "API"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"KNOWLEDGE HUB\"", "selector": "p:nth-of-type(104)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(104)", "Font", "px", "Text", "KNOWLEDGE", "HUB"], "severity": "error", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 48px, Text: \"Dive into HostedScan's Knowledge Hub\"", "selector": "h2:nth-of-type(105)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(105)", "Font", "px", "Text", "Dive", "into", "HostedScan", "s", "Knowledge", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"Updates\"", "selector": "span:nth-of-type(108)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(108)", "Font", "px", "Text", "Updates"], "severity": "error", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"What’s new at HostedScan - June 2025\"", "selector": "h4:nth-of-type(109)", "elementCount": 1, "affectedSelectors": ["h4:nth-of-type(109)", "Font", "px", "Text", "What", "s", "new", "at", "HostedScan", "June"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"Sc<PERSON>rs\"", "selector": "span:nth-of-type(110)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(110)", "Font", "px", "Text", "Scanners"], "severity": "error", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"Our Favorite Open-Source Vulnerability Scanning Tools\"", "selector": "h4:nth-of-type(111)", "elementCount": 1, "affectedSelectors": ["h4:nth-of-type(111)", "Font", "px", "Text", "Our", "Favorite", "Open-Source", "Vulnerability", "Scanning", "Tools"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"Cybersecurity\"", "selector": "span:nth-of-type(112)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(112)", "Font", "px", "Text", "Cybersecurity"], "severity": "error", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"Vulnerability Management SLAs: A Guide\"", "selector": "h4:nth-of-type(113)", "elementCount": 1, "affectedSelectors": ["h4:nth-of-type(113)", "Font", "px", "Text", "Vulnerability", "Management", "SLAs", "A", "Guide"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"Updates\"", "selector": "span:nth-of-type(114)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(114)", "Font", "px", "Text", "Updates"], "severity": "error", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"Upcoming Improvements for Q2 2024\"", "selector": "h4:nth-of-type(115)", "elementCount": 1, "affectedSelectors": ["h4:nth-of-type(115)", "Font", "px", "Text", "Upcoming", "Improvements", "for", "Q2"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"Cybersecurity\"", "selector": "span:nth-of-type(116)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(116)", "Font", "px", "Text", "Cybersecurity"], "severity": "error", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"Introducing HostedScan's Knowledge Hub and a couple of tips\"", "selector": "h4:nth-of-type(117)", "elementCount": 1, "affectedSelectors": ["h4:nth-of-type(117)", "Font", "px", "Text", "Introducing", "HostedScan", "s", "Knowledge", "<PERSON><PERSON>", "and", "a", "couple", "of", "tips"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 64px, Text: \"Become a more secure company today\"", "selector": "h5:nth-of-type(119)", "elementCount": 1, "affectedSelectors": ["h5:nth-of-type(119)", "Font", "px", "Text", "Become", "a", "more", "secure", "company", "today"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"HostedScan enables companies to meet compliance and security goals.\"", "selector": "p:nth-of-type(121)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(121)", "Font", "px", "Text", "HostedScan", "enables", "companies", "to", "meet", "compliance", "and", "security", "goals"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 64, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Our Scanners\"", "selector": "a:nth-of-type(125)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(125)", "Font", "px", "Text", "Our", "Scanners"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 65, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Pricing\"", "selector": "a:nth-of-type(126)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(126)", "Font", "px", "Text", "Pricing"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 66, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Documentation\"", "selector": "a:nth-of-type(127)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(127)", "Font", "px", "Text", "Documentation"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 67, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Qualys vs HostedScan\"", "selector": "a:nth-of-type(129)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(129)", "Font", "px", "Text", "Qualys", "vs", "HostedScan"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 68, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Nessus Tenable vs HostedScan\"", "selector": "a:nth-of-type(130)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(130)", "Font", "px", "Text", "<PERSON><PERSON><PERSON>", "Tenable", "vs", "HostedScan"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 69, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Netsparker vs HostedScan\"", "selector": "a:nth-of-type(131)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(131)", "Font", "px", "Text", "Netsparker", "vs", "HostedScan"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 70, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Detectify vs HostedScan\"", "selector": "a:nth-of-type(132)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(132)", "Font", "px", "Text", "Detectify", "vs", "HostedScan"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 71, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"OpenVAS - Network Scan\"", "selector": "a:nth-of-type(134)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(134)", "Font", "px", "Text", "OpenVAS", "Network", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 72, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Nmap - Port Scan\"", "selector": "a:nth-of-type(135)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(135)", "Font", "px", "Text", "Nmap", "Port", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 73, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"OWASP ZAP - Web Applications\"", "selector": "a:nth-of-type(136)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(136)", "Font", "px", "Text", "OWASP", "ZAP", "Web", "Applications"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 74, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"OWASP ZAP - API Security Scan\"", "selector": "a:nth-of-type(137)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(137)", "Font", "px", "Text", "OWASP", "ZAP", "API", "Security", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 75, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"SSLyze - TLS & SSL\"", "selector": "a:nth-of-type(138)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(138)", "Font", "px", "Text", "SSLyze", "TLS", "SSL"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 76, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Attack Surface Management (EASM)\"", "selector": "a:nth-of-type(140)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(140)", "Font", "px", "Text", "Attack", "Surface", "Management", "EASM"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 77, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Automated Penetration Testing\"", "selector": "a:nth-of-type(141)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(141)", "Font", "px", "Text", "Automated", "Penetration", "Testing"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 78, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Authenticated Web App Scanning\"", "selector": "a:nth-of-type(142)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(142)", "Font", "px", "Text", "Authenticated", "Web", "App", "Scanning"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 79, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"External Vulnerability Scan\"", "selector": "a:nth-of-type(143)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(143)", "Font", "px", "Text", "External", "Vulnerability", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 80, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Internal Vulnerability Scan\"", "selector": "a:nth-of-type(144)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(144)", "Font", "px", "Text", "Internal", "Vulnerability", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 81, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Wordpress Vulnerability Scan\"", "selector": "a:nth-of-type(146)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(146)", "Font", "px", "Text", "Wordpress", "Vulnerability", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 82, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Cloud Security\"", "selector": "a:nth-of-type(147)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(147)", "Font", "px", "Text", "Cloud", "Security"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 83, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Vulnerability Management\"", "selector": "a:nth-of-type(148)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(148)", "Font", "px", "Text", "Vulnerability", "Management"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 84, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"DAST Scanner\"", "selector": "a:nth-of-type(149)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(149)", "Font", "px", "Text", "DAST", "Scanner"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 85, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Online Port Scanner\"", "selector": "a:nth-of-type(150)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(150)", "Font", "px", "Text", "Online", "Port", "Scanner"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 86, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Online Vulnerability Scanner\"", "selector": "a:nth-of-type(151)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(151)", "Font", "px", "Text", "Online", "Vulnerability", "Scanner"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 87, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Continuous Security Monitoring\"", "selector": "a:nth-of-type(152)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(152)", "Font", "px", "Text", "Continuous", "Security", "Monitoring"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 88, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"DevSecOps Scanning\"", "selector": "a:nth-of-type(153)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(153)", "Font", "px", "Text", "DevSecOps", "Scanning"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 89, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Enterprise & Reseller\"", "selector": "a:nth-of-type(154)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(154)", "Font", "px", "Text", "Enterprise", "Reseller"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 90, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"MSPs & MSSPs\"", "selector": "a:nth-of-type(155)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(155)", "Font", "px", "Text", "MSPs", "MSSPs"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 91, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"ISO 27001\"", "selector": "a:nth-of-type(157)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(157)", "Font", "px", "Text", "ISO"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 92, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"SOC 2\"", "selector": "a:nth-of-type(158)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(158)", "Font", "px", "Text", "SOC"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 93, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"GDPR\"", "selector": "a:nth-of-type(159)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(159)", "Font", "px", "Text", "GDPR"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 94, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"TPN\"", "selector": "a:nth-of-type(160)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(160)", "Font", "px", "Text", "TPN"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 95, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Affiliate Referral Program\"", "selector": "a:nth-of-type(162)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(162)", "Font", "px", "Text", "Affiliate", "Referral", "Program"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 96, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"SPF DKIM DMARC Security Tool\"", "selector": "a:nth-of-type(164)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(164)", "Font", "px", "Text", "SPF", "DKIM", "DMARC", "Security", "Tool"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 97, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Subdomain Discovery Tool\"", "selector": "a:nth-of-type(165)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(165)", "Font", "px", "Text", "Subdomain", "Discovery", "Tool"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 98, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"About\"", "selector": "a:nth-of-type(167)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(167)", "Font", "px", "Text", "About"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 99, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Careers\"", "selector": "a:nth-of-type(168)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(168)", "Font", "px", "Text", "Careers"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 100, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Terms & Policies\"", "selector": "a:nth-of-type(169)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(169)", "Font", "px", "Text", "Terms", "Policies"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 101, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Status\"", "selector": "a:nth-of-type(170)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(170)", "Font", "px", "Text", "Status"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 102, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"<EMAIL>\"", "selector": "a:nth-of-type(172)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(172)", "Font", "px", "Text", "hello", "hostedscan.com"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 103, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"2212 Queen Anne Ave N Suite #521\"", "selector": "p:nth-of-type(174)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(174)", "Font", "px", "Text", "Queen", "<PERSON>", "Ave", "N", "Suite"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 104, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Seattle, WA 98109\"", "selector": "p:nth-of-type(175)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(175)", "Font", "px", "Text", "Seattle", "WA"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 105, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"<EMAIL>\"", "selector": "a:nth-of-type(179)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(179)", "Font", "px", "Text", "hello", "hostedscan.com"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 106, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"2212 Queen Anne Ave N Suite #521\"", "selector": "p:nth-of-type(181)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(181)", "Font", "px", "Text", "Queen", "<PERSON>", "Ave", "N", "Suite"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 107, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Seattle, WA 98109\"", "selector": "p:nth-of-type(182)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(182)", "Font", "px", "Text", "Seattle", "WA"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 108, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"About\"", "selector": "a:nth-of-type(184)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(184)", "Font", "px", "Text", "About"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 109, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Careers\"", "selector": "a:nth-of-type(185)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(185)", "Font", "px", "Text", "Careers"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 110, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Terms & Policies\"", "selector": "a:nth-of-type(186)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(186)", "Font", "px", "Text", "Terms", "Policies"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 111, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Status\"", "selector": "a:nth-of-type(187)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(187)", "Font", "px", "Text", "Status"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 112, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"About\"", "selector": "a:nth-of-type(190)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(190)", "Font", "px", "Text", "About"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 113, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Careers\"", "selector": "a:nth-of-type(191)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(191)", "Font", "px", "Text", "Careers"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 114, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Terms & Policies\"", "selector": "a:nth-of-type(192)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(192)", "Font", "px", "Text", "Terms", "Policies"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 115, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Status\"", "selector": "a:nth-of-type(193)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(193)", "Font", "px", "Text", "Status"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 116, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"<EMAIL>\"", "selector": "a:nth-of-type(195)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(195)", "Font", "px", "Text", "hello", "hostedscan.com"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 117, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"2212 Queen Anne Ave N Suite #521\"", "selector": "p:nth-of-type(197)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(197)", "Font", "px", "Text", "Queen", "<PERSON>", "Ave", "N", "Suite"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 118, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Seattle, WA 98109\"", "selector": "p:nth-of-type(198)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(198)", "Font", "px", "Text", "Seattle", "WA"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 119, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"© HostedScan, LLC. 2019 - 2025. All rights reserved.\"", "selector": "p:nth-of-type(199)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(199)", "Font", "px", "Text", "HostedScan", "LLC", "All", "rights", "reserved"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 120, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Our Scanners\"", "selector": "a:nth-of-type(201)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(201)", "Font", "px", "Text", "Our", "Scanners"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 121, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Pricing\"", "selector": "a:nth-of-type(202)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(202)", "Font", "px", "Text", "Pricing"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 122, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Documentation\"", "selector": "a:nth-of-type(203)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(203)", "Font", "px", "Text", "Documentation"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 123, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Qualys vs HostedScan\"", "selector": "a:nth-of-type(205)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(205)", "Font", "px", "Text", "Qualys", "vs", "HostedScan"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 124, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Nessus Tenable vs HostedScan\"", "selector": "a:nth-of-type(206)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(206)", "Font", "px", "Text", "<PERSON><PERSON><PERSON>", "Tenable", "vs", "HostedScan"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 125, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Netsparker vs HostedScan\"", "selector": "a:nth-of-type(207)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(207)", "Font", "px", "Text", "Netsparker", "vs", "HostedScan"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 126, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Detectify vs HostedScan\"", "selector": "a:nth-of-type(208)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(208)", "Font", "px", "Text", "Detectify", "vs", "HostedScan"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 127, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"OpenVAS - Network Scan\"", "selector": "a:nth-of-type(210)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(210)", "Font", "px", "Text", "OpenVAS", "Network", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 128, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Nmap - Port Scan\"", "selector": "a:nth-of-type(211)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(211)", "Font", "px", "Text", "Nmap", "Port", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 129, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"OWASP ZAP - Web Applications\"", "selector": "a:nth-of-type(212)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(212)", "Font", "px", "Text", "OWASP", "ZAP", "Web", "Applications"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 130, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"OWASP ZAP - API Security Scan\"", "selector": "a:nth-of-type(213)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(213)", "Font", "px", "Text", "OWASP", "ZAP", "API", "Security", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 131, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.308Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"SSLyze - TLS & SSL\"", "selector": "a:nth-of-type(214)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(214)", "Font", "px", "Text", "SSLyze", "TLS", "SSL"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 132, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Attack Surface Management (EASM)\"", "selector": "a:nth-of-type(216)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(216)", "Font", "px", "Text", "Attack", "Surface", "Management", "EASM"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 133, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Automated Penetration Testing\"", "selector": "a:nth-of-type(217)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(217)", "Font", "px", "Text", "Automated", "Penetration", "Testing"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 134, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Authenticated Web App Scanning\"", "selector": "a:nth-of-type(218)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(218)", "Font", "px", "Text", "Authenticated", "Web", "App", "Scanning"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 135, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"External Vulnerability Scan\"", "selector": "a:nth-of-type(219)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(219)", "Font", "px", "Text", "External", "Vulnerability", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 136, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Internal Vulnerability Scan\"", "selector": "a:nth-of-type(220)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(220)", "Font", "px", "Text", "Internal", "Vulnerability", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 137, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Wordpress Vulnerability Scan\"", "selector": "a:nth-of-type(221)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(221)", "Font", "px", "Text", "Wordpress", "Vulnerability", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 138, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Cloud Security\"", "selector": "a:nth-of-type(222)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(222)", "Font", "px", "Text", "Cloud", "Security"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 139, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Vulnerability Management\"", "selector": "a:nth-of-type(223)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(223)", "Font", "px", "Text", "Vulnerability", "Management"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 140, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"DAST Scanner\"", "selector": "a:nth-of-type(224)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(224)", "Font", "px", "Text", "DAST", "Scanner"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 141, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Online Port Scanner\"", "selector": "a:nth-of-type(225)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(225)", "Font", "px", "Text", "Online", "Port", "Scanner"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 142, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Online Vulnerability Scanner\"", "selector": "a:nth-of-type(226)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(226)", "Font", "px", "Text", "Online", "Vulnerability", "Scanner"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 143, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Continuous Security Monitoring\"", "selector": "a:nth-of-type(227)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(227)", "Font", "px", "Text", "Continuous", "Security", "Monitoring"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 144, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"DevSecOps Scanning\"", "selector": "a:nth-of-type(228)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(228)", "Font", "px", "Text", "DevSecOps", "Scanning"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 145, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Enterprise & Reseller\"", "selector": "a:nth-of-type(229)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(229)", "Font", "px", "Text", "Enterprise", "Reseller"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 146, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"MSPs & MSSPs\"", "selector": "a:nth-of-type(230)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(230)", "Font", "px", "Text", "MSPs", "MSSPs"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 147, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"ISO 27001\"", "selector": "a:nth-of-type(232)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(232)", "Font", "px", "Text", "ISO"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 148, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"SOC 2\"", "selector": "a:nth-of-type(233)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(233)", "Font", "px", "Text", "SOC"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 149, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"GDPR\"", "selector": "a:nth-of-type(234)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(234)", "Font", "px", "Text", "GDPR"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 150, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"TPN\"", "selector": "a:nth-of-type(235)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(235)", "Font", "px", "Text", "TPN"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 151, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"SPF DKIM DMARC Security Tool\"", "selector": "a:nth-of-type(237)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(237)", "Font", "px", "Text", "SPF", "DKIM", "DMARC", "Security", "Tool"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 152, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Subdomain Discovery Tool\"", "selector": "a:nth-of-type(238)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(238)", "Font", "px", "Text", "Subdomain", "Discovery", "Tool"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 153, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Affiliate Referral Program\"", "selector": "a:nth-of-type(240)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(240)", "Font", "px", "Text", "Affiliate", "Referral", "Program"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 154, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"About\"", "selector": "a:nth-of-type(242)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(242)", "Font", "px", "Text", "About"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 155, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Careers\"", "selector": "a:nth-of-type(243)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(243)", "Font", "px", "Text", "Careers"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 156, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Terms & Policies\"", "selector": "a:nth-of-type(244)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(244)", "Font", "px", "Text", "Terms", "Policies"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 157, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Status\"", "selector": "a:nth-of-type(245)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(245)", "Font", "px", "Text", "Status"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 158, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"<EMAIL>\"", "selector": "a:nth-of-type(247)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(247)", "Font", "px", "Text", "hello", "hostedscan.com"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 159, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"2212 Queen Anne Ave N Suite #521\"", "selector": "p:nth-of-type(249)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(249)", "Font", "px", "Text", "Queen", "<PERSON>", "Ave", "N", "Suite"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 160, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Seattle, WA 98109\"", "selector": "p:nth-of-type(250)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(250)", "Font", "px", "Text", "Seattle", "WA"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 161, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"<EMAIL>\"", "selector": "a:nth-of-type(254)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(254)", "Font", "px", "Text", "hello", "hostedscan.com"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 162, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"2212 Queen Anne Ave N Suite #521\"", "selector": "p:nth-of-type(256)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(256)", "Font", "px", "Text", "Queen", "<PERSON>", "Ave", "N", "Suite"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 163, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Seattle, WA 98109\"", "selector": "p:nth-of-type(257)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(257)", "Font", "px", "Text", "Seattle", "WA"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 164, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"About\"", "selector": "a:nth-of-type(259)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(259)", "Font", "px", "Text", "About"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 165, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Careers\"", "selector": "a:nth-of-type(260)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(260)", "Font", "px", "Text", "Careers"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 166, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Terms & Policies\"", "selector": "a:nth-of-type(261)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(261)", "Font", "px", "Text", "Terms", "Policies"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 167, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Status\"", "selector": "a:nth-of-type(262)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(262)", "Font", "px", "Text", "Status"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 168, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"About\"", "selector": "a:nth-of-type(265)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(265)", "Font", "px", "Text", "About"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 169, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Careers\"", "selector": "a:nth-of-type(266)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(266)", "Font", "px", "Text", "Careers"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 170, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Terms & Policies\"", "selector": "a:nth-of-type(267)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(267)", "Font", "px", "Text", "Terms", "Policies"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 171, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Status\"", "selector": "a:nth-of-type(268)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(268)", "Font", "px", "Text", "Status"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 172, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"<EMAIL>\"", "selector": "a:nth-of-type(270)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(270)", "Font", "px", "Text", "hello", "hostedscan.com"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 173, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"2212 Queen Anne Ave N Suite #521\"", "selector": "p:nth-of-type(272)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(272)", "Font", "px", "Text", "Queen", "<PERSON>", "Ave", "N", "Suite"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 174, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Seattle, WA 98109\"", "selector": "p:nth-of-type(273)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(273)", "Font", "px", "Text", "Seattle", "WA"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 175, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"© HostedScan, LLC. 2019 - 2025. All rights reserved.\"", "selector": "p:nth-of-type(274)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(274)", "Font", "px", "Text", "HostedScan", "LLC", "All", "rights", "reserved"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 176, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Attack Surface Management (EASM)\"", "selector": "a:nth-of-type(276)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(276)", "Font", "px", "Text", "Attack", "Surface", "Management", "EASM"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 177, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Automated Penetration Testing\"", "selector": "a:nth-of-type(277)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(277)", "Font", "px", "Text", "Automated", "Penetration", "Testing"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 178, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Authenticated Web App Scanning\"", "selector": "a:nth-of-type(278)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(278)", "Font", "px", "Text", "Authenticated", "Web", "App", "Scanning"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 179, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"External Vulnerability Scan\"", "selector": "a:nth-of-type(279)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(279)", "Font", "px", "Text", "External", "Vulnerability", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 180, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Internal Vulnerability Scan\"", "selector": "a:nth-of-type(280)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(280)", "Font", "px", "Text", "Internal", "Vulnerability", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 181, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Wordpress Vulnerability Scan\"", "selector": "a:nth-of-type(281)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(281)", "Font", "px", "Text", "Wordpress", "Vulnerability", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 182, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Cloud Security\"", "selector": "a:nth-of-type(282)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(282)", "Font", "px", "Text", "Cloud", "Security"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 183, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Vulnerability Management\"", "selector": "a:nth-of-type(283)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(283)", "Font", "px", "Text", "Vulnerability", "Management"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 184, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"DAST Scanner\"", "selector": "a:nth-of-type(284)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(284)", "Font", "px", "Text", "DAST", "Scanner"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 185, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Online Port Scanner\"", "selector": "a:nth-of-type(285)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(285)", "Font", "px", "Text", "Online", "Port", "Scanner"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 186, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Online Vulnerability Scanner\"", "selector": "a:nth-of-type(286)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(286)", "Font", "px", "Text", "Online", "Vulnerability", "Scanner"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 187, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Continuous Security Monitoring\"", "selector": "a:nth-of-type(287)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(287)", "Font", "px", "Text", "Continuous", "Security", "Monitoring"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 188, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"DevSecOps Scanning\"", "selector": "a:nth-of-type(288)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(288)", "Font", "px", "Text", "DevSecOps", "Scanning"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 189, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Enterprise & Reseller\"", "selector": "a:nth-of-type(289)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(289)", "Font", "px", "Text", "Enterprise", "Reseller"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 190, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"MSPs & MSSPs\"", "selector": "a:nth-of-type(290)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(290)", "Font", "px", "Text", "MSPs", "MSSPs"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 191, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"ISO 27001\"", "selector": "a:nth-of-type(292)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(292)", "Font", "px", "Text", "ISO"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 192, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"SOC 2\"", "selector": "a:nth-of-type(293)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(293)", "Font", "px", "Text", "SOC"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 193, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"GDPR\"", "selector": "a:nth-of-type(294)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(294)", "Font", "px", "Text", "GDPR"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 194, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"TPN\"", "selector": "a:nth-of-type(295)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(295)", "Font", "px", "Text", "TPN"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 195, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Qualys vs HostedScan\"", "selector": "a:nth-of-type(297)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(297)", "Font", "px", "Text", "Qualys", "vs", "HostedScan"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 196, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Nessus Tenable vs HostedScan\"", "selector": "a:nth-of-type(298)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(298)", "Font", "px", "Text", "<PERSON><PERSON><PERSON>", "Tenable", "vs", "HostedScan"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 197, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Netsparker vs HostedScan\"", "selector": "a:nth-of-type(299)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(299)", "Font", "px", "Text", "Netsparker", "vs", "HostedScan"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 198, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Detectify vs HostedScan\"", "selector": "a:nth-of-type(300)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(300)", "Font", "px", "Text", "Detectify", "vs", "HostedScan"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 199, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"OpenVAS - Network Scan\"", "selector": "a:nth-of-type(302)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(302)", "Font", "px", "Text", "OpenVAS", "Network", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 200, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Nmap - Port Scan\"", "selector": "a:nth-of-type(303)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(303)", "Font", "px", "Text", "Nmap", "Port", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 201, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"OWASP ZAP - Web Applications\"", "selector": "a:nth-of-type(304)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(304)", "Font", "px", "Text", "OWASP", "ZAP", "Web", "Applications"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 202, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"OWASP ZAP - API Security Scan\"", "selector": "a:nth-of-type(305)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(305)", "Font", "px", "Text", "OWASP", "ZAP", "API", "Security", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 203, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"SSLyze - TLS & SSL\"", "selector": "a:nth-of-type(306)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(306)", "Font", "px", "Text", "SSLyze", "TLS", "SSL"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 204, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"SPF DKIM DMARC Security Tool\"", "selector": "a:nth-of-type(308)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(308)", "Font", "px", "Text", "SPF", "DKIM", "DMARC", "Security", "Tool"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 205, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Subdomain Discovery Tool\"", "selector": "a:nth-of-type(309)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(309)", "Font", "px", "Text", "Subdomain", "Discovery", "Tool"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 206, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Affiliate Referral Program\"", "selector": "a:nth-of-type(311)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(311)", "Font", "px", "Text", "Affiliate", "Referral", "Program"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 207, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"About\"", "selector": "a:nth-of-type(313)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(313)", "Font", "px", "Text", "About"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 208, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Careers\"", "selector": "a:nth-of-type(314)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(314)", "Font", "px", "Text", "Careers"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 209, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Terms & Policies\"", "selector": "a:nth-of-type(315)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(315)", "Font", "px", "Text", "Terms", "Policies"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 210, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Status\"", "selector": "a:nth-of-type(316)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(316)", "Font", "px", "Text", "Status"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 211, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"<EMAIL>\"", "selector": "a:nth-of-type(318)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(318)", "Font", "px", "Text", "hello", "hostedscan.com"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 212, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"2212 Queen Anne Ave N Suite #521\"", "selector": "p:nth-of-type(320)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(320)", "Font", "px", "Text", "Queen", "<PERSON>", "Ave", "N", "Suite"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 213, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Seattle, WA 98109\"", "selector": "p:nth-of-type(321)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(321)", "Font", "px", "Text", "Seattle", "WA"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 214, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"<EMAIL>\"", "selector": "a:nth-of-type(325)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(325)", "Font", "px", "Text", "hello", "hostedscan.com"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 215, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"2212 Queen Anne Ave N Suite #521\"", "selector": "p:nth-of-type(327)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(327)", "Font", "px", "Text", "Queen", "<PERSON>", "Ave", "N", "Suite"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 216, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Seattle, WA 98109\"", "selector": "p:nth-of-type(328)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(328)", "Font", "px", "Text", "Seattle", "WA"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 217, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"About\"", "selector": "a:nth-of-type(330)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(330)", "Font", "px", "Text", "About"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 218, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Careers\"", "selector": "a:nth-of-type(331)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(331)", "Font", "px", "Text", "Careers"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 219, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Terms & Policies\"", "selector": "a:nth-of-type(332)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(332)", "Font", "px", "Text", "Terms", "Policies"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 220, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.309Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Status\"", "selector": "a:nth-of-type(333)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(333)", "Font", "px", "Text", "Status"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 221, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.310Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"About\"", "selector": "a:nth-of-type(336)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(336)", "Font", "px", "Text", "About"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 222, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.310Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Careers\"", "selector": "a:nth-of-type(337)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(337)", "Font", "px", "Text", "Careers"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 223, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.310Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Terms & Policies\"", "selector": "a:nth-of-type(338)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(338)", "Font", "px", "Text", "Terms", "Policies"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 224, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.310Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Status\"", "selector": "a:nth-of-type(339)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(339)", "Font", "px", "Text", "Status"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 225, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.310Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"<EMAIL>\"", "selector": "a:nth-of-type(341)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(341)", "Font", "px", "Text", "hello", "hostedscan.com"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 226, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.310Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"2212 Queen Anne Ave N Suite #521\"", "selector": "p:nth-of-type(343)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(343)", "Font", "px", "Text", "Queen", "<PERSON>", "Ave", "N", "Suite"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 227, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.310Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Seattle, WA 98109\"", "selector": "p:nth-of-type(344)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(344)", "Font", "px", "Text", "Seattle", "WA"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 228, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.310Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"© HostedScan, LLC. 2019 - 2025. All rights reserved.\"", "selector": "p:nth-of-type(345)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(345)", "Font", "px", "Text", "HostedScan", "LLC", "All", "rights", "reserved"], "severity": "warning", "metadata": {"scanDuration": 4083, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 229, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.310Z"}}}], "recommendations": ["Implement responsive typography with relative units", "Test layout at 200% text scaling", "Use relative units (em, rem, %) for font sizes", "Ensure text can be resized up to 200% without loss of functionality", "Avoid fixed pixel dimensions for text containers", "Test text resizing with browser zoom and font size controls", "Remove viewport restrictions that prevent user scaling"], "executionTime": 2797, "originalScore": 0}, "timestamp": 1752233907310, "hash": "bb8ab1459c3ab64c1966463bac77d7a3", "accessCount": 1, "lastAccessed": 1752233907310, "size": 128988}
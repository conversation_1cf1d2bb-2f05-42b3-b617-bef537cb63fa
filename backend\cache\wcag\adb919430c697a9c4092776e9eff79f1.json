{"data": {"ruleId": "WCAG-058", "ruleName": "Motor", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.04, "automated": true, "evidence": [{"type": "measurement", "description": "Interactive element 1 may be too small for motor accessibility", "value": "Size: 135.015625x28px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x28px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 2 meets minimum target size", "value": "Size: 152.09375x44px, Meets minimum: true", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x44px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 3 meets minimum target size", "value": "Size: 111.65625x44px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x44px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 4 meets minimum target size", "value": "Size: 90.171875x44px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x44px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 5 meets minimum target size", "value": "Size: 150.09375x44px, Meets minimum: true", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x44px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 6 meets minimum target size", "value": "Size: 69.828125x44px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x44px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 7 may be too small for motor accessibility", "value": "Size: 90.921875x41px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x41px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 8 may be too small for motor accessibility", "value": "Size: 103.46875x41px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x41px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 9 meets minimum target size", "value": "Size: 490x58px, Meets minimum: true", "element": "input", "elementCount": 1, "affectedSelectors": ["Size", "x58px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 10 meets minimum target size", "value": "Size: 145.890625x50px, Meets minimum: true", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x50px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 11 may be too small for motor accessibility", "value": "Size: 363.796875x28px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x28px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 12 may be too small for motor accessibility", "value": "Size: 488.40625x28px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x28px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 13 may be too small for motor accessibility", "value": "Size: 363.796875x28px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x28px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 14 may be too small for motor accessibility", "value": "Size: 488.390625x28px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x28px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 15 meets minimum target size", "value": "Size: 192x50px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x50px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 16 meets minimum target size", "value": "Size: 192x50px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x50px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 17 may be too small for motor accessibility", "value": "Size: 117.859375x28px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x28px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 18 may be too small for motor accessibility", "value": "Size: 179.125x28px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x28px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 19 meets minimum target size", "value": "Size: 48x48px, Meets minimum: true", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x48px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 20 meets minimum target size", "value": "Size: 48x48px, Meets minimum: true", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x48px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 21 may be too small for motor accessibility", "value": "Size: 273x24px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x24px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 22 may be too small for motor accessibility", "value": "Size: 273x24px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x24px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 23 may be too small for motor accessibility", "value": "Size: 273x24px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x24px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 24 may be too small for motor accessibility", "value": "Size: 273x24px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x24px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 25 may be too small for motor accessibility", "value": "Size: 273x24px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x24px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 26 may be too small for motor accessibility", "value": "Size: 273x24px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x24px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 27 may be too small for motor accessibility", "value": "Size: 273x24px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x24px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 28 may be too small for motor accessibility", "value": "Size: 273x24px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x24px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 29 may be too small for motor accessibility", "value": "Size: 87.1875x24px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x24px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 30 meets minimum target size", "value": "Size: 320x350px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x350px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 31 meets minimum target size", "value": "Size: 320x350px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x350px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 32 meets minimum target size", "value": "Size: 320x350px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x350px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 33 meets minimum target size", "value": "Size: 320x350px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x350px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.638Z"}}}, {"type": "measurement", "description": "Interactive element 34 meets minimum target size", "value": "Size: 320x350px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x350px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 35 meets minimum target size", "value": "Size: 48x48px, Meets minimum: true", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x48px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 36 meets minimum target size", "value": "Size: 48x48px, Meets minimum: true", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x48px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 37 meets minimum target size", "value": "Size: 192x50px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x50px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 38 meets minimum target size", "value": "Size: 192x50px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x50px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 39 may be too small for motor accessibility", "value": "Size: 223.015625x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 40 may be too small for motor accessibility", "value": "Size: 191.484375x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 41 may be too small for motor accessibility", "value": "Size: 208.421875x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 42 may be too small for motor accessibility", "value": "Size: 162.328125x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 43 may be too small for motor accessibility", "value": "Size: 160.46875x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 44 may be too small for motor accessibility", "value": "Size: 179.59375x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 45 may be too small for motor accessibility", "value": "Size: 89.203125x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 46 may be too small for motor accessibility", "value": "Size: 163.015625x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 47 may be too small for motor accessibility", "value": "Size: 86.1875x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 48 may be too small for motor accessibility", "value": "Size: 122.375x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 49 may be too small for motor accessibility", "value": "Size: 174.0625x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 50 may be too small for motor accessibility", "value": "Size: 196.90625x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 51 may be too small for motor accessibility", "value": "Size: 130.0625x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 52 may be too small for motor accessibility", "value": "Size: 128.078125x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 53 may be too small for motor accessibility", "value": "Size: 93.90625x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 54 may be too small for motor accessibility", "value": "Size: 63.296875x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 55 may be too small for motor accessibility", "value": "Size: 38.046875x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 56 may be too small for motor accessibility", "value": "Size: 35.640625x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 57 may be too small for motor accessibility", "value": "Size: 25.65625x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 58 may be too small for motor accessibility", "value": "Size: 135.375x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 59 may be too small for motor accessibility", "value": "Size: 188.3125x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 60 may be too small for motor accessibility", "value": "Size: 161.515625x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 61 may be too small for motor accessibility", "value": "Size: 148.625x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 62 may be too small for motor accessibility", "value": "Size: 157.125x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 63 may be too small for motor accessibility", "value": "Size: 109.078125x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 64 may be too small for motor accessibility", "value": "Size: 197x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 65 may be too small for motor accessibility", "value": "Size: 195.15625x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 64, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 66 may be too small for motor accessibility", "value": "Size: 116.1875x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 65, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 67 may be too small for motor accessibility", "value": "Size: 194.640625x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 66, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 68 may be too small for motor accessibility", "value": "Size: 163.46875x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 67, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 69 may be too small for motor accessibility", "value": "Size: 155.265625x19px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 68, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 70 may be too small for motor accessibility", "value": "Size: 38x38px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x38px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 69, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 71 may be too small for motor accessibility", "value": "Size: 38x38px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x38px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 70, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 72 may be too small for motor accessibility", "value": "Size: 221.4375x20px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x20px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 71, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 73 may be too small for motor accessibility", "value": "Size: 221.4375x20px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x20px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 72, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 74 may be too small for motor accessibility", "value": "Size: 221.4375x20px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x20px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 73, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 75 may be too small for motor accessibility", "value": "Size: 221.4375x20px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x20px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 74, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 76 may be too small for motor accessibility", "value": "Size: 221.4375x20px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x20px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 75, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "measurement", "description": "Interactive element 77 may be too small for motor accessibility", "value": "Size: 221.4375x40px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x40px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 76, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "interaction", "description": "Error analyzing gesture requirements", "value": "Error: element.className.includes is not a function\npptr:evaluate;MotorCheck.analyzeGestureRequirements%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cchecks%5Cmotor.ts%3A213%3A48):7:43", "element": "gesture elements", "elementCount": 1, "affectedSelectors": ["Error", "element.className.includes", "is", "not", "a", "function", "pptr", "evaluate", "MotorCheck.analyzeGestureRequirements", "D", "A", "CWeb", "projects", "CCom<PERSON>ly", "Checker", "Cbackend", "Csrc", "Ccompliance", "Cwcag", "Cchecks", "Cmotor.ts", "A213", "A48"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 77, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "interaction", "description": "Error analyzing motion controls", "value": "Error: element.className.toLowerCase is not a function\npptr:evaluate;MotorCheck.analyzeMotionControls%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cchecks%5Cmotor.ts%3A330%3A47):16:57", "element": "motion elements", "elementCount": 1, "affectedSelectors": ["Error", "element.className.toLowerCase", "is", "not", "a", "function", "pptr", "evaluate", "MotorCheck.analyzeMotionControls", "D", "A", "CWeb", "projects", "CCom<PERSON>ly", "Checker", "Cbackend", "Csrc", "Ccompliance", "Cwcag", "Cchecks", "Cmotor.ts", "A330", "A47"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 78, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}, {"type": "interaction", "description": "No timeout indicators found", "value": "Timeouts found: false", "element": "page", "elementCount": 1, "affectedSelectors": ["Timeouts", "found", "false"], "metadata": {"scanDuration": 2328, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 79, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T11:38:08.639Z"}}}], "recommendations": ["Increase size of a element 1 to at least 44x44px", "Increase size of a element 7 to at least 44x44px", "Increase size of a element 8 to at least 44x44px", "Increase size of a element 11 to at least 44x44px", "Increase size of a element 12 to at least 44x44px", "Increase size of a element 13 to at least 44x44px", "Increase size of a element 14 to at least 44x44px", "Increase size of a element 17 to at least 44x44px", "Increase size of a element 18 to at least 44x44px", "Increase size of a element 21 to at least 44x44px", "Increase size of a element 22 to at least 44x44px", "Increase size of a element 23 to at least 44x44px", "Increase size of a element 24 to at least 44x44px", "Increase size of a element 25 to at least 44x44px", "Increase size of a element 26 to at least 44x44px", "Increase size of a element 27 to at least 44x44px", "Increase size of a element 28 to at least 44x44px", "Increase size of a element 29 to at least 44x44px", "Increase size of a element 39 to at least 44x44px", "Increase size of a element 40 to at least 44x44px", "Increase size of a element 41 to at least 44x44px", "Increase size of a element 42 to at least 44x44px", "Increase size of a element 43 to at least 44x44px", "Increase size of a element 44 to at least 44x44px", "Increase size of a element 45 to at least 44x44px", "Increase size of a element 46 to at least 44x44px", "Increase size of a element 47 to at least 44x44px", "Increase size of a element 48 to at least 44x44px", "Increase size of a element 49 to at least 44x44px", "Increase size of a element 50 to at least 44x44px", "Increase size of a element 51 to at least 44x44px", "Increase size of a element 52 to at least 44x44px", "Increase size of a element 53 to at least 44x44px", "Increase size of a element 54 to at least 44x44px", "Increase size of a element 55 to at least 44x44px", "Increase size of a element 56 to at least 44x44px", "Increase size of a element 57 to at least 44x44px", "Increase size of a element 58 to at least 44x44px", "Increase size of a element 59 to at least 44x44px", "Increase size of a element 60 to at least 44x44px", "Increase size of a element 61 to at least 44x44px", "Increase size of a element 62 to at least 44x44px", "Increase size of a element 63 to at least 44x44px", "Increase size of a element 64 to at least 44x44px", "Increase size of a element 65 to at least 44x44px", "Increase size of a element 66 to at least 44x44px", "Increase size of a element 67 to at least 44x44px", "Increase size of a element 68 to at least 44x44px", "Increase size of a element 69 to at least 44x44px", "Increase size of a element 70 to at least 44x44px", "Increase size of a element 71 to at least 44x44px", "Increase size of a element 72 to at least 44x44px", "Increase size of a element 73 to at least 44x44px", "Increase size of a element 74 to at least 44x44px", "Increase size of a element 75 to at least 44x44px", "Increase size of a element 76 to at least 44x44px", "Increase size of a element 77 to at least 44x44px", "Check gesture requirements manually", "Check motion controls manually"], "executionTime": 965, "originalScore": 26}, "timestamp": 1752233888639, "hash": "72baf193cf2e25092ee50e17b084a133", "accessCount": 1, "lastAccessed": 1752233888639, "size": 43811}
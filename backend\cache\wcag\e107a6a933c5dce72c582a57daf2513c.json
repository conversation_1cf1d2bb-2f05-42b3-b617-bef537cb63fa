{"data": {"ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0611, "automated": true, "evidence": [{"type": "code", "description": "Moving content without user controls: JavaScript-based animation detected", "value": "Moving content detected", "selector": "script", "severity": "error", "elementCount": 1, "affectedSelectors": ["script", "Moving", "content", "detected"], "metadata": {"scanDuration": 361, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T20:07:40.989Z"}}}], "recommendations": ["Provide pause, stop, or hide controls for moving content", "Allow users to control auto-updating content", "Ensure moving content can be paused on user request", "Consider reducing or eliminating unnecessary animations"], "executionTime": 45, "originalScore": 0, "thresholdApplied": 75, "scoringDetails": "0.0% (threshold: 75%) - FAILED"}, "timestamp": 1752264460989, "hash": "db0579feb66668d69feb55de18b48c0f", "accessCount": 1, "lastAccessed": 1752264460989, "size": 1065, "metadata": {"originalKey": "WCAG-045:053b13d2:add92319", "normalizedKey": "wcag-045_053b13d2_add92319", "savedAt": 1752264460990, "version": "1.1", "keyHash": "7b43cd99526d17cceac5e8e4d08aa542"}}
{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame 'A362B1B11B79F8E7E1503ABC8B8AFA25'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A362B1B11B79F8E7E1503ABC8B8AFA25"], "metadata": {"scanDuration": 1, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.85, "checkType": "keyboard-trap-analysis", "focusManagementValidation": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "modalTrapDetection": true, "escapeRouteValidation": true, "evidenceIndex": 0, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-11T18:49:52.765Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259792765, "hash": "aae196266e525460ffc48b82d5cd84b4", "accessCount": 1, "lastAccessed": 1752259792765, "size": 852, "metadata": {"originalKey": "WCAG-027:WCAG-027:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-027_wcag-027_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752259792766, "version": "1.1", "keyHash": "7527784732554e5067febf06f0a04cab"}}
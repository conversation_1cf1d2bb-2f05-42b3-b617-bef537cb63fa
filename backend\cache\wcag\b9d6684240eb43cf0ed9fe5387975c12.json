{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "metadata": {"scanDuration": 84, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.85, "checkType": "keyboard-trap-analysis", "focusManagementValidation": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "modalTrapDetection": true, "escapeRouteValidation": true, "evidenceIndex": 0, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-11T18:03:52.960Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752257032960, "hash": "a73bc604b763a9569f4a6ba9e1b00611", "accessCount": 1, "lastAccessed": 1752257032960, "size": 852, "metadata": {"originalKey": "WCAG-027:WCAG-027:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-027_wcag-027_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752257032960, "version": "1.1", "keyHash": "7527784732554e5067febf06f0a04cab"}}
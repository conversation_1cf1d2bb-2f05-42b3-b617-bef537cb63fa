{"data": [{"type": "code", "description": "Context change detection: high risk", "value": "Unexpected: 27, User-controlled: 0, Automatic: 0, Warnings: 0", "severity": "error", "elementCount": 1, "affectedSelectors": ["Unexpected", "User-controlled", "Automatic", "Warnings"], "metadata": {"scanDuration": 48, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "context-change-analysis", "behaviorAnalysis": true, "userInitiatedChanges": true, "accessibilityPatterns": true, "componentLibraryDetection": true, "evidenceIndex": 0, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-11T11:39:11.455Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Detected context change types", "value": "new-window", "severity": "info", "elementCount": 1, "affectedSelectors": ["new-window"], "metadata": {"scanDuration": 48, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "context-change-analysis", "behaviorAnalysis": true, "userInitiatedChanges": true, "accessibilityPatterns": true, "componentLibraryDetection": true, "evidenceIndex": 1, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-11T11:39:11.455Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "User control validation: Inadequate control mechanisms", "value": "Available controls: none (need at least 2)", "severity": "error", "elementCount": 1, "affectedSelectors": ["Available", "controls", "none", "need", "at", "least"], "metadata": {"scanDuration": 48, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "context-change-analysis", "behaviorAnalysis": true, "userInitiatedChanges": true, "accessibilityPatterns": true, "componentLibraryDetection": true, "evidenceIndex": 2, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-11T11:39:11.455Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Unexpected change analysis: Low risk of unexpected changes", "value": "Risk assessment: low, Score: 0.0%", "severity": "info", "elementCount": 1, "affectedSelectors": ["Risk", "assessment", "low", "Score"], "metadata": {"scanDuration": 48, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "context-change-analysis", "behaviorAnalysis": true, "userInitiatedChanges": true, "accessibilityPatterns": true, "componentLibraryDetection": true, "evidenceIndex": 3, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-11T11:39:11.455Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Accessibility control testing: No context change controls detected", "value": "No context change controls found to test", "severity": "info", "elementCount": 1, "affectedSelectors": ["No", "context", "change", "controls", "found", "to", "test"], "metadata": {"scanDuration": 48, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "context-change-analysis", "behaviorAnalysis": true, "userInitiatedChanges": true, "accessibilityPatterns": true, "componentLibraryDetection": true, "evidenceIndex": 4, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-11T11:39:11.455Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752233951455, "hash": "e2880c0980f8b2371bf0f2b389e47471", "accessCount": 1, "lastAccessed": 1752233951455, "size": 3827}
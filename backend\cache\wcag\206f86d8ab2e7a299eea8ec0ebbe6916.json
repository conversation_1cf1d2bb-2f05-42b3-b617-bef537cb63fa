{"data": {"ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "category": "operable", "wcagVersion": "2.2", "successCriterion": "2.4.11", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.06, "automated": true, "evidence": [{"type": "text", "description": "Focus obstruction analysis summary", "value": "13/75 focused elements remain unobscured", "severity": "error", "elementCount": 1, "affectedSelectors": ["focused", "elements", "remain", "unobscured"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "info", "description": "Advanced focus obstruction analysis with overlay detection", "element": "focus-obstructions", "value": "{\"overallScore\":17,\"criticalIssues\":[\"Focus indicator obscured for element: a.w-full\",\"Focus indicator obscured for element: #demo-account-login-button\",\"Focus indicator obscured for element: a.mt-10\",\"Focus indicator obscured for element: a.mt-10\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.w-full\",\"Focus indicator obscured for element: #demo-account-login-button\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Failed to analyze focus obstruction for #headlessui-menu-button-:Rspmm:: DOMException: SyntaxError: Failed to execute 'querySelector' on 'Document': '#headlessui-menu-button-:Rspmm:' is not a valid selector.\",\"Failed to analyze focus obstruction for #headlessui-menu-button-:Ru9mm:: DOMException: SyntaxError: Failed to execute 'querySelector' on 'Document': '#headlessui-menu-button-:Ru9mm:' is not a valid selector.\",\"Focus indicator obscured for element: button.right-1\",\"Focus indicator obscured for element: button.rounded-full\",\"Focus indicator obscured for element: button.rounded-full\",\"Focus indicator obscured for element: input.grow\"],\"recommendations\":[\"Ensure focus indicator for a.w-full is not obscured by other elements\",\"Ensure focus indicator for #demo-account-login-button is not obscured by other elements\",\"Ensure focus indicator for a.mt-10 is not obscured by other elements\",\"Ensure focus indicator for a.mt-10 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.w-full is not obscured by other elements\",\"Ensure focus indicator for #demo-account-login-button is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for button.right-1 is not obscured by other elements\",\"Ensure focus indicator for button.rounded-full is not obscured by other elements\",\"Ensure focus indicator for button.rounded-full is not obscured by other elements\",\"Ensure focus indicator for input.grow is not obscured by other elements\"],\"performanceMetrics\":{\"analysisTime\":1403,\"elementsAnalyzed\":75,\"obstructionsFound\":60}}", "severity": "error", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "Focus", "indicator", "obscured", "for", "element", "a.w-full", "demo-account-login-button", "a.mt-10", "a.mt-4", "a.relative", "a.text-sm", "Failed", "to", "analyze", "focus", "obstruction", "headlessui-menu-button-", "Rspmm", "DOMException", "SyntaxError", "execute", "querySelector", "on", "Document", "is", "not", "a", "valid", "selector", "Ru9mm", "button.right-1", "button.rounded-full", "input.grow", "recommendations", "Ensure", "by", "other", "elements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "obstructionsFound"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is not obscured by other content", "value": "Element remains fully visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "remains", "fully", "visible", "when", "focused"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is not obscured by other content", "value": "Element remains fully visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "remains", "fully", "visible", "when", "focused"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is not obscured by other content", "value": "Element remains fully visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "remains", "fully", "visible", "when", "focused"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is not obscured by other content", "value": "Element remains fully visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "remains", "fully", "visible", "when", "focused"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is not obscured by other content", "value": "Element remains fully visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "remains", "fully", "visible", "when", "focused"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is not obscured by other content", "value": "Element remains fully visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "remains", "fully", "visible", "when", "focused"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is not obscured by other content", "value": "Element remains fully visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "remains", "fully", "visible", "when", "focused"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is not obscured by other content", "value": "Element remains fully visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "remains", "fully", "visible", "when", "focused"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is not obscured by other content", "value": "Element remains fully visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "remains", "fully", "visible", "when", "focused"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is not obscured by other content", "value": "Element remains fully visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "remains", "fully", "visible", "when", "focused"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.w-full", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.w-full", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "#demo-account-login-button", "severity": "error", "elementCount": 1, "affectedSelectors": ["#demo-account-login-button", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.mt-10", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.mt-10", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.mt-10", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.mt-10", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.mt-4", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.mt-4", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.078Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.mt-4", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.079Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.mt-4", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.079Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.mt-4", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.079Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.mt-4", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.079Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.mt-4", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.079Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.mt-4", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.079Z"}}}, {"type": "measurement", "description": "Focused element is not obscured by other content", "value": "Element remains fully visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "remains", "fully", "visible", "when", "focused"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.080Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.relative", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.relative", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.080Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.relative", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.relative", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.080Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.relative", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.relative", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.080Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.relative", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.relative", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.080Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.relative", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.relative", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.080Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "a.w-full", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.w-full", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.080Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "#demo-account-login-button", "severity": "error", "elementCount": 1, "affectedSelectors": ["#demo-account-login-button", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.080Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.080Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is not obscured by other content", "value": "Element remains fully visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "remains", "fully", "visible", "when", "focused"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is not obscured by other content", "value": "Element remains fully visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "remains", "fully", "visible", "when", "focused"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 64, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 65, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 66, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 67, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 68, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 69, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element has no dimensions", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Obscured", "by", "Element", "has", "no", "dimensions"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 70, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.081Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "button.right-1", "severity": "error", "elementCount": 1, "affectedSelectors": ["button.right-1", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 71, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.082Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "button.rounded-full", "severity": "error", "elementCount": 1, "affectedSelectors": ["button.rounded-full", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 72, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.082Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "button.rounded-full", "severity": "error", "elementCount": 1, "affectedSelectors": ["button.rounded-full", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 73, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.082Z"}}}, {"type": "measurement", "description": "Focused element is obscured by other content", "value": "Obscured by: Element outside viewport", "selector": "input.grow", "severity": "error", "elementCount": 1, "affectedSelectors": ["input.grow", "Obscured", "by", "Element", "outside", "viewport"], "metadata": {"scanDuration": 4139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 74, "ruleId": "WCAG-010", "ruleName": "Focus Not Obscured (Minimum)", "timestamp": "2025-07-11T11:37:35.082Z"}}}], "recommendations": ["Review fixed and sticky positioned elements that may obscure focused content", "Ensure focus indicator for a.w-full is not obscured by other elements", "Ensure focus indicator for #demo-account-login-button is not obscured by other elements", "Ensure focus indicator for a.mt-10 is not obscured by other elements", "Ensure focus indicator for a.mt-10 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.w-full is not obscured by other elements", "Ensure focus indicator for #demo-account-login-button is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for button.right-1 is not obscured by other elements", "Ensure focus indicator for button.rounded-full is not obscured by other elements", "Ensure focus indicator for button.rounded-full is not obscured by other elements", "Ensure focus indicator for input.grow is not obscured by other elements", "Ensure a.w-full is not hidden by fixed/sticky elements when focused", "Ensure #demo-account-login-button is not hidden by fixed/sticky elements when focused", "Ensure a.mt-10 is not hidden by fixed/sticky elements when focused", "Ensure a.mt-10 is not hidden by fixed/sticky elements when focused", "Ensure a.mt-4 is not hidden by fixed/sticky elements when focused", "Ensure a.mt-4 is not hidden by fixed/sticky elements when focused", "Ensure a.mt-4 is not hidden by fixed/sticky elements when focused", "Ensure a.mt-4 is not hidden by fixed/sticky elements when focused", "Ensure a.mt-4 is not hidden by fixed/sticky elements when focused", "Ensure a.mt-4 is not hidden by fixed/sticky elements when focused", "Ensure a.mt-4 is not hidden by fixed/sticky elements when focused", "Ensure a.mt-4 is not hidden by fixed/sticky elements when focused", "Ensure a.relative is not hidden by fixed/sticky elements when focused", "Ensure a.relative is not hidden by fixed/sticky elements when focused", "Ensure a.relative is not hidden by fixed/sticky elements when focused", "Ensure a.relative is not hidden by fixed/sticky elements when focused", "Ensure a.relative is not hidden by fixed/sticky elements when focused", "Ensure a.w-full is not hidden by fixed/sticky elements when focused", "Ensure #demo-account-login-button is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure a.text-sm is not hidden by fixed/sticky elements when focused", "Ensure button.right-1 is not hidden by fixed/sticky elements when focused", "Ensure button.rounded-full is not hidden by fixed/sticky elements when focused", "Ensure button.rounded-full is not hidden by fixed/sticky elements when focused", "Ensure input.grow is not hidden by fixed/sticky elements when focused", "Consider using scroll-padding or scroll-margin CSS properties", "Ensure focused elements are scrolled into view when needed"], "executionTime": 2751, "originalScore": 17}, "timestamp": 1752233855082, "hash": "0f3bdcc05cef1d192140d1cf025db9b0", "accessCount": 1, "lastAccessed": 1752233855082, "size": 57452}
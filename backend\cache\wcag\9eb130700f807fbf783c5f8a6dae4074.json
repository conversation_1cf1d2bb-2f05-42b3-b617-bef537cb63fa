{"data": {"ruleId": "WCAG-056", "ruleName": "Motion Actuation", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "passed", "score": 100, "maxScore": 100, "weight": 0.0458, "automated": true, "evidence": [{"type": "info", "description": "No motion-triggered functionality detected", "value": "<PERSON> does not appear to use device motion for triggering functions", "severity": "info", "metadata": {"scanDuration": 1378, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-056", "ruleName": "Motion Actuation", "timestamp": "2025-07-11T11:39:03.288Z"}}, "elementCount": 1, "affectedSelectors": ["Page", "does", "not", "appear", "to", "use", "device", "motion", "for", "triggering", "functions"]}], "recommendations": [], "executionTime": 7, "originalScore": 100}, "timestamp": 1752233943288, "hash": "3e016b205b7944e6bd4b76f6819dcbf7", "accessCount": 1, "lastAccessed": 1752233943288, "size": 819}
{"data": [{"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(2)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(2)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1124, "elementsAnalyzed": 7, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 1, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T19:26:50.542Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(3)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(3)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1124, "elementsAnalyzed": 7, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 2, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T19:26:50.542Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(4)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(4)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1124, "elementsAnalyzed": 7, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 3, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T19:26:50.542Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(5)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(5)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1124, "elementsAnalyzed": 7, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 4, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T19:26:50.542Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(6)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(6)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1124, "elementsAnalyzed": 7, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 5, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T19:26:50.543Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(7)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(7)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1124, "elementsAnalyzed": 7, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 6, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T19:26:50.543Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752262010543, "hash": "e2499a1b060174e48a18e229601fcfe1", "accessCount": 1, "lastAccessed": 1752262010543, "size": 4639, "metadata": {"originalKey": "rule:WCAG-047:WCAG-047", "normalizedKey": "rule_wcag-047_wcag-047", "savedAt": 1752262010543, "version": "1.1", "keyHash": "4e081412ce3eef4b50af726215b5e7f4"}}
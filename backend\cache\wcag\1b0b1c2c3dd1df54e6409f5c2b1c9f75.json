{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame 'A362B1B11B79F8E7E1503ABC8B8AFA25'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A362B1B11B79F8E7E1503ABC8B8AFA25"], "metadata": {"scanDuration": 1, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.85, "checkType": "enhanced-error-prevention-analysis", "comprehensiveFormAnalysis": true, "allInputValidation": true, "advancedErrorDetection": true, "preventionMethodValidation": true, "evidenceIndex": 0, "ruleId": "WCAG-066", "ruleName": "Error Prevention Enhanced", "timestamp": "2025-07-11T18:49:43.732Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259783732, "hash": "f7c29f7c76a449ef6ee36e5e301b3732", "accessCount": 1, "lastAccessed": 1752259783732, "size": 850, "metadata": {"originalKey": "WCAG-066:WCAG-066:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-066_wcag-066_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752259783732, "version": "1.1", "keyHash": "e5ac3b8f326e67274fc86593eed8ecd1"}}
{"data": {"ruleId": "WCAG-058", "ruleName": "Motor", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.04, "automated": true, "evidence": [{"type": "measurement", "description": "Interactive element 1 may be too small for motor accessibility", "value": "Size: 1x1px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x1px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 2 may be too small for motor accessibility", "value": "Size: 176.953125x32.375px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x32", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 3 may be too small for motor accessibility", "value": "Size: 108.296875x32.375px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x32", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 4 may be too small for motor accessibility", "value": "Size: 58.640625x32.375px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x32", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 5 may be too small for motor accessibility", "value": "Size: 16.1875x32.375px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x32", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 6 may be too small for motor accessibility", "value": "Size: 34.375x36.375px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x36", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 7 meets minimum target size", "value": "Size: 160x53.640625px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x53", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 8 meets minimum target size", "value": "Size: 67.109375x56px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x56px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 9 meets minimum target size", "value": "Size: 124.171875x56px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x56px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 10 may be too small for motor accessibility", "value": "Size: 16.1875x56px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x56px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 11 meets minimum target size", "value": "Size: 88.46875x56px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x56px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 12 may be too small for motor accessibility", "value": "Size: 16.1875x56px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x56px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 13 meets minimum target size", "value": "Size: 93.765625x56px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x56px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 14 may be too small for motor accessibility", "value": "Size: 16.1875x56px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x56px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 15 meets minimum target size", "value": "Size: 157.328125x56px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x56px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 16 may be too small for motor accessibility", "value": "Size: 16.1875x56px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x56px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 17 meets minimum target size", "value": "Size: 146.96875x48.796875px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x48", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 18 may be too small for motor accessibility", "value": "Size: 138.0625x43.046875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x43", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 19 may be too small for motor accessibility", "value": "Size: 122.453125x38.546875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x38", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 20 may be too small for motor accessibility", "value": "Size: 32x48px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x48px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 21 may be too small for motor accessibility", "value": "Size: 32x48px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x48px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 22 meets minimum target size", "value": "Size: 166.96875x87px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x87px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 23 meets minimum target size", "value": "Size: 223.5x89px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x89px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 24 meets minimum target size", "value": "Size: 161.765625x84.890625px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x84", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 25 meets minimum target size", "value": "Size: 177.875x85px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x85px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 26 meets minimum target size", "value": "Size: 197.5x85px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x85px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 27 meets minimum target size", "value": "Size: 223.5x87px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x87px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 28 meets minimum target size", "value": "Size: 169x87px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x87px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 29 meets minimum target size", "value": "Size: 223.5x89px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x89px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 30 meets minimum target size", "value": "Size: 205.5x87.359375px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x87", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.241Z"}}}, {"type": "measurement", "description": "Interactive element 31 meets minimum target size", "value": "Size: 223.5x89.359375px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x89", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 32 meets minimum target size", "value": "Size: 166.96875x87px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x87px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 33 meets minimum target size", "value": "Size: 223.5x89px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x89px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 34 meets minimum target size", "value": "Size: 161.765625x84.890625px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x84", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 35 meets minimum target size", "value": "Size: 177.875x85px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x85px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 36 meets minimum target size", "value": "Size: 197.5x85px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x85px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 37 meets minimum target size", "value": "Size: 223.5x87px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x87px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 38 meets minimum target size", "value": "Size: 169x87px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x87px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 39 meets minimum target size", "value": "Size: 223.5x89px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x89px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 40 meets minimum target size", "value": "Size: 205.5x87.359375px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x87", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 41 meets minimum target size", "value": "Size: 223.5x89.359375px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x89", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 42 meets minimum target size", "value": "Size: 166.96875x87px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x87px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 43 meets minimum target size", "value": "Size: 223.5x89px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x89px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 44 meets minimum target size", "value": "Size: 161.765625x84.890625px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x84", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 45 meets minimum target size", "value": "Size: 177.875x85px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x85px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 46 meets minimum target size", "value": "Size: 197.5x85px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x85px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 47 meets minimum target size", "value": "Size: 223.5x87px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x87px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 48 meets minimum target size", "value": "Size: 169x87px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x87px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 49 meets minimum target size", "value": "Size: 223.5x89px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x89px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 50 meets minimum target size", "value": "Size: 205.5x87.359375px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x87", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 51 meets minimum target size", "value": "Size: 223.5x89.359375px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x89", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 52 meets minimum target size", "value": "Size: 166.96875x87px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x87px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 53 meets minimum target size", "value": "Size: 223.5x89px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x89px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 54 meets minimum target size", "value": "Size: 161.765625x84.890625px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x84", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 55 meets minimum target size", "value": "Size: 177.875x85px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x85px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 56 meets minimum target size", "value": "Size: 197.5x85px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x85px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 57 meets minimum target size", "value": "Size: 223.5x87px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x87px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 58 may be too small for motor accessibility", "value": "Size: 10x10px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x10px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 59 may be too small for motor accessibility", "value": "Size: 10x10px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x10px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 60 may be too small for motor accessibility", "value": "Size: 10x10px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x10px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 61 may be too small for motor accessibility", "value": "Size: 10x10px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x10px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 62 may be too small for motor accessibility", "value": "Size: 10x10px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x10px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 63 may be too small for motor accessibility", "value": "Size: 10x10px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x10px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 64 meets minimum target size", "value": "Size: 222.796875x168px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x168px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 65 meets minimum target size", "value": "Size: 222.796875x145px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x145px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 64, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 66 meets minimum target size", "value": "Size: 222.796875x168px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x168px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 65, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.242Z"}}}, {"type": "measurement", "description": "Interactive element 67 meets minimum target size", "value": "Size: 222.796875x168px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x168px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 66, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 68 meets minimum target size", "value": "Size: 222.8125x145px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x145px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 67, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 69 may be too small for motor accessibility", "value": "Size: 148.46875x43.171875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x43", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 68, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 70 meets minimum target size", "value": "Size: 589x331.3125px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x331", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 69, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 71 may be too small for motor accessibility", "value": "Size: 32x48px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x48px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 70, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 72 may be too small for motor accessibility", "value": "Size: 32x48px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x48px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 71, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 73 may be too small for motor accessibility", "value": "Size: 88.359375x34.546875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x34", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 72, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 74 may be too small for motor accessibility", "value": "Size: 88.359375x34.546875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x34", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 73, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 75 may be too small for motor accessibility", "value": "Size: 88.359375x34.546875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x34", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 74, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 76 may be too small for motor accessibility", "value": "Size: 88.359375x34.546875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x34", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 75, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 77 may be too small for motor accessibility", "value": "Size: 88.359375x34.546875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x34", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 76, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 78 may be too small for motor accessibility", "value": "Size: 88.359375x34.546875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x34", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 77, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 79 may be too small for motor accessibility", "value": "Size: 88.359375x34.546875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x34", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 78, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 80 may be too small for motor accessibility", "value": "Size: 88.359375x34.546875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x34", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 79, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 81 meets minimum target size", "value": "Size: 433.15625x96px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x96px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 80, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 82 meets minimum target size", "value": "Size: 433.15625x96px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x96px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 81, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 83 meets minimum target size", "value": "Size: 433.15625x96px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x96px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 82, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 84 meets minimum target size", "value": "Size: 433.15625x138px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x138px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 83, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 85 meets minimum target size", "value": "Size: 433.15625x96px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x96px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 84, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 86 may be too small for motor accessibility", "value": "Size: 100.359375x34.546875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x34", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 85, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 87 may be too small for motor accessibility", "value": "Size: 100.359375x34.546875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x34", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 86, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 88 may be too small for motor accessibility", "value": "Size: 100.359375x34.546875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x34", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 87, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 89 may be too small for motor accessibility", "value": "Size: 100.359375x34.546875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x34", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 88, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 90 may be too small for motor accessibility", "value": "Size: 32x48px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x48px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 89, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 91 may be too small for motor accessibility", "value": "Size: 32x48px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x48px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 90, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 92 may be too small for motor accessibility", "value": "Size: 10x10px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x10px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 91, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 93 may be too small for motor accessibility", "value": "Size: 10x10px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x10px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 92, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 94 may be too small for motor accessibility", "value": "Size: 10x10px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x10px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 93, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 95 may be too small for motor accessibility", "value": "Size: 10x10px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x10px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 94, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 96 meets minimum target size", "value": "Size: 696.390625x191.328125px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x191", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 95, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 97 meets minimum target size", "value": "Size: 696.390625x180px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x180px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 96, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 98 meets minimum target size", "value": "Size: 696.390625x187.5px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x187", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 97, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 99 meets minimum target size", "value": "Size: 262.5x138px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x138px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 98, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 100 meets minimum target size", "value": "Size: 262.5x125.578125px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x125", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 99, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 101 meets minimum target size", "value": "Size: 262.5x123.96875px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x123", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 100, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 102 meets minimum target size", "value": "Size: 229.21875x128.703125px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x128", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 101, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 103 meets minimum target size", "value": "Size: 231.703125x129.265625px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x129", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 102, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 104 meets minimum target size", "value": "Size: 243.34375x128.53125px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x128", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 103, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 105 may be too small for motor accessibility", "value": "Size: 176.984375x35.578125px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x35", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 104, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 106 meets minimum target size", "value": "Size: 589x232.96875px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x232", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 105, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 107 may be too small for motor accessibility", "value": "Size: 168.046875x35.578125px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x35", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 106, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 108 meets minimum target size", "value": "Size: 589x285.171875px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x285", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 107, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 109 may be too small for motor accessibility", "value": "Size: 111.5x24px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x24px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 108, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 110 may be too small for motor accessibility", "value": "Size: 57.4375x24px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x24px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 109, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 111 may be too small for motor accessibility", "value": "Size: 57.4375x24px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x24px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 110, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 112 may be too small for motor accessibility", "value": "Size: 212.390625x40px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x40px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 111, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 113 may be too small for motor accessibility", "value": "Size: 148.8125x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 112, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 114 may be too small for motor accessibility", "value": "Size: 212.390625x42px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x42px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 113, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 115 may be too small for motor accessibility", "value": "Size: 136.359375x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 114, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 116 may be too small for motor accessibility", "value": "Size: 144.9375x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 115, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 117 may be too small for motor accessibility", "value": "Size: 95.546875x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 116, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 118 may be too small for motor accessibility", "value": "Size: 85.875x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 117, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.243Z"}}}, {"type": "measurement", "description": "Interactive element 119 may be too small for motor accessibility", "value": "Size: 212.390625x40px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x40px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 118, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 120 may be too small for motor accessibility", "value": "Size: 166.40625x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 119, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 121 may be too small for motor accessibility", "value": "Size: 162.796875x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 120, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 122 may be too small for motor accessibility", "value": "Size: 83.890625x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 121, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 123 may be too small for motor accessibility", "value": "Size: 62.890625x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 122, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 124 may be too small for motor accessibility", "value": "Size: 85.421875x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 123, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 125 may be too small for motor accessibility", "value": "Size: 37.21875x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 124, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 126 may be too small for motor accessibility", "value": "Size: 212.390625x40px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x40px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 125, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 127 may be too small for motor accessibility", "value": "Size: 137.15625x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 126, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 128 may be too small for motor accessibility", "value": "Size: 66.765625x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 127, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 129 may be too small for motor accessibility", "value": "Size: 86.203125x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 128, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 130 may be too small for motor accessibility", "value": "Size: 116.921875x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 129, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 131 may be too small for motor accessibility", "value": "Size: 85.4375x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 130, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 132 may be too small for motor accessibility", "value": "Size: 69.890625x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 131, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 133 may be too small for motor accessibility", "value": "Size: 150.296875x40px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x40px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 132, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 134 may be too small for motor accessibility", "value": "Size: 109.9375x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 133, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 135 may be too small for motor accessibility", "value": "Size: 98.28125x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 134, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 136 may be too small for motor accessibility", "value": "Size: 95.953125x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 135, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 137 may be too small for motor accessibility", "value": "Size: 93.15625x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 136, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 138 may be too small for motor accessibility", "value": "Size: 66x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 137, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 139 may be too small for motor accessibility", "value": "Size: 76.53125x22px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x22px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 138, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 140 may be too small for motor accessibility", "value": "Size: 39.328125x19.1875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 139, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 141 may be too small for motor accessibility", "value": "Size: 62.65625x19.1875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 140, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 142 may be too small for motor accessibility", "value": "Size: 27.328125x19.1875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 141, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 143 may be too small for motor accessibility", "value": "Size: 36.65625x19.1875px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x19", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 142, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 144 meets minimum target size", "value": "Size: 150x46.421875px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x46", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 143, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 145 meets minimum target size", "value": "Size: 150x46.421875px, Meets minimum: true", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x46", "px", "Meets", "minimum", "true"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 144, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 146 may be too small for motor accessibility", "value": "Size: 20x28px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x28px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 145, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 147 may be too small for motor accessibility", "value": "Size: 20x28px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x28px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 146, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 148 may be too small for motor accessibility", "value": "Size: 20x28px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x28px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 147, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 149 may be too small for motor accessibility", "value": "Size: 16x27px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x27px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 148, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 150 may be too small for motor accessibility", "value": "Size: 20x28px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x28px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 149, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 151 may be too small for motor accessibility", "value": "Size: 28.796875x43.1875px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x43", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 150, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 152 may be too small for motor accessibility", "value": "Size: 28.796875x43.1875px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x43", "px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 151, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 153 may be too small for motor accessibility", "value": "Size: 139.828125x36px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x36px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 152, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.244Z"}}}, {"type": "measurement", "description": "Interactive element 154 may be too small for motor accessibility", "value": "Size: 127.71875x36px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x36px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 153, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "measurement", "description": "Interactive element 155 may be too small for motor accessibility", "value": "Size: 139.828125x36px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x36px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 154, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "measurement", "description": "Interactive element 156 may be too small for motor accessibility", "value": "Size: 139.828125x36px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x36px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 155, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "measurement", "description": "Interactive element 157 may be too small for motor accessibility", "value": "Size: 127.71875x36px, Required: 44x44px, Meets minimum: false", "element": "a", "elementCount": 1, "affectedSelectors": ["Size", "x36px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 156, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "measurement", "description": "Interactive element 158 may be too small for motor accessibility", "value": "Size: 56x41px, Required: 44x44px, Meets minimum: false", "element": "button", "elementCount": 1, "affectedSelectors": ["Size", "x41px", "Required", "x44px", "Meets", "minimum", "false"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 157, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Error analyzing gesture requirements", "value": "Error: element.className.includes is not a function\npptr:evaluate;MotorCheck.analyzeGestureRequirements%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cchecks%5Cmotor.ts%3A213%3A48):7:43", "element": "gesture elements", "elementCount": 1, "affectedSelectors": ["Error", "element.className.includes", "is", "not", "a", "function", "pptr", "evaluate", "MotorCheck.analyzeGestureRequirements", "D", "A", "CWeb", "projects", "CCom<PERSON>ly", "Checker", "Cbackend", "Csrc", "Ccompliance", "Cwcag", "Cchecks", "Cmotor.ts", "A213", "A48"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 158, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Error analyzing motion controls", "value": "Error: element.className.toLowerCase is not a function\npptr:evaluate;MotorCheck.analyzeMotionControls%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cchecks%5Cmotor.ts%3A330%3A47):16:57", "element": "motion elements", "elementCount": 1, "affectedSelectors": ["Error", "element.className.toLowerCase", "is", "not", "a", "function", "pptr", "evaluate", "MotorCheck.analyzeMotionControls", "D", "A", "CWeb", "projects", "CCom<PERSON>ly", "Checker", "Cbackend", "Csrc", "Ccompliance", "Cwcag", "Cchecks", "Cmotor.ts", "A330", "A47"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 159, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Timeout element 1 appears to have extend option", "value": "Has extend option: true, Text: if(navigator.useragent.match(/msie|internet explorer/i)||navigator.useragent.match(/trident\\/7\\..*?r", "element": "html", "elementCount": 1, "affectedSelectors": ["Has", "extend", "option", "true", "Text", "if", "navigator.useragent.match", "msie", "internet", "explorer", "i", "trident", "r"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 160, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Timeout element 2 appears to have extend option", "value": "Has extend option: true, Text: if(navigator.useragent.match(/msie|internet explorer/i)||navigator.useragent.match(/trident\\/7\\..*?r", "element": "head", "elementCount": 1, "affectedSelectors": ["Has", "extend", "option", "true", "Text", "if", "navigator.useragent.match", "msie", "internet", "explorer", "i", "trident", "r"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 161, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Timeout element 3 may need extend option", "value": "Has extend option: false, Text: (()=>{class rocketlazyloadscripts{constructor(){this.v=\"*******\",this.triggerevents=[\"keydown\",\"mous", "element": "script", "elementCount": 1, "affectedSelectors": ["Has", "extend", "option", "false", "Text", "class", "rocketlazyloadscripts", "constructor", "this.v", "this.triggerevents", "keydown", "mous"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 162, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Timeout element 4 may need extend option", "value": "Has extend option: false, Text: \n\t/* fix: wp-rocket (application/ld+json) */\n\twindow._vwo_code || (function () {\n\tvar account_id=867", "element": "script", "elementCount": 1, "affectedSelectors": ["Has", "extend", "option", "false", "Text", "fix", "wp-rocket", "application", "ld", "json", "window", "vwo_code", "function", "var", "account_id"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 163, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Timeout element 5 may need extend option", "value": "Has extend option: false, Text: (function(){function _vwo_err(e){function ge(e,a){return\"https://dev.visualwebsiteoptimizer.com/ee.g", "element": "script", "elementCount": 1, "affectedSelectors": ["Has", "extend", "option", "false", "Text", "function", "vwo_err", "e", "ge", "a", "return", "https", "dev.visualwebsiteoptimizer.com", "ee.g"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 164, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Timeout element 6 may need extend option", "value": "Has extend option: false, Text: \n                                $(document).ready(function(){\n                                     ", "element": "script", "elementCount": 1, "affectedSelectors": ["Has", "extend", "option", "false", "Text", "document", "ready", "function"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 165, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Timeout element 7 appears to have extend option", "value": "Has extend option: true, Text: /* for license info, refer to: https://dev.visualwebsiteoptimizer.com/cdn/edrv/license.txt */\n(funct", "element": "script", "elementCount": 1, "affectedSelectors": ["Has", "extend", "option", "true", "Text", "for", "license", "info", "refer", "to", "https", "dev.visualwebsiteoptimizer.com", "cdn", "edrv", "license.txt", "funct"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 166, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Timeout element 8 may need extend option", "value": "Has extend option: false, Text: !function(){\"use strict\";if(window.vwo=window.vwo||[],window.vwo.corelibexecuted)return;window.vwo.c", "element": "script", "elementCount": 1, "affectedSelectors": ["Has", "extend", "option", "false", "Text", "function", "use", "strict", "if", "window.vwo", "window.vwo.corelibexecuted", "return", "window.vwo.c"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 167, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Timeout element 9 may need extend option", "value": "Has extend option: false, Text: \n\n<iframe src=https://www.googletagmanager.com/ns.html?id=gtm-n8sql3v\nheight=\"0\" width=\"0\" style=\"di", "element": "body", "elementCount": 1, "affectedSelectors": ["Has", "extend", "option", "false", "Text", "iframe", "src", "https", "www.googletagmanager.com", "ns.html", "id", "gtm-n8sql3v", "height", "width", "style", "di"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 168, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Timeout element 10 may need extend option", "value": "Has extend option: false, Text: \n\"use strict\";var _createclass=function(){function defineproperties(target,props){for(var i=0;i<prop", "element": "script", "elementCount": 1, "affectedSelectors": ["Has", "extend", "option", "false", "Text", "use", "strict", "var", "createclass", "function", "defineproperties", "target", "props", "for", "i", "prop"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 169, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Timeout element 11 may need extend option", "value": "Has extend option: false, Text: \n(function() {\n\"use strict\";var r=\"function\"==typeof symbol&&\"symbol\"==typeof symbol.iterator?functi", "element": "script", "elementCount": 1, "affectedSelectors": ["Has", "extend", "option", "false", "Text", "function", "use", "strict", "var", "r", "typeof", "symbol", "symbol.iterator", "functi"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 170, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Timeout element 12 may need extend option", "value": "Has extend option: false, Text: (function(b,c,e,f,a,d){a=c.createelement(e);a.onload=f;a.defer=1;a.src=\"https://cdn.jsdelivr.net/gh/", "element": "script", "elementCount": 1, "affectedSelectors": ["Has", "extend", "option", "false", "Text", "function", "b", "c", "e", "f", "a", "d", "c.createelement", "a.onload", "a.defer", "a.src", "https", "cdn.jsdelivr.net", "gh"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 171, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Timeout element 13 may need extend option", "value": "Has extend option: false, Text: datalayer=window.datalayer||[];\n(function(){function c(a,d){var b=\"drift\",h=b;b=\"chat\";a={event:h+\".", "element": "script", "elementCount": 1, "affectedSelectors": ["Has", "extend", "option", "false", "Text", "datalayer", "window.datalayer", "function", "c", "a", "d", "var", "b", "drift", "h", "chat", "event"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 172, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}, {"type": "interaction", "description": "Timeout element 14 may need extend option", "value": "Has extend option: false, Text: (function(){function k(a,c,d,b){var f=\"\";d&&(f=new date,f.settime(f.gettime()+d*24*60*60*1e3),f=\"; e", "element": "script", "elementCount": 1, "affectedSelectors": ["Has", "extend", "option", "false", "Text", "function", "k", "a", "c", "d", "b", "var", "f", "new", "date", "f.settime", "f.gettime", "e3", "e"], "metadata": {"scanDuration": 1382, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 173, "ruleId": "WCAG-058", "ruleName": "Motor", "timestamp": "2025-07-11T10:45:47.245Z"}}}], "recommendations": ["Increase size of a element 1 to at least 44x44px", "Increase size of a element 2 to at least 44x44px", "Increase size of a element 3 to at least 44x44px", "Increase size of a element 4 to at least 44x44px", "Increase size of button element 5 to at least 44x44px", "Increase size of a element 6 to at least 44x44px", "Increase size of button element 10 to at least 44x44px", "Increase size of button element 12 to at least 44x44px", "Increase size of button element 14 to at least 44x44px", "Increase size of button element 16 to at least 44x44px", "Increase size of a element 18 to at least 44x44px", "Increase size of a element 19 to at least 44x44px", "Increase size of button element 20 to at least 44x44px", "Increase size of button element 21 to at least 44x44px", "Increase size of button element 58 to at least 44x44px", "Increase size of button element 59 to at least 44x44px", "Increase size of button element 60 to at least 44x44px", "Increase size of button element 61 to at least 44x44px", "Increase size of button element 62 to at least 44x44px", "Increase size of button element 63 to at least 44x44px", "Increase size of a element 69 to at least 44x44px", "Increase size of button element 71 to at least 44x44px", "Increase size of button element 72 to at least 44x44px", "Increase size of a element 73 to at least 44x44px", "Increase size of a element 74 to at least 44x44px", "Increase size of a element 75 to at least 44x44px", "Increase size of a element 76 to at least 44x44px", "Increase size of a element 77 to at least 44x44px", "Increase size of a element 78 to at least 44x44px", "Increase size of a element 79 to at least 44x44px", "Increase size of a element 80 to at least 44x44px", "Increase size of a element 86 to at least 44x44px", "Increase size of a element 87 to at least 44x44px", "Increase size of a element 88 to at least 44x44px", "Increase size of a element 89 to at least 44x44px", "Increase size of button element 90 to at least 44x44px", "Increase size of button element 91 to at least 44x44px", "Increase size of button element 92 to at least 44x44px", "Increase size of button element 93 to at least 44x44px", "Increase size of button element 94 to at least 44x44px", "Increase size of button element 95 to at least 44x44px", "Increase size of a element 105 to at least 44x44px", "Increase size of a element 107 to at least 44x44px", "Increase size of a element 109 to at least 44x44px", "Increase size of a element 110 to at least 44x44px", "Increase size of a element 111 to at least 44x44px", "Increase size of a element 112 to at least 44x44px", "Increase size of a element 113 to at least 44x44px", "Increase size of a element 114 to at least 44x44px", "Increase size of a element 115 to at least 44x44px", "Increase size of a element 116 to at least 44x44px", "Increase size of a element 117 to at least 44x44px", "Increase size of a element 118 to at least 44x44px", "Increase size of a element 119 to at least 44x44px", "Increase size of a element 120 to at least 44x44px", "Increase size of a element 121 to at least 44x44px", "Increase size of a element 122 to at least 44x44px", "Increase size of a element 123 to at least 44x44px", "Increase size of a element 124 to at least 44x44px", "Increase size of a element 125 to at least 44x44px", "Increase size of a element 126 to at least 44x44px", "Increase size of a element 127 to at least 44x44px", "Increase size of a element 128 to at least 44x44px", "Increase size of a element 129 to at least 44x44px", "Increase size of a element 130 to at least 44x44px", "Increase size of a element 131 to at least 44x44px", "Increase size of a element 132 to at least 44x44px", "Increase size of a element 133 to at least 44x44px", "Increase size of a element 134 to at least 44x44px", "Increase size of a element 135 to at least 44x44px", "Increase size of a element 136 to at least 44x44px", "Increase size of a element 137 to at least 44x44px", "Increase size of a element 138 to at least 44x44px", "Increase size of a element 139 to at least 44x44px", "Increase size of a element 140 to at least 44x44px", "Increase size of a element 141 to at least 44x44px", "Increase size of a element 142 to at least 44x44px", "Increase size of a element 143 to at least 44x44px", "Increase size of a element 146 to at least 44x44px", "Increase size of a element 147 to at least 44x44px", "Increase size of a element 148 to at least 44x44px", "Increase size of a element 149 to at least 44x44px", "Increase size of a element 150 to at least 44x44px", "Increase size of button element 151 to at least 44x44px", "Increase size of button element 152 to at least 44x44px", "Increase size of a element 153 to at least 44x44px", "Increase size of a element 154 to at least 44x44px", "Increase size of a element 155 to at least 44x44px", "Increase size of a element 156 to at least 44x44px", "Increase size of a element 157 to at least 44x44px", "Increase size of button element 158 to at least 44x44px", "Check gesture requirements manually", "Check motion controls manually"], "executionTime": 899, "originalScore": 40}, "timestamp": 1752230747245, "hash": "10dc8183bbf551207bb90cc24ea48ed3", "accessCount": 1, "lastAccessed": 1752230747245, "size": 94003}
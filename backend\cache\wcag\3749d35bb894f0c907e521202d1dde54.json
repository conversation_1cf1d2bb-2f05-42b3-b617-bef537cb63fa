{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "metadata": {"scanDuration": 66, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.5, "checkType": "pronunciation-analysis", "pronunciationGuidanceDetection": true, "ambiguousWordIdentification": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T18:03:38.341Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752257018341, "hash": "be9b116b064ecafb2f7b08b901423e74", "accessCount": 1, "lastAccessed": 1752257018341, "size": 832, "metadata": {"originalKey": "WCAG-063:WCAG-063:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-063_wcag-063_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752257018342, "version": "1.1", "keyHash": "598a624515a6545184d9813fac543eb3"}}
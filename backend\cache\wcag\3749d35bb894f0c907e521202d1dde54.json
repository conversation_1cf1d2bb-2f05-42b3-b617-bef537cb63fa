{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame 'A362B1B11B79F8E7E1503ABC8B8AFA25'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A362B1B11B79F8E7E1503ABC8B8AFA25"], "metadata": {"scanDuration": 2, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.5, "checkType": "pronunciation-analysis", "pronunciationGuidanceDetection": true, "ambiguousWordIdentification": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T18:49:40.499Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259780499, "hash": "b12d55933ac402942ed7c7b36cfd0a8b", "accessCount": 1, "lastAccessed": 1752259780499, "size": 832, "metadata": {"originalKey": "WCAG-063:WCAG-063:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-063_wcag-063_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752259780501, "version": "1.1", "keyHash": "598a624515a6545184d9813fac543eb3"}}
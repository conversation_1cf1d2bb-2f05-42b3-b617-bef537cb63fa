{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame 'A362B1B11B79F8E7E1503ABC8B8AFA25'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A362B1B11B79F8E7E1503ABC8B8AFA25"], "metadata": {"scanDuration": 2, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "navigation-method-analysis", "navigationAnalysis": true, "methodDetection": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-035", "ruleName": "Multiple Ways", "timestamp": "2025-07-11T18:50:01.324Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259801324, "hash": "3a3ae1f8efea237ae432303729056566", "accessCount": 1, "lastAccessed": 1752259801324, "size": 813, "metadata": {"originalKey": "WCAG-035:WCAG-035:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-035_wcag-035_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752259801338, "version": "1.1", "keyHash": "4f968c960a6ce93ba83e6c3c3237f422"}}
{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "metadata": {"scanDuration": 7, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "navigation-method-analysis", "navigationAnalysis": true, "methodDetection": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-035", "ruleName": "Multiple Ways", "timestamp": "2025-07-11T18:04:02.720Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752257042720, "hash": "efff8aea5a561fb4fad6b21e1de5b0c2", "accessCount": 1, "lastAccessed": 1752257042720, "size": 812, "metadata": {"originalKey": "WCAG-035:WCAG-035:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-035_wcag-035_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752257042721, "version": "1.1", "keyHash": "4f968c960a6ce93ba83e6c3c3237f422"}}
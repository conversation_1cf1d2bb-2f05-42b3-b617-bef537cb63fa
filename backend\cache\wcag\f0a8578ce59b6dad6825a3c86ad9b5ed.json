{"data": {"ruleId": "WCAG-008", "ruleName": "Error Identification", "category": "understandable", "wcagVersion": "2.2", "successCriterion": "0.0.0", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.1, "automated": false, "evidence": [], "recommendations": ["Manual review required"], "executionTime": 0, "errorMessage": "Manual review check failed: Cannot read properties of undefined (reading 'getElementSelector')\npptr:evaluate;FormAccessibilityAnalyzer.getForms%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cutils%5Cform-accessibility-analyzer.ts%3A434%3A27):7:44", "manualReviewItems": []}, "timestamp": 1752230714433, "hash": "f68d1d74fd77bf015372ff202cd7ca3f", "accessCount": 1, "lastAccessed": 1752230714433, "size": 616}
{"data": [{"type": "text", "description": "Non-text content analysis summary", "value": "0/19 elements pass automated checks, 44 require manual review", "severity": "error", "elementCount": 1, "affectedSelectors": ["elements", "pass", "automated", "checks", "require", "manual", "review"], "fixExample": {"before": "Current implementation", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 0, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.226Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TigerConnect-Clinical-Healthcare-Communications-Logo\" - accuracy verification needed", "selector": "img:nth-child(1)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(1)", "Alt", "text", "present", "TigerConnect-Clinical-Healthcare-Communications-Logo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 1, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.226Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(2)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(2)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 2, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.226Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(3)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(3)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 3, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.227Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(4)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(4)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 4, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.227Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(5)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(5)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 5, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.227Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(6)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(6)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 6, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.227Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(7)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(7)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 7, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.227Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(8)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(8)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 8, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.227Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(9)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(9)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 9, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.228Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(10)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(10)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 10, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.228Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(11)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(11)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 11, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.228Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(12)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(12)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 12, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.229Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(13)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(13)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 13, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.229Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(14)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(14)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 14, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.230Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(15)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(15)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 15, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.230Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(16)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(16)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 16, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.230Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(17)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(17)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 17, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.231Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TigerConnect-Clinical-Healthcare-Communications-Logo\" - accuracy verification needed", "selector": "img:nth-child(18)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(18)", "Alt", "text", "present", "TigerConnect-Clinical-Healthcare-Communications-Logo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 18, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.232Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Ambulance white\" - accuracy verification needed", "selector": "img:nth-child(19)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(19)", "Alt", "text", "present", "Ambulance", "white", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 19, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.233Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"group message white\" - accuracy verification needed", "selector": "img:nth-child(20)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(20)", "Alt", "text", "present", "group", "message", "white", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 20, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.233Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"group 94\" - accuracy verification needed", "selector": "img:nth-child(21)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(21)", "Alt", "text", "present", "group", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 21, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.234Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Alarm Mgt 1w\" - accuracy verification needed", "selector": "img:nth-child(22)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(22)", "Alt", "text", "present", "Alarm", "Mgt", "w", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 22, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.234Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"group 93\" - accuracy verification needed", "selector": "img:nth-child(23)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(23)", "Alt", "text", "present", "group", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 23, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.234Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"CareConduit logo\" - accuracy verification needed", "selector": "img:nth-child(24)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(24)", "Alt", "text", "present", "CareConduit", "logo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 24, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.234Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Critical Response\" - accuracy verification needed", "selector": "img:nth-child(25)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(25)", "Alt", "text", "present", "Workflow", "Critical", "Response", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 25, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.234Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Emergency Department\" - accuracy verification needed", "selector": "img:nth-child(26)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(26)", "Alt", "text", "present", "Workflow", "Emergency", "Department", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 26, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.234Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Inpatient Care\" - accuracy verification needed", "selector": "img:nth-child(27)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(27)", "Alt", "text", "present", "Workflow", "Inpatient", "Care", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 27, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.235Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Operating Room\" - accuracy verification needed", "selector": "img:nth-child(28)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(28)", "Alt", "text", "present", "Workflow", "Operating", "Room", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 28, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.235Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Post Acute Ambulatory\" - accuracy verification needed", "selector": "img:nth-child(29)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(29)", "Alt", "text", "present", "Workflow", "Post", "Acute", "Ambulatory", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 29, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.235Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"blog header why healthcare needs purpose built collaboration solutions\" - accuracy verification needed", "selector": "img:nth-child(30)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(30)", "Alt", "text", "present", "blog", "header", "why", "healthcare", "needs", "purpose", "built", "collaboration", "solutions", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 30, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.235Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo CapHealth G\" - accuracy verification needed", "selector": "img:nth-child(31)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(31)", "Alt", "text", "present", "Logo", "CapHealth", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 31, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.235Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo CommonSpirit G\" - accuracy verification needed", "selector": "img:nth-child(32)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(32)", "Alt", "text", "present", "Logo", "CommonSpirit", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 32, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.235Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo Geisinger G\" - accuracy verification needed", "selector": "img:nth-child(33)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(33)", "Alt", "text", "present", "Logo", "<PERSON><PERSON><PERSON><PERSON>", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 33, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.235Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo Innovation G\" - accuracy verification needed", "selector": "img:nth-child(34)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(34)", "Alt", "text", "present", "Logo", "Innovation", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 34, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.235Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo UMMC G\" - accuracy verification needed", "selector": "img:nth-child(35)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(35)", "Alt", "text", "present", "Logo", "UMMC", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 35, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.236Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"whclgoo\" - accuracy verification needed", "selector": "img:nth-child(36)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(36)", "Alt", "text", "present", "whclgoo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 36, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.236Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Doctors with tablet\" - accuracy verification needed", "selector": "img:nth-child(37)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(37)", "Alt", "text", "present", "Doctors", "with", "tablet", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 37, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.236Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Homepage SS 2\" - accuracy verification needed", "selector": "img:nth-child(38)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(38)", "Alt", "text", "present", "Homepage", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 38, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.236Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Physician Scheduling Product SS\" - accuracy verification needed", "selector": "img:nth-child(39)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(39)", "Alt", "text", "present", "Physician", "Scheduling", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 39, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.236Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Clinical Collaboration Product SS\" - accuracy verification needed", "selector": "img:nth-child(40)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(40)", "Alt", "text", "present", "Clinical", "Collaboration", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 40, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.236Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"AMEN Product SS\" - accuracy verification needed", "selector": "img:nth-child(41)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(41)", "Alt", "text", "present", "AMEN", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 41, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.236Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"PE Product SS\" - accuracy verification needed", "selector": "img:nth-child(42)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(42)", "Alt", "text", "present", "PE", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 42, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.236Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Users Physicians\" - accuracy verification needed", "selector": "img:nth-child(43)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(43)", "Alt", "text", "present", "Users", "Physicians", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 43, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.236Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Users IT\" - accuracy verification needed", "selector": "img:nth-child(44)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(44)", "Alt", "text", "present", "Users", "IT", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 44, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.236Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Users Nurse1\" - accuracy verification needed", "selector": "img:nth-child(45)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(45)", "Alt", "text", "present", "Users", "Nurse1", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 45, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.236Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"ICO\" - accuracy verification needed", "selector": "img:nth-child(46)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(46)", "Alt", "text", "present", "ICO", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 46, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.237Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"2024 best in klas recognition\" - accuracy verification needed", "selector": "img:nth-child(47)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(47)", "Alt", "text", "present", "best", "in", "klas", "recognition", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 47, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.237Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"g2 logo png\" - accuracy verification needed", "selector": "img:nth-child(48)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(48)", "Alt", "text", "present", "g2", "logo", "png", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 48, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.237Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"<PERSON><PERSON>ner logo blue small digital\" - accuracy verification needed", "selector": "img:nth-child(49)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(49)", "Alt", "text", "present", "<PERSON><PERSON><PERSON>", "logo", "blue", "small", "digital", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 49, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.237Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"RSC 04\" - accuracy verification needed", "selector": "img:nth-child(50)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(50)", "Alt", "text", "present", "RSC", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 50, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.237Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Webinar Preview Transform Pre Hospital and Transfer Communication\" - accuracy verification needed", "selector": "img:nth-child(51)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(51)", "Alt", "text", "present", "Webinar", "Preview", "Transform", "Pre", "Hospital", "and", "Transfer", "Communication", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 51, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.237Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"resources inforgraphic\" - accuracy verification needed", "selector": "img:nth-child(52)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(52)", "Alt", "text", "present", "resources", "inforgraphic", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 52, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.237Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Case Study Tufts Medical Center Preview\" - accuracy verification needed", "selector": "img:nth-child(53)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(53)", "Alt", "text", "present", "Case", "Study", "Tufts", "Medical", "Center", "Preview", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 53, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.237Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"RSC 02\" - accuracy verification needed", "selector": "img:nth-child(54)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(54)", "Alt", "text", "present", "RSC", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 54, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.238Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Blog Expanding Care Beyong Hospital Walls Preview\" - accuracy verification needed", "selector": "img:nth-child(55)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(55)", "Alt", "text", "present", "Blog", "Expanding", "Care", "<PERSON><PERSON>", "Hospital", "Walls", "Preview", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 55, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.238Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Beckers 2024 report state of healthcare collaboration how communication inefficiencies impact clinician productivity 11.08.57 AM 1\" - accuracy verification needed", "selector": "img:nth-child(56)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(56)", "Alt", "text", "present", "<PERSON><PERSON>", "report", "state", "of", "healthcare", "collaboration", "how", "communication", "inefficiencies", "impact", "clinician", "productivity", "AM", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 56, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.238Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Alt text too short to be descriptive", "selector": "img:nth-child(57)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(57)", "Alt", "text", "too", "short", "to", "be", "descriptive"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 57, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.238Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TigerConnect-Clinical-Healthcare-Communications-Logo-R\" - accuracy verification needed", "selector": "img:nth-child(58)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(58)", "Alt", "text", "present", "TigerConnect-Clinical-Healthcare-Communications-Logo-R", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 58, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.238Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Google-Play-App-Clinical-Collaboration-Apple-App-TigerConnect\" - accuracy verification needed", "selector": "img:nth-child(59)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(59)", "Alt", "text", "present", "Google-Play-App-Clinical-Collaboration-Apple-App-TigerConnect", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 64, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 59, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.238Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259479240, "hash": "88e8ecfac43856f0b0015e48c705c7f6", "accessCount": 1, "lastAccessed": 1752259479240, "size": 57158, "metadata": {"originalKey": "WCAG-001:WCAG-001:dGV4dDpOb24tdGV4dCBjb250ZW50IGFu", "normalizedKey": "wcag-001_wcag-001_dgv4ddpob24tdgv4dcbjb250zw50igfu", "savedAt": 1752259479243, "version": "1.1", "keyHash": "145746fca6164d6b49a2dbcd9d2e023e"}}
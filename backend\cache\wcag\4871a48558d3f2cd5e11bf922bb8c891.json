{"data": {"ruleId": "WCAG-056", "ruleName": "Motion Actuation", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "passed", "score": 100, "maxScore": 100, "weight": 0.0458, "automated": true, "evidence": [{"type": "info", "description": "No motion-triggered functionality detected", "value": "<PERSON> does not appear to use device motion for triggering functions", "severity": "info", "metadata": {"scanDuration": 360, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-056", "ruleName": "Motion Actuation", "timestamp": "2025-07-11T20:08:20.001Z"}}, "elementCount": 1, "affectedSelectors": ["Page", "does", "not", "appear", "to", "use", "device", "motion", "for", "triggering", "functions"]}], "recommendations": [], "executionTime": 14, "originalScore": 100, "thresholdApplied": 75, "scoringDetails": "100.0% (threshold: 75%) - PASSED"}, "timestamp": 1752264500001, "hash": "954381f137b9a4aa3f624e9a20c04491", "accessCount": 1, "lastAccessed": 1752264500001, "size": 893, "metadata": {"originalKey": "WCAG-056:053b13d2:add92319", "normalizedKey": "wcag-056_053b13d2_add92319", "savedAt": 1752264500001, "version": "1.1", "keyHash": "2e2617f222f1c5aded9bb5f78617dce7"}}
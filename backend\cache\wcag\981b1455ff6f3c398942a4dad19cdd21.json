{"data": [{"type": "text", "description": "Reading level analysis", "value": "Overall reading level: Graduate (17th grade and above) (Grade 65.8)", "elementCount": 1, "affectedSelectors": ["Overall", "reading", "level", "Graduate", "th", "grade", "and", "above", "Grade"], "severity": "error", "metadata": {"scanDuration": 152, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.55, "checkType": "reading-level-analysis", "textComplexityAnalysis": true, "readabilityScoring": true, "aiContentAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T19:24:14.789Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Complex text block (Grade 67.2)", "value": "\"Skip to content\n\t\t\n\t\n\t\t\n\t\t\t\n\t\t\t\t\n\t\t\t\t\t\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\n\t\t\t\t\n\t\t\tContact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Sch...\"", "selector": "#wrapper", "severity": "error", "metadata": {"scanDuration": 152, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.55, "checkType": "reading-level-analysis", "textComplexityAnalysis": true, "readabilityScoring": true, "aiContentAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T19:24:14.789Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "<PERSON><PERSON>", "to", "content", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Sch"]}, {"type": "text", "description": "Complex text block (Grade 80.1)", "value": "\"Contact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Scheduling\n\tTigerConnect Community\n\n\n\n\t\t\t\t\t\tSearch for:Search Button\t\t\n\t\n\t\n\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\n\t\n\n\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t\t...\"", "selector": "#main-header", "severity": "error", "metadata": {"scanDuration": 152, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.55, "checkType": "reading-level-analysis", "textComplexityAnalysis": true, "readabilityScoring": true, "aiContentAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T19:24:14.789Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#main-header", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Scheduling", "Community", "Search", "for", "<PERSON><PERSON>"]}, {"type": "text", "description": "Complex text block (Grade 80.1)", "value": "\"Contact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Scheduling\n\tTigerConnect Community\n\n\n\n\t\t\t\t\t\tSearch for:Search Button\t\t\n\t\n\t\n\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\n\t\n\n\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t\t...\"", "selector": ".site-header-inner-wrap", "severity": "error", "metadata": {"scanDuration": 152, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.55, "checkType": "reading-level-analysis", "textComplexityAnalysis": true, "readabilityScoring": true, "aiContentAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T19:24:14.789Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".site-header-inner-wrap", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Scheduling", "Community", "Search", "for", "<PERSON><PERSON>"]}, {"type": "text", "description": "Complex text block (Grade 80.1)", "value": "\"Contact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Scheduling\n\tTigerConnect Community\n\n\n\n\t\t\t\t\t\tSearch for:Search Button\t\t\n\t\n\t\n\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\n\t\n\n\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t\t...\"", "selector": ".site-header-upper-wrap", "severity": "error", "metadata": {"scanDuration": 152, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.55, "checkType": "reading-level-analysis", "textComplexityAnalysis": true, "readabilityScoring": true, "aiContentAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T19:24:14.789Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".site-header-upper-wrap", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Scheduling", "Community", "Search", "for", "<PERSON><PERSON>"]}, {"type": "text", "description": "Complex text block (Grade 80.1)", "value": "\"Contact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Scheduling\n\tTigerConnect Community\n\n\n\n\t\t\t\t\t\tSearch for:Search Button\t\t\n\t\n\t\n\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\n\t\n\n\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t\t...\"", "selector": ".site-header-upper-inner-wrap", "severity": "error", "metadata": {"scanDuration": 152, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.55, "checkType": "reading-level-analysis", "textComplexityAnalysis": true, "readabilityScoring": true, "aiContentAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T19:24:14.789Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".site-header-upper-inner-wrap", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Scheduling", "Community", "Search", "for", "<PERSON><PERSON>"]}], "timestamp": 1752261854789, "hash": "2f61b76333403cc13ba4552f6e713d2f", "accessCount": 1, "lastAccessed": 1752261854789, "size": 6359, "metadata": {"originalKey": "rule:WCAG-062:WCAG-062", "normalizedKey": "rule_wcag-062_wcag-062", "savedAt": 1752261854790, "version": "1.1", "keyHash": "2d940584d6e984efc1a0705346d9c99d"}}
{"data": {"ruleId": "WCAG-062", "ruleName": "Reading Level", "category": "understandable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0305, "automated": true, "evidence": [{"type": "text", "description": "Reading level analysis", "value": "Overall reading level: Graduate (17th grade and above) (Grade 65.8)", "elementCount": 1, "affectedSelectors": ["Overall", "reading", "level", "Graduate", "th", "grade", "and", "above", "Grade"], "severity": "error", "metadata": {"scanDuration": 341, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T10:46:47.332Z"}}}, {"type": "text", "description": "Complex text block (Grade 67.2)", "value": "\"Skip to content\n\t\t\n\t\n\t\t\n\t\t\t\n\t\t\t\t\n\t\t\t\t\t\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\n\t\t\t\t\n\t\t\tContact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Sch...\"", "selector": "#wrapper", "severity": "error", "metadata": {"scanDuration": 341, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T10:46:47.332Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "<PERSON><PERSON>", "to", "content", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Sch"]}, {"type": "text", "description": "Complex text block (Grade 80.1)", "value": "\"Contact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Scheduling\n\tTigerConnect Community\n\n\n\n\t\t\t\t\t\tSearch for:Search Button\t\t\n\t\n\t\n\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\n\t\n\n\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t\t...\"", "selector": "#main-header", "severity": "error", "metadata": {"scanDuration": 341, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T10:46:47.332Z"}}, "elementCount": 1, "affectedSelectors": ["#main-header", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Scheduling", "Community", "Search", "for", "<PERSON><PERSON>"]}, {"type": "text", "description": "Complex text block (Grade 80.1)", "value": "\"Contact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Scheduling\n\tTigerConnect Community\n\n\n\n\t\t\t\t\t\tSearch for:Search Button\t\t\n\t\n\t\n\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\n\t\n\n\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t\t...\"", "selector": ".site-header-inner-wrap", "severity": "error", "metadata": {"scanDuration": 341, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T10:46:47.332Z"}}, "elementCount": 1, "affectedSelectors": [".site-header-inner-wrap", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Scheduling", "Community", "Search", "for", "<PERSON><PERSON>"]}, {"type": "text", "description": "Complex text block (Grade 80.1)", "value": "\"Contact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Scheduling\n\tTigerConnect Community\n\n\n\n\t\t\t\t\t\tSearch for:Search Button\t\t\n\t\n\t\n\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\n\t\n\n\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t\t...\"", "selector": ".site-header-upper-wrap", "severity": "error", "metadata": {"scanDuration": 341, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T10:46:47.332Z"}}, "elementCount": 1, "affectedSelectors": [".site-header-upper-wrap", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Scheduling", "Community", "Search", "for", "<PERSON><PERSON>"]}, {"type": "text", "description": "Complex text block (Grade 80.1)", "value": "\"Contact Sales: (800) 572-0470\nContact Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Scheduling\n\tTigerConnect Community\n\n\n\n\t\t\t\t\t\tSearch for:Search Button\t\t\n\t\n\t\n\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\n\t\n\n\n\t\n\t\t\t\t\n\t\t\t\n\t\t\t\t...\"", "selector": ".site-header-upper-inner-wrap", "severity": "error", "metadata": {"scanDuration": 341, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T10:46:47.332Z"}}, "elementCount": 1, "affectedSelectors": [".site-header-upper-inner-wrap", "Contact", "Sales", "Support", "LoginExpand", "TigerConnect", "Physician", "Scheduling", "Community", "Search", "for", "<PERSON><PERSON>"]}], "recommendations": ["Simplify sentence structure and use shorter sentences", "Replace complex words with simpler alternatives when possible", "Consider providing a summary or simplified version of complex content", "Use bullet points and lists to break up complex information", "Test content with actual users to ensure comprehension", "CRITICAL: Content may be inaccessible to many users due to complexity", "Consider providing alternative formats (audio, video, infographics)", "Use plain language principles for better accessibility", "Consider your target audience's reading level and education background"], "executionTime": 175, "originalScore": 40}, "timestamp": 1752230807332, "hash": "0e27a1f2ee368c787861c69e93dcf7e5", "accessCount": 1, "lastAccessed": 1752230807332, "size": 5450}
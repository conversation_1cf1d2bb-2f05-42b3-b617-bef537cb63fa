{"data": {"ruleId": "WCAG-001", "ruleName": "Non-text Content", "category": "perceivable", "wcagVersion": "2.2", "successCriterion": "0.0.0", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.08, "automated": false, "evidence": [{"type": "text", "description": "Non-text content analysis summary", "value": "70/86 elements pass automated checks, 46 require manual review", "severity": "warning", "elementCount": 70, "affectedSelectors": ["elements", "pass", "automated", "checks", "require", "manual", "review"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.432Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TigerConnect-Clinical-Healthcare-Communications-Logo\" - accuracy verification needed", "selector": "img.custom-logo", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.custom-logo", "Alt", "text", "present", "TigerConnect-Clinical-Healthcare-Communications-Logo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.432Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kb-img.wp-image-513", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-513", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.432Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-541", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-541", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.432Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-1075", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1075", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.432Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-542", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-542", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.432Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kb-img.wp-image-18947", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-18947", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.432Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-541", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-541", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.432Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-1075", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1075", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.432Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-542", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-542", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.432Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kb-img.wp-image-1088", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-1088", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.432Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-541", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-541", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.432Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-1075", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1075", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-542", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-542", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kb-img.wp-image-1089", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-1089", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-541", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-541", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-1075", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1075", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "img.kt-info-box-image.wp-image-542", "severity": "info", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-542", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TigerConnect-Clinical-Healthcare-Communications-Logo\" - accuracy verification needed", "selector": "img.custom-logo", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.custom-logo", "Alt", "text", "present", "TigerConnect-Clinical-Healthcare-Communications-Logo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Ambulance white\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-12053", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-12053", "Alt", "text", "present", "Ambulance", "white", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"group message white\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-9231", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-9231", "Alt", "text", "present", "group", "message", "white", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"group 94\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-9234", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-9234", "Alt", "text", "present", "group", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Alarm Mgt 1w\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-15420", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-15420", "Alt", "text", "present", "Alarm", "Mgt", "w", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"group 93\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-9233", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-9233", "Alt", "text", "present", "group", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"CareConduit logo\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-12200.kt-info-svg-image", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-12200.kt-info-svg-image", "Alt", "text", "present", "CareConduit", "logo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Critical Response\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-1271", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1271", "Alt", "text", "present", "Workflow", "Critical", "Response", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Emergency Department\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-1272", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1272", "Alt", "text", "present", "Workflow", "Emergency", "Department", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Inpatient Care\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-1273", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1273", "Alt", "text", "present", "Workflow", "Inpatient", "Care", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Operating Room\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-1274", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1274", "Alt", "text", "present", "Workflow", "Operating", "Room", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Post Acute Ambulatory\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-1275", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1275", "Alt", "text", "present", "Workflow", "Post", "Acute", "Ambulatory", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"blog header why healthcare needs purpose built collaboration solutions\" - accuracy verification needed", "selector": "img.kadence-video-poster.wp-image-6605", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kadence-video-poster.wp-image-6605", "Alt", "text", "present", "blog", "header", "why", "healthcare", "needs", "purpose", "built", "collaboration", "solutions", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo CapHealth G\" - accuracy verification needed", "selector": "img.wp-image-702.skip-lazy", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.wp-image-702.skip-lazy", "Alt", "text", "present", "Logo", "CapHealth", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo CommonSpirit G\" - accuracy verification needed", "selector": "img.wp-image-703.skip-lazy", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.wp-image-703.skip-lazy", "Alt", "text", "present", "Logo", "CommonSpirit", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo Geisinger G\" - accuracy verification needed", "selector": "img.wp-image-704.skip-lazy", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.wp-image-704.skip-lazy", "Alt", "text", "present", "Logo", "<PERSON><PERSON><PERSON><PERSON>", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo Innovation G\" - accuracy verification needed", "selector": "img.wp-image-705.skip-lazy", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.wp-image-705.skip-lazy", "Alt", "text", "present", "Logo", "Innovation", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo UMMC G\" - accuracy verification needed", "selector": "img.wp-image-708.skip-lazy", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.wp-image-708.skip-lazy", "Alt", "text", "present", "Logo", "UMMC", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"whclgoo\" - accuracy verification needed", "selector": "img.wp-image-709.skip-lazy", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.wp-image-709.skip-lazy", "Alt", "text", "present", "whclgoo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Doctors with tablet\" - accuracy verification needed", "selector": "img.kb-img.wp-image-21766", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-21766", "Alt", "text", "present", "Doctors", "with", "tablet", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.433Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Homepage SS 2\" - accuracy verification needed", "selector": "img.kb-img.wp-image-21735", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-21735", "Alt", "text", "present", "Homepage", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Physician Scheduling Product SS\" - accuracy verification needed", "selector": "img.kb-img.wp-image-21759", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-21759", "Alt", "text", "present", "Physician", "Scheduling", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Clinical Collaboration Product SS\" - accuracy verification needed", "selector": "img.kb-img.wp-image-21758", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-21758", "Alt", "text", "present", "Clinical", "Collaboration", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"AMEN Product SS\" - accuracy verification needed", "selector": "img.kb-img.wp-image-21791", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-21791", "Alt", "text", "present", "AMEN", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"PE Product SS\" - accuracy verification needed", "selector": "img.kb-img.wp-image-21790", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-21790", "Alt", "text", "present", "PE", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Users Physicians\" - accuracy verification needed", "selector": "img.kb-img.wp-image-1138", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-1138", "Alt", "text", "present", "Users", "Physicians", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Users IT\" - accuracy verification needed", "selector": "img.kb-img.wp-image-1136", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-1136", "Alt", "text", "present", "Users", "IT", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Users Nurse1\" - accuracy verification needed", "selector": "img.kb-img.wp-image-1137", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-1137", "Alt", "text", "present", "Users", "Nurse1", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"ICO\" - accuracy verification needed", "selector": "img.kb-img.wp-image-1148", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-1148", "Alt", "text", "present", "ICO", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"2024 best in klas recognition\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-20332", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-20332", "Alt", "text", "present", "best", "in", "klas", "recognition", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"g2 logo png\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-20321", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-20321", "Alt", "text", "present", "g2", "logo", "png", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"<PERSON><PERSON>ner logo blue small digital\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-20906", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-20906", "Alt", "text", "present", "<PERSON><PERSON><PERSON>", "logo", "blue", "small", "digital", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"RSC 04\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-1388", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1388", "Alt", "text", "present", "RSC", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Webinar Preview Transform Pre Hospital and Transfer Communication\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-21616", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-21616", "Alt", "text", "present", "Webinar", "Preview", "Transform", "Pre", "Hospital", "and", "Transfer", "Communication", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"resources inforgraphic\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-5852", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-5852", "Alt", "text", "present", "resources", "inforgraphic", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Case Study Tufts Medical Center Preview\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-20048", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-20048", "Alt", "text", "present", "Case", "Study", "Tufts", "Medical", "Center", "Preview", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"RSC 02\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-1386", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-1386", "Alt", "text", "present", "RSC", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Blog Expanding Care Beyong Hospital Walls Preview\" - accuracy verification needed", "selector": "img.kt-info-box-image.wp-image-21615", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kt-info-box-image.wp-image-21615", "Alt", "text", "present", "Blog", "Expanding", "Care", "<PERSON><PERSON>", "Hospital", "Walls", "Preview", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Beckers 2024 report state of healthcare collaboration how communication inefficiencies impact clinician productivity 11.08.57 AM 1\" - accuracy verification needed", "selector": "img.kb-img.wp-image-17384", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-17384", "Alt", "text", "present", "<PERSON><PERSON>", "report", "state", "of", "healthcare", "collaboration", "how", "communication", "inefficiencies", "impact", "clinician", "productivity", "AM", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.434Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Alt text too short to be descriptive", "selector": "img.kb-img.wp-image-1370", "severity": "error", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-1370", "Alt", "text", "too", "short", "to", "be", "descriptive"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TigerConnect-Clinical-Healthcare-Communications-Logo-R\" - accuracy verification needed", "selector": "img.kb-img.wp-image-47", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-47", "Alt", "text", "present", "TigerConnect-Clinical-Healthcare-Communications-Logo-R", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Google-Play-App-Clinical-Collaboration-Apple-App-TigerConnect\" - accuracy verification needed", "selector": "img.kb-img.wp-image-40", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-40", "Alt", "text", "present", "Google-Play-App-Clinical-Collaboration-Apple-App-TigerConnect", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Apple-App-Clinical-Collaboration-Google-App-TigerConnect\" - accuracy verification needed", "selector": "img.kb-img.wp-image-41", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.kb-img.wp-image-41", "Alt", "text", "present", "Apple-App-Clinical-Collaboration-Google-App-TigerConnect", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 64, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Search\" - accuracy verification needed", "selector": "svg.[object.SVGAnimatedString]", "severity": "warning", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Alt", "text", "present", "Search", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 65, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Search\" - accuracy verification needed", "selector": "svg.[object.SVGAnimatedString]", "severity": "warning", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Alt", "text", "present", "Search", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 66, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 67, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 68, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 69, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 70, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 71, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 72, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.435Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 73, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 74, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 75, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 76, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 77, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 78, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Play\" - accuracy verification needed", "selector": "svg.[object.SVGAnimatedString]", "severity": "warning", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Alt", "text", "present", "Play", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 79, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 80, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 81, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 82, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 83, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 84, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 85, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 86, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 87, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 88, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 89, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 90, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 91, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 92, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 93, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 94, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 95, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 96, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 97, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 98, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 99, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.436Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 100, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 101, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 102, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 103, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 104, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 105, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 106, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 107, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 108, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 109, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 110, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 111, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 112, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 113, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 114, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 115, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 116, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 117, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 118, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 119, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 120, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 121, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 122, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 123, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 124, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 125, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.437Z"}}}, {"type": "text", "description": "Non-text content has appropriate alternative", "value": "Decorative element properly marked with empty alt or aria-hidden", "selector": "svg.[object.SVGAnimatedString]", "severity": "info", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "properly", "marked", "with", "empty", "alt", "or", "aria-hidden"], "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 126, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.438Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 127, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.438Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 128, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.438Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 129, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.438Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 130, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.438Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 131, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.438Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 261, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 132, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T10:44:40.438Z"}}}], "recommendations": ["Add appropriate alternative text for all non-text content", "img.kb-img.wp-image-1370: Provide more descriptive alternative text", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add alt=\"\" for decorative images", "svg.[object.SVGAnimatedString]: Add alt=\"\" for decorative images", "svg.[object.SVGAnimatedString]: Add alt=\"\" for decorative images", "svg.[object.SVGAnimatedString]: Add alt=\"\" for decorative images", "svg.[object.SVGAnimatedString]: Add alt=\"\" for decorative images", "svg.[object.SVGAnimatedString]: Add alt=\"\" for decorative images", "svg.[object.SVGAnimatedString]: Add alt=\"\" for decorative images", "svg.[object.SVGAnimatedString]: Add alt=\"\" for decorative images", "svg.[object.SVGAnimatedString]: Add alt=\"\" for decorative images", "svg.[object.SVGAnimatedString]: Add alt=\"\" for decorative images", "svg.[object.SVGAnimatedString]: Add alt=\"\" for decorative images", "svg.[object.SVGAnimatedString]: Add alt=\"\" for decorative images", "Use empty alt=\"\" for decorative images", "Provide descriptive alternatives that convey the same information"], "executionTime": 0, "errorMessage": "Missing or inadequate alternative text for img.kb-img.wp-image-1370; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]", "manualReviewItems": []}, "timestamp": 1752230680438, "hash": "97431b52798dfd7f2ac7733c1024f2a5", "accessCount": 1, "lastAccessed": 1752230680438, "size": 91258}
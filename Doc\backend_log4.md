Microsoft Windows [Version 10.0.19045.6093]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\Web projects\Comply Checker\backend"

D:\Web projects\Comply Checker\backend>npm run dev

> backend@0.1.0 dev
> cross-env DEBUG=keycloak* nodemon --exec ts-node -r tsconfig-paths/register src/index.ts

[nodemon] 3.1.10
[nodemon] to restart at any time, enter `rs`
[nodemon] watching path(s): src\**\* ..\lib\**\*
[nodemon] watching extensions: ts,json
[nodemon] starting `ts-node -r tsconfig-paths/register src/index.ts`
[ENV_DEBUG] NODE_ENV: development
[ENV_DEBUG] isTestEnv: false
[ENV_DEBUG] process.env.POSTGRES_HOST (raw): localhost
[ENV_DEBUG] parsedEnv.POSTGRES_HOST (from Zod): localhost
[ENV_DEBUG] dbHost chosen: localhost
[ENV_DEBUG] Constructed DATABASE_URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
2025-07-11T19:23:11.792Z [INFO] - 🗄️ Smart cache initialized - {"maxSize":200,"maxEntries":1000,"de faultTTL":14400000,"defaultTTLMinutes":240}
2025-07-11T19:23:12.555Z [DEBUG] - Readability analysis library not available
2025-07-11T19:23:12.563Z [DEBUG] - Language detection library not available
🔧 Nuclei client initialized with path: D:\Web projects\Comply Checker\tools\nuclei\nuclei.exe
🔧 Templates path: D:\Web projects\Comply Checker\nuclei-templates
🔧 Initializing HIPAA Security Database...
📋 Database URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
📋 Environment: development
🔧 Nuclei client initialized with path: D:\Web projects\Comply Checker\tools\nuclei\nuclei.exe
🔧 Templates path: D:\Web projects\Comply Checker\nuclei-templates
🔧 Initializing HIPAA Security Database...
📋 Database URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
📋 Environment: development
🔧 Nuclei client initialized with path: D:\Web projects\Comply Checker\tools\nuclei\nuclei.exe
🔧 Templates path: D:\Web projects\Comply Checker\nuclei-templates
🔧 Initializing HIPAA Security Database...
📋 Database URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
📋 Environment: development
🚀 Browser pool initialized: 2 max browsers, 3 max pages per browser
2025-07-11T19:23:24.885Z [DEBUG] - Initialized 5 core accessibility patterns
2025-07-11T19:23:24.886Z [INFO] - 📚 Accessibility Pattern Library initialized - {"totalPatterns":5,"enabledCategories":6,"strictMode":false}
2025-07-11T19:23:24.888Z [INFO] - 🤖 AI-Powered Semantic Validator initialized - {"nlpAnalysis":true,"patternLibrary":true,"contextualAnalysis":true}
2025-07-11T19:23:24.889Z [INFO] - 📊 Enhanced Content Quality Analyzer initialized - {"readabilityAnalysis":true,"semanticAnalysis":true,"languageDetection":true}
2025-07-11T19:23:24.891Z [INFO] - ⚡ Modern Framework Optimizer initialized - {"svelte":true,"solidjs":true,"qwik":true,"buildTools":true}
2025-07-11T19:23:24.892Z [INFO] - 🏗️ Component Library Detector initialized - {"materialUI":true,"a ntDesign":true,"chakraUI":true,"deepAnalysis":true}
2025-07-11T19:23:24.893Z [INFO] - EnhancedColorAnalyzer initialized - {"getContrastLibrary":true,"colorJSLibrary":true,"enhancedAccuracy":true}
2025-07-11T19:23:24.894Z [INFO] - 📄 Headless CMS Detector initialized - {"strapi":true,"contentful":true,"sanity":true,"jamstack":true}
2025-07-11T19:23:24.895Z [INFO] - 🔧 Utility Integration Manager initialized
2025-07-11T19:23:24.896Z [INFO] - 🚀 Enhanced Performance Monitor initialized - {"trendAnalysis":true,"autoOptimization":true,"resourceCorrelation":true,"realTimeAlerts":true}
2025-07-11T19:23:24.897Z [INFO] - 🔗 Performance Integration Bridge initialized
2025-07-11T19:23:24.898Z [INFO] - 📊 Real-Time Monitoring Dashboard initialized
2025-07-11T19:23:24.899Z [INFO] - 🔮 Initialized 4 predictive models
2025-07-11T19:23:24.899Z [INFO] - 🔮 Predictive Performance Analytics initialized
2025-07-11T19:23:24.900Z [INFO] - 🤖 Automated Optimization Engine initialized
2025-07-11T19:23:24.901Z [INFO] - 🔌 Dashboard WebSocket Service initialized
2025-07-11T19:23:24.902Z [INFO] - 🎛️ Performance Automation Controller initialized
2025-07-11T19:23:25.004Z [INFO] - 🌐 Browser pool initialized - {"maxBrowsers":1,"maxPagesPerBrowser":3,"memoryThreshold":2048}
2025-07-11T19:23:25.006Z [INFO] - Detected VPS profile: xlarge - {"cpu":4,"memory":12,"recommendedLimits":{"maxConcurrentScans":12,"maxBrowserInstances":4,"maxPagesPerBrowser":4,"maxCacheSize":400,"maxMemoryUsage":12800,"scanTimeout":60000}}
2025-07-11T19:23:25.009Z [DEBUG] - Worker processes disabled - {"enableWorkerProcesses":false,"isMaster":true,"nodeEnv":"development"}
2025-07-11T19:23:25.010Z [DEBUG] - Connection pools configured - {"maxSockets":50,"keepAlive":true}
[INFO] Registering test-auth routes (development only)
🚀 Starting Comply Checker Backend in development mode
📋 Port: 3001
📋 Setting up API routes...
📋 Attempting to start server on port 3001...
✅ Server started successfully!
🌐 Server running on: http://localhost:3001
🏥 Health check: http://localhost:3001/health
🧪 HIPAA Analysis: http://localhost:3001/api/v1/compliance/scan
📋 Environment: development
🚀 Ready to accept HIPAA compliance requests!
User found: {
  id: '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
  keycloak_id: '4eb85cce-8784-4bf1-b50a-9ce3a80fb67b',
  email: '<EMAIL>',
  created_at: 2025-06-20T10:13:02.039Z,
  updated_at: 2025-06-20T10:13:02.039Z
}
User found: {
  id: '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
  keycloak_id: '4eb85cce-8784-4bf1-b50a-9ce3a80fb67b',
  email: '<EMAIL>',
  created_at: 2025-06-20T10:13:02.039Z,
  updated_at: 2025-06-20T10:13:02.039Z
}
2025-07-11T19:23:35.724Z [INFO] - 🔓 [6e320ec8-5eb3-4651-ad3d-6c3ca8575707] Development mode: Mock authentication applied
2025-07-11T19:23:35.726Z [INFO] - 🚀 [6e320ec8-5eb3-4651-ad3d-6c3ca8575707] WCAG scan request received
2025-07-11T19:23:35.726Z [INFO] - 📋 [6e320ec8-5eb3-4651-ad3d-6c3ca8575707] Request headers: - {"content-type":"application/json","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","authorization":"Bearer [REDACTED]"}
2025-07-11T19:23:35.729Z [INFO] - 👤 [6e320ec8-5eb3-4651-ad3d-6c3ca8575707] User context: - {"userId":"9fed30a7-64b4-4ffe-b531-6e9cf592952b","email":"<EMAIL>","permissions":["wcag:scan","wcag:view","wcag:export"]}
2025-07-11T19:23:35.730Z [INFO] - 🎯 [6e320ec8-5eb3-4651-ad3d-6c3ca8575707] Target URL:  https://tigerconnect.com/
2025-07-11T19:23:35.731Z [INFO] - ⚙️ [6e320ec8-5eb3-4651-ad3d-6c3ca8575707] Scan options: - {"enableContrastAnalysis":true,"enableKeyboardTesting":true,"enableFocusAnalysis":true,"enableSemanticValidation":true,"enableManualReview":true,"wcagVersion":"all","level":"AAA","maxPages":5,"timeout":60000}
2025-07-11T19:23:35.732Z [INFO] - ✅ [6e320ec8-5eb3-4651-ad3d-6c3ca8575707] Target URL validation passed
2025-07-11T19:23:35.733Z [INFO] - 🔧 [6e320ec8-5eb3-4651-ad3d-6c3ca8575707] Scan configuration created: - {"targetUrl":" https://tigerconnect.com/","userId":"9fed30a7-64b4-4ffe-b531-6e9cf592952b","level":"AAA","timeout":60000}
2025-07-11T19:23:35.734Z [INFO] - 🚀 [6e320ec8-5eb3-4651-ad3d-6c3ca8575707] Starting comprehensive WCAG scan with orchestrator
2025-07-11T19:23:35.736Z [INFO] - 📝 Creating WCAG scan record...
2025-07-11T19:23:35.738Z [INFO] - 🆔 [6e320ec8-5eb3-4651-ad3d-6c3ca8575707] Scan initiated with ID: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:23:35.739Z [INFO] - 📊 [6e320ec8-5eb3-4651-ad3d-6c3ca8575707] Scan started successfully: - {"scanId":"1de36534-70ae-401c-b6b9-93552ec2dfaa","status":"pending","targetUrl":" https://tigerconnect.com/"}
2025-07-11T19:23:35.740Z [INFO] - ✅ [6e320ec8-5eb3-4651-ad3d-6c3ca8575707] WCAG scan initiated successfully in 14ms
2025-07-11T19:23:35.791Z [INFO] - ✅ WCAG scan record created with ID: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:23:35.791Z [INFO] - ✅ WCAG scan record created with ID: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:23:35.794Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> pending
2025-07-11T19:23:35.839Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> pending
2025-07-11T19:23:35.839Z [INFO] - 🚀 Initialized scan 1de36534-70ae-401c-b6b9-93552ec2dfaa with 66 rules
2025-07-11T19:23:35.841Z [DEBUG] - 📊 Started performance monitoring for scan: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:23:35.843Z [DEBUG] - 📊 Started performance monitoring for scan: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:23:35.845Z [DEBUG] - 📊 Started performance monitoring for scan: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:23:35.846Z [DEBUG] - 🔗 Integrated monitoring started for scan: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:23:35.847Z [DEBUG] - 📊 Registered active scan: 1de36534-70ae-401c-b6b9-93552ec2dfaa (total: 1)
2025-07-11T19:23:35.848Z [INFO] - 🎛️ Starting Performance Automation System...
2025-07-11T19:23:35.850Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:23:35.850Z [INFO] - 📊 Real-Time Monitoring Dashboard started
2025-07-11T19:23:35.852Z [INFO] - ✅ Dashboard monitoring started
2025-07-11T19:23:35.854Z [INFO] - 🔌 Dashboard WebSocket Service started on port 8081
2025-07-11T19:23:35.854Z [INFO] - ✅ WebSocket service started
2025-07-11T19:23:35.856Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T19:23:35.856Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T19:23:35.857Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T19:23:35.858Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T19:23:35.859Z [INFO] - 🔮 Predictive Performance Analytics started
2025-07-11T19:23:35.860Z [INFO] - ✅ Predictive analytics started
2025-07-11T19:23:35.862Z [INFO] - 🎛️ Performance Automation System fully operational
2025-07-11T19:23:35.866Z [INFO] - 🎛️ Started Performance Automation Controller for scan monitoring
2025-07-11T19:23:35.867Z [DEBUG] - 🔍 Getting page for scan: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:23:37.516Z [INFO] - ✅ Created new browser: browser-1752261817516-308c996ct
2025-07-11T19:23:37.856Z [DEBUG] - 🆕 Created new page for scan: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:23:37.856Z [DEBUG] - ✅ URL validation passed for:  https://tigerconnect.com/
2025-07-11T19:23:37.858Z [DEBUG] - 🔍 Checking connectivity for:  https://tigerconnect.com/
2025-07-11T19:23:39.359Z [DEBUG] - ✅ Connectivity check passed for:  https://tigerconnect.com/ (status: 200)
2025-07-11T19:23:39.359Z [INFO] - 🔗 Navigating to:  https://tigerconnect.com/ (timeout: 60000ms)
2025-07-11T19:23:40.857Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:23:44.219Z [INFO] - ✅ Navigation completed
2025-07-11T19:23:44.220Z [INFO] - 🚀 Starting Phase 2 analysis: Website type detection and optimization
2025-07-11T19:23:44.222Z [DEBUG] - 🔍 Starting CMS platform detection
2025-07-11T19:23:44.227Z [DEBUG] - 🛒 Starting e-commerce accessibility analysis
2025-07-11T19:23:44.228Z [DEBUG] - 🎬 Starting media accessibility analysis
2025-07-11T19:23:44.231Z [DEBUG] - ⚛️ Starting framework-specific accessibility analysis
2025-07-11T19:23:44.248Z [WARN] - ⚠️ Phase 2 analysis encountered errors, continuing with standard analysis - {"error":"CMS detection failed - no results returned"}
2025-07-11T19:23:44.250Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:44.282Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:44.282Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 0% (0/66)
2025-07-11T19:23:44.284Z [INFO] - 🔍 Executing 66 WCAG checks...
2025-07-11T19:23:44.287Z [INFO] - 🔥 Pre-warming cache for WCAG scan...
2025-07-11T19:23:44.288Z [INFO] - 🔥 Pre-warming cache for WCAG scan:  https://tigerconnect.com/
2025-07-11T19:23:44.289Z [INFO] - 🔥 Warming up cache with 1 URLs
2025-07-11T19:23:44.298Z [DEBUG] - 💾 Cache saved to file: 2a845e7845c36451d231bef38dd004eb.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.298Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:ce50a093 (62 bytes)
2025-07-11T19:23:44.306Z [DEBUG] - 💾 Cache saved to file: f4baa1391129ce66f14a80fa831f74c4.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.307Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:a43c1b0a (61 bytes)
2025-07-11T19:23:44.312Z [DEBUG] - 💾 Cache saved to file: a87feed7dbc8021e19fc2a33ee27e035.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.313Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:0cc175b9 (57 bytes)
2025-07-11T19:23:44.317Z [DEBUG] - 💾 Cache saved to file: 9115c5ce4c94bb2c6ee72e1334eca8a8.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.318Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:b798abe6 (59 bytes)
2025-07-11T19:23:44.324Z [DEBUG] - 💾 Cache saved to file: cc36c9e7addda5e54be3b7d02a5af8df.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.325Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:3fcdb73d (60 bytes)
2025-07-11T19:23:44.329Z [DEBUG] - 💾 Cache saved to file: fe97c18eeabb0f14e101e1601e6f3e47.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.329Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:d72e5ee9 (59 bytes)
2025-07-11T19:23:44.332Z [DEBUG] - 💾 Cache saved to file: 91a153f57284a3e1c73e22a6635605d5.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.333Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:fad58de7 (60 bytes)
2025-07-11T19:23:44.336Z [DEBUG] - 💾 Cache saved to file: 909f18e046ebc2fa780c1ceb75ae2e21.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.336Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:099fb995 (62 bytes)
2025-07-11T19:23:44.340Z [DEBUG] - 💾 Cache saved to file: 25ece69033148bae6d493dc0d672b5a8.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.342Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:251d1646 (62 bytes)
2025-07-11T19:23:44.345Z [DEBUG] - 💾 Cache saved to file: 205567be6824cd4c6c217585058e8bf1.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.346Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:3f3852a9 (73 bytes)
2025-07-11T19:23:44.349Z [DEBUG] - 💾 Cache saved to file: 4e859001dcf7bf1dbd464a1ac5a437be.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.349Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:14e1a17f (71 bytes)
2025-07-11T19:23:44.352Z [DEBUG] - 💾 Cache saved to file: 641b28e8b930d3a45fcc072b247c019a.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.352Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:2e716448 (68 bytes)
2025-07-11T19:23:44.357Z [DEBUG] - 💾 Cache saved to file: af700f66eac47cdc76feb16105d0884a.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.358Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:cacf55f1 (61 bytes)
2025-07-11T19:23:44.362Z [DEBUG] - 💾 Cache saved to file: 81ca17de75827ca8663df24df59da070.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.362Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:af04abc2 (66 bytes)
2025-07-11T19:23:44.365Z [DEBUG] - 💾 Cache saved to file: 7ed6ef74268f5d0f9072e831444fcc75.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.365Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:346b81a3 (58 bytes)
2025-07-11T19:23:44.368Z [DEBUG] - 💾 Cache saved to file: 0750f253ecde96cffceabcf406f9c78a.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.368Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:490b2834 (58 bytes)
2025-07-11T19:23:44.375Z [DEBUG] - 💾 Cache saved to file: d7bc520f8823c8f27baa51e5df7a69e3.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.376Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:6f207f8b (58 bytes)
2025-07-11T19:23:44.380Z [DEBUG] - 💾 Cache saved to file: c800f104b239a15728f8410edf55737a.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.380Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:ce1b1e8c (58 bytes)
2025-07-11T19:23:44.384Z [DEBUG] - 💾 Cache saved to file: 7adc36e31eb7285fcd776e956021a2c4.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.384Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:77230e94 (58 bytes)
2025-07-11T19:23:44.393Z [DEBUG] - 💾 Cache saved to file: 5b12266db9052c70c7a8d8083d15c804.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.394Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:d8544ba1 (58 bytes)
2025-07-11T19:23:44.398Z [DEBUG] - 💾 Cache saved to file: f3d9f673d9df0bec84a8da79252f3427.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.399Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:73d5342e (63 bytes)
2025-07-11T19:23:44.402Z [DEBUG] - 💾 Cache saved to file: af72610fd14a583d3d723f1b5887378f.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.404Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:92a2b5cb (63 bytes)
2025-07-11T19:23:44.408Z [DEBUG] - 💾 Cache saved to file: 34cbecd0ebaf956ad65a320018d0ead9.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.409Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:421b47ff (61 bytes)
2025-07-11T19:23:44.412Z [DEBUG] - 💾 Cache saved to file: 19369bb7ae838a828d9ff54f81b8b569.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.412Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:a5ca0b58 (61 bytes)
2025-07-11T19:23:44.415Z [DEBUG] - 💾 Cache saved to file: b07feca51ba258d682315dade04fc6b4.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.416Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:a598e4f2 (62 bytes)
2025-07-11T19:23:44.422Z [DEBUG] - 💾 Cache saved to file: 5e7e0997d2a97e50add9a9ab8412574e.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.423Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:a8cfde63 (62 bytes)
2025-07-11T19:23:44.426Z [DEBUG] - 💾 Cache saved to file: dd8dbab490b1803b07b48c754bdfeec5.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.426Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:269605d4 (61 bytes)
2025-07-11T19:23:44.430Z [DEBUG] - 💾 Cache saved to file: a19a506b6f1933e2adce86ea88aafb42.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.430Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:d304ba20 (61 bytes)
2025-07-11T19:23:44.438Z [DEBUG] - 💾 Cache saved to file: 82d5dfafe366577209ca31818bf1490d.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.439Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:639612e6 (64 bytes)
2025-07-11T19:23:44.443Z [DEBUG] - 💾 Cache saved to file: df43756965f7946819387675968e4fc6.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.443Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:3d4dcd6f (62 bytes)
2025-07-11T19:23:44.447Z [DEBUG] - 💾 Cache saved to file: a236b7e934743ba88ab839731f176d6d.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.450Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:99938282 (62 bytes)
2025-07-11T19:23:44.456Z [DEBUG] - 💾 Cache saved to file: e898f85784e13cf9d4092ee04fcd0b3f.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.457Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:6394d816 (64 bytes)
2025-07-11T19:23:44.462Z [DEBUG] - 💾 Cache saved to file: c9d7e9624fd2d5933fbe291a90c34540.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.463Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:aab9e1de (61 bytes)
2025-07-11T19:23:44.476Z [DEBUG] - 💾 Cache saved to file: 40557d320806d002840c34ccd4315eff.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.478Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:1fdc0f89 (58 bytes)
2025-07-11T19:23:44.490Z [DEBUG] - 💾 Cache saved to file: 437bf9bc49de19438325389b747e23dc.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.491Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:626726e6 (58 bytes)
2025-07-11T19:23:44.518Z [DEBUG] - 💾 Cache saved to file: a9baf949da570ca3d95f96f8471da263.json (key: dom:https://tigerconnect.com/:...)
2025-07-11T19:23:44.523Z [DEBUG] - 💾 Cached: dom:dom:https://tigerconnect.com/:7a7dc1cd (63 bytes)
2025-07-11T19:23:44.530Z [DEBUG] - 💾 Cache saved to file: 2bd6a2506cb0d2718ae128b2f31442d2.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:23:44.532Z [DEBUG] - 💾 Cached: site:site:https://tigerconnect.com/:image-analysis (82 bytes)
2025-07-11T19:23:44.538Z [DEBUG] - 💾 Cache saved to file: 52cae929017b4332189f46f5c5a9dd94.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:23:44.540Z [DEBUG] - 💾 Cached: site:site:https://tigerconnect.com/:link-analysis (81 bytes)
2025-07-11T19:23:44.546Z [DEBUG] - 💾 Cache saved to file: 4967babebf16755f473dd3951d9948dd.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:23:44.548Z [DEBUG] - 💾 Cached: site:site:https://tigerconnect.com/:form-analysis (81 bytes)
2025-07-11T19:23:44.555Z [DEBUG] - 💾 Cache saved to file: df40a9913b5fbedcfe1070ee8522c15e.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:23:44.556Z [DEBUG] - 💾 Cached: site:site:https://tigerconnect.com/:heading-analysis (84 bytes)
2025-07-11T19:23:44.564Z [DEBUG] - 💾 Cache saved to file: 78bfcc47166ac82ab1c5fe9c258262d0.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:23:44.565Z [DEBUG] - 💾 Cached: site:site:https://tigerconnect.com/:color-contrast (82 bytes)
2025-07-11T19:23:44.571Z [DEBUG] - 💾 Cache saved to file: d3e088d5b6487849d5f98d78a804b3a1.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:23:44.573Z [DEBUG] - 💾 Cached: site:site:https://tigerconnect.com/:focus-management (84 bytes)
2025-07-11T19:23:44.581Z [DEBUG] - 💾 Cache saved to file: 4c6e67814110b62433763abca4983d44.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:23:44.582Z [DEBUG] - 💾 Cached: site:site:https://tigerconnect.com/:keyboard-navigation (87 bytes)
2025-07-11T19:23:44.589Z [DEBUG] - 💾 Cache saved to file: 782fa3a9f5e6c61387345aaa4181c957.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:23:44.591Z [DEBUG] - 💾 Cached: site:site:https://tigerconnect.com/:aria-validation (83 bytes)
2025-07-11T19:23:44.592Z [DEBUG] - 🔥 Warmed up cache for: https://tigerconnect.com/
2025-07-11T19:23:44.593Z [INFO] - 🔥 Cache warming completed for 1 URLs
2025-07-11T19:23:44.595Z [INFO] - ✅ Cache pre-warming completed for:  https://tigerconnect.com/
2025-07-11T19:23:44.596Z [INFO] - ✅ Cache pre-warming completed
2025-07-11T19:23:44.597Z [INFO] - 📋 Extracting unified page structure for all checks...
2025-07-11T19:23:44.601Z [DEBUG] - 📁 Cache file not found: 90bcb3e53c2a6e023e1ce985ebd4e9a7.json (key: dom-structure:https://tigercon...)
2025-07-11T19:23:44.602Z [DEBUG] - 🔍 Cache miss: dom:dom-structure:https://tigerconnect.com/:053b13d2...
2025-07-11T19:23:44.606Z [DEBUG] - 📊 Cache stats: 0 hits, 1 misses, 36 entries
2025-07-11T19:23:44.607Z [INFO] - 📋 Extracting page structure for all WCAG checks...
2025-07-11T19:23:44.609Z [DEBUG] - 🔄 DOM injection attempt 1/3
2025-07-11T19:23:44.622Z [DEBUG] - ✅ DOM extraction functions validated successfully (attempt 1)
2025-07-11T19:23:44.787Z [DEBUG] - 💾 Cache saved to file: 90bcb3e53c2a6e023e1ce985ebd4e9a7.json (key: dom-structure:https://tigercon...)
2025-07-11T19:23:44.787Z [DEBUG] - 💾 Cached: dom:dom-structure:https://tigerconnect.com/:053b13d2 (1316591 bytes)
2025-07-11T19:23:44.790Z [INFO] - 📋 Page structure extracted in 135ms - 372 elements found
2025-07-11T19:23:44.792Z [INFO] - 📋 Page structure extracted successfully: 761 elements found
2025-07-11T19:23:44.793Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:44.808Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:44.808Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 0% (0/66)
2025-07-11T19:23:44.810Z [INFO] - 🔍 Executing rule: Non-text Content (WCAG-001)
2025-07-11T19:23:44.813Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-001: Non-text Content
2025-07-11T19:23:44.826Z [DEBUG] - 📁 Cache file valid: c6754d1f3d7984f828ea8e880832ddbe.json (201min remaining)
2025-07-11T19:23:44.833Z [DEBUG] - 📁 Cache loaded from file: c6754d1f3d7984f828ea8e880832ddbe.json (key: rule:WCAG-001:053b13d2:add9231...)
2025-07-11T19:23:44.837Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-001:053b13d2:add92319...
2025-07-11T19:23:44.840Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-001:053b13d2:add92319... (accessed 2 times, age: 2346s)
2025-07-11T19:23:44.845Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-001
2025-07-11T19:23:44.848Z [DEBUG] - 📁 Cache file not found: 1869938e17710638ee268fb349d1ca5a.json (key: rule:WCAG-001:WCAG-001...)
2025-07-11T19:23:44.849Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-001:WCAG-001...
2025-07-11T19:23:44.850Z [DEBUG] - 📊 Cache stats: 1 hits, 2 misses, 1 entries
2025-07-11T19:23:44.867Z [DEBUG] - 📊 Reached max evidence items limit: 60
2025-07-11T19:23:44.888Z [DEBUG] - 💾 Cache saved to file: 1869938e17710638ee268fb349d1ca5a.json (key: rule:WCAG-001:WCAG-001...)
2025-07-11T19:23:44.889Z [DEBUG] - 💾 Cached: rule:rule:WCAG-001:WCAG-001 (57158 bytes)
2025-07-11T19:23:44.892Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-001 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableContentQualityAnalysis":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T19:23:44.895Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:23:44.896Z [DEBUG] - 📁 Cache file not found: b5c8b1740a14e730d4e17f093e8ccf80.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:23:44.898Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:44.901Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:23:44.903Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:23:44.911Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:23:44.918Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:semantic-validation...
2025-07-11T19:23:44.920Z [DEBUG] - 📊 Cache stats: 1 hits, 3 misses, 8 entries
2025-07-11T19:23:44.924Z [DEBUG] - 🏗️ Starting semantic HTML and ARIA validation
2025-07-11T19:23:45.013Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:23:45.064Z [DEBUG] - 💾 Cache saved to file: b5c8b1740a14e730d4e17f093e8ccf80.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:23:45.065Z [DEBUG] - 💾 Cached: site:site:https://tigerconnect.com/:semantic-validation-{"validateLandmarks":true,"validateHeadings":true,"validateAria":true,"validateSemanticElements":true,"strictMode":false,"includeHiddenElements":false} (27562 bytes)
2025-07-11T19:23:45.067Z [INFO] - ✅ Semantic validation completed - {"landmarksValid":"5/5","headingsValid":false,"ariaValid":"11/11","overallScore":52}
2025-07-11T19:23:45.069Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:23:45.071Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:23:45.072Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:23:45.073Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:23:45.086Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:23:45.100Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":2,"validPatterns":1,"overallScore":50}
2025-07-11T19:23:45.101Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-001 - {"utilitiesUsed":["content-quality","component-library","framework-optimization","semantic-validation","pattern-validation"],"confidence":1,"accuracy":0.8999999999999999,"executionTime":209}
2025-07-11T19:23:45.102Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-001: - {"utilitiesUsed":5,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:45.105Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-001"]}
2025-07-11T19:23:45.106Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-001
2025-07-11T19:23:45.106Z [DEBUG] - 🔧 Utility performance recorded for WCAG-001: - {"utilitiesUsed":5,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:45.107Z [DEBUG] - 🔧 Utility analysis completed for WCAG-001: - {"utilitiesUsed":5,"errors":0,"executionTime":292}
2025-07-11T19:23:45.108Z [DEBUG] - ⏱️ Check WCAG-001 completed in 298ms (success: true)
2025-07-11T19:23:45.109Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:45.113Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:45.115Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 2% (1/66)
2025-07-11T19:23:45.116Z [INFO] - ✅ Rule WCAG-001 completed: failed (0/100)
2025-07-11T19:23:45.117Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:45.122Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:45.123Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 2% (1/66)
2025-07-11T19:23:45.123Z [INFO] - 🔍 Executing rule: Captions (Prerecorded) (WCAG-002)
2025-07-11T19:23:45.124Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-002: Captions
2025-07-11T19:23:45.126Z [DEBUG] - 📁 Cache file valid: 9409fab1ac2c0264853efa9784cc35c9.json (201min remaining)
2025-07-11T19:23:45.128Z [DEBUG] - 📁 Cache loaded from file: 9409fab1ac2c0264853efa9784cc35c9.json (key: rule:WCAG-002:053b13d2:add9231...)
2025-07-11T19:23:45.129Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-002:053b13d2:add92319...
2025-07-11T19:23:45.131Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-002:053b13d2:add92319... (accessed 2 times, age: 2340s)
2025-07-11T19:23:45.132Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-002
2025-07-11T19:23:45.133Z [DEBUG] - 📁 Cache file not found: 16d5607e2ed0981c5cc09eb732b9d323.json (key: rule:WCAG-002:WCAG-002...)
2025-07-11T19:23:45.133Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-002:WCAG-002...
2025-07-11T19:23:45.134Z [DEBUG] - 📊 Cache stats: 2 hits, 4 misses, 3 entries
2025-07-11T19:23:45.136Z [DEBUG] - 💾 Cache saved to file: 16d5607e2ed0981c5cc09eb732b9d323.json (key: rule:WCAG-002:WCAG-002...)
2025-07-11T19:23:45.137Z [DEBUG] - 💾 Cached: rule:rule:WCAG-002:WCAG-002 (2 bytes)
2025-07-11T19:23:45.138Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-002 - {"config":{"enableSemanticValidation":true,"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"enhance","maxExecutionTime":8000}}
2025-07-11T19:23:45.138Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:23:45.139Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 2 times, age: 0s)
2025-07-11T19:23:45.140Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:23:45.141Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:23:45.142Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:23:45.147Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:23:45.148Z [DEBUG] - 📊 Cache stats: 3 hits, 5 misses, 9 entries
2025-07-11T19:23:45.149Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:23:45.151Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:23:45.152Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:23:45.154Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:23:45.155Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:23:45.181Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:23:45.204Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:23:45.861Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:23:50.189Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:23:50.190Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:23:50.192Z [WARN] - ⚠️ Utility cms-detection failed on attempt 1, retrying... - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded","attempt":1,"maxRetries":2}
2025-07-11T19:23:50.869Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:23:51.203Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:23:51.203Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:23:51.205Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:23:51.207Z [DEBUG] - 📊 Cache stats: 3 hits, 6 misses, 9 entries
2025-07-11T19:23:53.158Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis failed for WCAG-002: - {"error":"Utility analysis timeout"}
2025-07-11T19:23:53.158Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-002: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:53.160Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-002","Utility errors detected for WCAG-002: Utility analysis error: Utility analysis timeout"]}
2025-07-11T19:23:53.162Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-002
2025-07-11T19:23:53.164Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Utility errors detected for WCAG-002: Utility analysis error: Utility analysis timeout
2025-07-11T19:23:53.165Z [DEBUG] - 🔧 Utility performance recorded for WCAG-002: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:53.165Z [DEBUG] - 🔧 Utility analysis completed for WCAG-002: - {"utilitiesUsed":2,"errors":1,"executionTime":8035}
2025-07-11T19:23:53.167Z [DEBUG] - ⏱️ Check WCAG-002 completed in 8044ms (success: true)
2025-07-11T19:23:53.168Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.173Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.173Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 3% (2/66)
2025-07-11T19:23:53.176Z [INFO] - ✅ Rule WCAG-002 completed: failed (0/100)
2025-07-11T19:23:53.180Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.185Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.185Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 3% (2/66)
2025-07-11T19:23:53.186Z [INFO] - 🔍 Executing rule: Info and Relationships (WCAG-003)
2025-07-11T19:23:53.188Z [INFO] - 🌈 Wide Gamut Color Analyzer initialized - {"p3Analysis":true,"rec2020Analysis":true,"dynamicMonitoring":true}
2025-07-11T19:23:53.189Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-003: Info and Relationships
2025-07-11T19:23:53.192Z [DEBUG] - 📁 Cache file valid: 825737c58beb1e0ddbe4133e16fca74a.json (201min remaining)
2025-07-11T19:23:53.192Z [DEBUG] - 📁 Cache loaded from file: 825737c58beb1e0ddbe4133e16fca74a.json (key: rule:WCAG-003:053b13d2:add9231...)
2025-07-11T19:23:53.193Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-003:053b13d2:add92319...
2025-07-11T19:23:53.195Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-003:053b13d2:add92319... (accessed 2 times, age: 2339s)
2025-07-11T19:23:53.196Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-003
2025-07-11T19:23:53.198Z [DEBUG] - 📁 Cache file not found: c26ce3f094fb1be952589ef840c8f0b9.json (key: rule:WCAG-003:WCAG-003...)
2025-07-11T19:23:53.199Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-003:WCAG-003...
2025-07-11T19:23:53.200Z [DEBUG] - 📊 Cache stats: 4 hits, 7 misses, 5 entries
2025-07-11T19:23:53.206Z [DEBUG] - 💾 Cache saved to file: c26ce3f094fb1be952589ef840c8f0b9.json (key: rule:WCAG-003:WCAG-003...)
2025-07-11T19:23:53.207Z [DEBUG] - 💾 Cached: rule:rule:WCAG-003:WCAG-003 (37800 bytes)
2025-07-11T19:23:53.209Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-003 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableContentQualityAnalysis":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:23:53.210Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:23:53.211Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 3 times, age: 8s)
2025-07-11T19:23:53.212Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:53.215Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:23:53.216Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:23:53.217Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:23:53.218Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:23:53.219Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:23:53.220Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:23:53.224Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:23:53.245Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:23:53.266Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:23:53.282Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:23:53.282Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-003 - {"utilitiesUsed":["content-quality","semantic-validation","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.7,"executionTime":74}
2025-07-11T19:23:53.283Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-003: - {"utilitiesUsed":4,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:53.286Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-003"]}
2025-07-11T19:23:53.288Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-003
2025-07-11T19:23:53.289Z [DEBUG] - 🔧 Utility performance recorded for WCAG-003: - {"utilitiesUsed":4,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:53.290Z [DEBUG] - 🔧 Utility analysis completed for WCAG-003: - {"utilitiesUsed":4,"errors":0,"executionTime":97}
2025-07-11T19:23:53.291Z [DEBUG] - ⏱️ Check WCAG-003 completed in 105ms (success: true)
2025-07-11T19:23:53.292Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.299Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.300Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 5% (3/66)
2025-07-11T19:23:53.301Z [INFO] - ✅ Rule WCAG-003 completed: failed (0/100)
2025-07-11T19:23:53.303Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.309Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.309Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 5% (3/66)
2025-07-11T19:23:53.310Z [INFO] - 🔍 Executing rule: Contrast (Minimum) (WCAG-004)
2025-07-11T19:23:53.311Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-004: Contrast (Minimum)
2025-07-11T19:23:53.318Z [DEBUG] - 📁 Cache file valid: dd9cf69eadda27ed121596a06470d50b.json (201min remaining)
2025-07-11T19:23:53.318Z [DEBUG] - 📁 Cache loaded from file: dd9cf69eadda27ed121596a06470d50b.json (key: rule:WCAG-004:053b13d2:add9231...)
2025-07-11T19:23:53.319Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-004:053b13d2:add92319...
2025-07-11T19:23:53.321Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-004:053b13d2:add92319... (accessed 2 times, age: 2335s)
2025-07-11T19:23:53.322Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-004
2025-07-11T19:23:53.324Z [DEBUG] - 📁 Cache file not found: 0ff4d87e84bfce9b867fa42f05128899.json (key: rule:WCAG-004:WCAG-004...)
2025-07-11T19:23:53.325Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-004:WCAG-004...
2025-07-11T19:23:53.326Z [DEBUG] - 📊 Cache stats: 6 hits, 8 misses, 7 entries
2025-07-11T19:23:53.327Z [DEBUG] - 🔍 Skipping low-quality evidence: Third-party library integration status
2025-07-11T19:23:53.328Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis summary
2025-07-11T19:23:53.333Z [DEBUG] - 🔍 Skipping low-quality evidence: Wide gamut color space analysis (P3, Rec2020)
2025-07-11T19:23:53.334Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: <iframe src=https://www.google...
2025-07-11T19:23:53.336Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Skip to content...
2025-07-11T19:23:53.337Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Contact Sales: (800) 572-0470...
2025-07-11T19:23:53.338Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Contact Support...
2025-07-11T19:23:53.339Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: LoginExpand...
2025-07-11T19:23:53.340Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Expand...
2025-07-11T19:23:53.341Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: TigerConnect...
2025-07-11T19:23:53.342Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Physician Scheduling...
2025-07-11T19:23:53.343Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: TigerConnect Community...
2025-07-11T19:23:53.344Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Search for:...
2025-07-11T19:23:53.348Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Search Button...
2025-07-11T19:23:53.349Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Home...
2025-07-11T19:23:53.350Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Who We ServeExpand...
2025-07-11T19:23:53.351Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Expand...
2025-07-11T19:23:53.352Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Who We Serve​...
2025-07-11T19:23:53.353Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: See how secure clinical collab...
2025-07-11T19:23:53.354Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Learn More...
2025-07-11T19:23:53.355Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Services...
2025-07-11T19:23:53.356Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Contact Us...
2025-07-11T19:23:53.357Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Healthcare Professionals...
2025-07-11T19:23:53.358Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Resources...
2025-07-11T19:23:53.359Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Healthcare Organizations...
2025-07-11T19:23:53.364Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Get a Demo...
2025-07-11T19:23:53.365Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: SolutionsExpand...
2025-07-11T19:23:53.366Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Expand...
2025-07-11T19:23:53.367Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Solutions​...
2025-07-11T19:23:53.368Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Coordinate care across teams a...
2025-07-11T19:23:53.370Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Learn More...
2025-07-11T19:23:53.371Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Contact Us...
2025-07-11T19:23:53.372Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Solutions...
2025-07-11T19:23:53.373Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Resources...
2025-07-11T19:23:53.374Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Workflows...
2025-07-11T19:23:53.375Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Get a Demo...
2025-07-11T19:23:53.376Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: ResourcesExpand...
2025-07-11T19:23:53.380Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Expand...
2025-07-11T19:23:53.382Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Resources...
2025-07-11T19:23:53.383Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: It’s all here! Explore our lat...
2025-07-11T19:23:53.384Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Learn More...
2025-07-11T19:23:53.385Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Support...
2025-07-11T19:23:53.385Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Contact Us...
2025-07-11T19:23:53.386Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Resources...
2025-07-11T19:23:53.387Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Resources...
2025-07-11T19:23:53.388Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Resources...
2025-07-11T19:23:53.389Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Get a Demo...
2025-07-11T19:23:53.390Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Why TigerConnect?Expand...
2025-07-11T19:23:53.391Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Expand...
2025-07-11T19:23:53.394Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Why TigerConnnect?...
2025-07-11T19:23:53.396Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Since 2010 TigerConnect has be...
2025-07-11T19:23:53.398Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Learn More...
2025-07-11T19:23:53.399Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Contact Us...
2025-07-11T19:23:53.400Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Resources...
2025-07-11T19:23:53.401Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Resources...
2025-07-11T19:23:53.402Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Why TigerConnect?...
2025-07-11T19:23:53.403Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Get a Demo...
2025-07-11T19:23:53.404Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Get a Demo...
2025-07-11T19:23:53.405Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Toggle Menu...
2025-07-11T19:23:53.406Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Unified Healthcare Communicati...
2025-07-11T19:23:53.407Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: One Platform to Unify Communic...
2025-07-11T19:23:53.410Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: One Platform to Unify Communic...
2025-07-11T19:23:53.412Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Discover how leading hospitals...
2025-07-11T19:23:53.413Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Watch Video...
2025-07-11T19:23:53.415Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Take a Tour...
2025-07-11T19:23:53.416Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Pre-Hospital...
2025-07-11T19:23:53.417Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Clinical Collaboration...
2025-07-11T19:23:53.418Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: PhysicianScheduling...
2025-07-11T19:23:53.419Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: PhysicianScheduling...
2025-07-11T19:23:53.420Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Alarm Management & Event Notif...
2025-07-11T19:23:53.421Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: PatientEngagement...
2025-07-11T19:23:53.423Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: PatientEngagement...
2025-07-11T19:23:53.425Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: CareConduit...
2025-07-11T19:23:53.427Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Streamline Workflows Across th...
2025-07-11T19:23:53.428Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: TigerConnect solutions make he...
2025-07-11T19:23:53.429Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Critical Response...
2025-07-11T19:23:53.431Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Emergency Department...
2025-07-11T19:23:53.432Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Inpatient Care...
2025-07-11T19:23:53.433Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Operating Room...
2025-07-11T19:23:53.434Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Post Acute/Ambulatory...
2025-07-11T19:23:53.435Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Learn More...
2025-07-11T19:23:53.436Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: The Future of Unified Healthca...
2025-07-11T19:23:53.437Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: TigerConnect’s vision for the ...
2025-07-11T19:23:53.438Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Play...
2025-07-11T19:23:53.442Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Improve Collaboration. Enhance...
2025-07-11T19:23:53.443Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Improve Collaboration. Enhance...
2025-07-11T19:23:53.444Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: 20% increase...
2025-07-11T19:23:53.445Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: in ER capacity from faster tra...
2025-07-11T19:23:53.446Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: 50% reduction...
2025-07-11T19:23:53.447Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: in patient readmissions...
2025-07-11T19:23:53.448Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: 50% faster...
2025-07-11T19:23:53.449Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: door-to-needle time...
2025-07-11T19:23:53.450Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: 70% improvement...
2025-07-11T19:23:53.451Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: in first case on-time starts...
2025-07-11T19:23:53.452Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: 2.5 min reduction...
2025-07-11T19:23:53.456Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: in code blue response time...
2025-07-11T19:23:53.458Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: 72% improvement...
2025-07-11T19:23:53.458Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: in ED consult response times...
2025-07-11T19:23:53.459Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: 20% reduction...
2025-07-11T19:23:53.460Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: in length of stay...
2025-07-11T19:23:53.462Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: 46% improvement...
2025-07-11T19:23:53.463Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: in sepsis bundle compliance...
2025-07-11T19:23:53.464Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Solve Communication Challenges...
2025-07-11T19:23:53.465Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Pre-Hospital...
2025-07-11T19:23:53.466Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Connect EMS to hospital teams ...
2025-07-11T19:23:53.467Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Physician Scheduling...
2025-07-11T19:23:53.468Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Simplify provider scheduling w...
2025-07-11T19:23:53.471Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Clinical Collaboration...
2025-07-11T19:23:53.472Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Coordinate care seamlessly acr...
2025-07-11T19:23:53.474Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Alarm Management & Event Notif...
2025-07-11T19:23:53.475Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Stay on top of patient needs w...
2025-07-11T19:23:53.476Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Patient Engagement...
2025-07-11T19:23:53.478Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Effortlessly engage with patie...
2025-07-11T19:23:53.479Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Built With Your Care Teams in ...
2025-07-11T19:23:53.480Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Empower Collaboration...
2025-07-11T19:23:53.481Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Read More...
2025-07-11T19:23:53.482Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Consolidate Your Technology...
2025-07-11T19:23:53.483Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Read More...
2025-07-11T19:23:53.484Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Minimize Administrative Tasks...
2025-07-11T19:23:53.488Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Read More...
2025-07-11T19:23:53.489Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Increase Efficiency...
2025-07-11T19:23:53.490Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Increase Efficiency...
2025-07-11T19:23:53.491Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Read More...
2025-07-11T19:23:53.492Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Trusted Partner in Unified Hea...
2025-07-11T19:23:53.493Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Real Customers...
2025-07-11T19:23:53.495Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Clinical Communication & Colla...
2025-07-11T19:23:53.495Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: 2024 Best in KLAS Winner...
2025-07-11T19:23:53.497Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: for Clinical Communications in...
2025-07-11T19:23:53.498Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: #1 Leader in Winter 2025 G2 Ra...
2025-07-11T19:23:53.498Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: for HIPAA-Compliant Messaging,...
2025-07-11T19:23:53.500Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Named as a Leader and Placed H...
2025-07-11T19:23:53.504Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: in the 2024 Gartner® Magic Qua...
2025-07-11T19:23:53.505Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Resources...
2025-07-11T19:23:53.507Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Sales:...
2025-07-11T19:23:53.507Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: (800) 572-0470...
2025-07-11T19:23:53.508Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Email...
2025-07-11T19:23:53.509Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Support:...
2025-07-11T19:23:53.510Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Email...
2025-07-11T19:23:53.511Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Since 2010 TigerConnect has be...
2025-07-11T19:23:53.512Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: What We Do...
2025-07-11T19:23:53.514Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Clinical Collaboration...
2025-07-11T19:23:53.514Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Alarm Management and Event Not...
2025-07-11T19:23:53.515Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Patient Engagement...
2025-07-11T19:23:53.518Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Physician Scheduling...
2025-07-11T19:23:53.519Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: CareConduit...
2025-07-11T19:23:53.520Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Workflows...
2025-07-11T19:23:53.521Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Who We Serve...
2025-07-11T19:23:53.522Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Healthcare Organizations...
2025-07-11T19:23:53.522Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Healthcare Professionals...
2025-07-11T19:23:53.523Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Physicians...
2025-07-11T19:23:53.524Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Nurses...
2025-07-11T19:23:53.525Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Executives...
2025-07-11T19:23:53.526Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: About Us...
2025-07-11T19:23:53.526Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Why TigerConnect?...
2025-07-11T19:23:53.527Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Careers...
2025-07-11T19:23:53.528Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Leadership...
2025-07-11T19:23:53.529Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Media Coverage...
2025-07-11T19:23:53.530Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Newsroom...
2025-07-11T19:23:53.533Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Partners...
2025-07-11T19:23:53.534Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Resources...
2025-07-11T19:23:53.535Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: App Download...
2025-07-11T19:23:53.536Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Blog Articles...
2025-07-11T19:23:53.537Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Case Studies...
2025-07-11T19:23:53.537Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Demo Tours...
2025-07-11T19:23:53.538Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Ebooks...
2025-07-11T19:23:53.539Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Webinars...
2025-07-11T19:23:53.540Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: ©2025 TigerConnect. All Rights...
2025-07-11T19:23:53.540Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Sitemap...
2025-07-11T19:23:53.541Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Accessibility...
2025-07-11T19:23:53.542Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Legal...
2025-07-11T19:23:53.543Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Privacy...
2025-07-11T19:23:53.544Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Home...
2025-07-11T19:23:53.545Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Solutions...
2025-07-11T19:23:53.546Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Toggle child menu...
2025-07-11T19:23:53.549Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Expand...
2025-07-11T19:23:53.550Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Pre-Hospital...
2025-07-11T19:23:53.551Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Physician Scheduling...
2025-07-11T19:23:53.552Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Clinical Collaboration...
2025-07-11T19:23:53.553Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Alarm Management & Event Notif...
2025-07-11T19:23:53.554Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Patient Engagement...
2025-07-11T19:23:53.554Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: CareConduit...
2025-07-11T19:23:53.555Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Workflows...
2025-07-11T19:23:53.556Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Toggle child menu...
2025-07-11T19:23:53.557Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Expand...
2025-07-11T19:23:53.558Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Critical Response...
2025-07-11T19:23:53.558Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Emergency Department Workflows...
2025-07-11T19:23:53.559Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Inpatient Workflows...
2025-07-11T19:23:53.560Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Operating Room Workflows...
2025-07-11T19:23:53.561Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Post Acute & Ambulatory Workfl...
2025-07-11T19:23:53.562Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Organizations...
2025-07-11T19:23:53.566Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Toggle child menu...
2025-07-11T19:23:53.566Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Expand...
2025-07-11T19:23:53.567Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Ambulatory Surgery Centers...
2025-07-11T19:23:53.568Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Behavioral Health...
2025-07-11T19:23:53.569Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Health Systems...
2025-07-11T19:23:53.570Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Home Health & Hospice...
2025-07-11T19:23:53.571Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Hospitals...
2025-07-11T19:23:53.572Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Physician Groups...
2025-07-11T19:23:53.573Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Skilled Nursing Facilities...
2025-07-11T19:23:53.573Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Professionals...
2025-07-11T19:23:53.574Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Toggle child menu...
2025-07-11T19:23:53.575Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Expand...
2025-07-11T19:23:53.576Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Physicians...
2025-07-11T19:23:53.577Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Nurses...
2025-07-11T19:23:53.578Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Executives...
2025-07-11T19:23:53.582Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Resources...
2025-07-11T19:23:53.583Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Toggle child menu...
2025-07-11T19:23:53.584Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Expand...
2025-07-11T19:23:53.585Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Articles...
2025-07-11T19:23:53.585Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Blog...
2025-07-11T19:23:53.586Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Case Studies...
2025-07-11T19:23:53.587Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Checklists...
2025-07-11T19:23:53.588Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Datasheets...
2025-07-11T19:23:53.589Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: eBooks...
2025-07-11T19:23:53.589Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Events...
2025-07-11T19:23:53.590Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Guides...
2025-07-11T19:23:53.591Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Infographics...
2025-07-11T19:23:53.592Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Why TigerConnect...
2025-07-11T19:23:53.593Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Toggle child menu...
2025-07-11T19:23:53.597Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Expand...
2025-07-11T19:23:53.598Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Careers...
2025-07-11T19:23:53.599Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Contact Us...
2025-07-11T19:23:53.600Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Leadership...
2025-07-11T19:23:53.601Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Media Coverage...
2025-07-11T19:23:53.601Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Partners...
2025-07-11T19:23:53.602Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Contact Us...
2025-07-11T19:23:53.603Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Download Our App...
2025-07-11T19:23:53.604Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced contrast analysis: Get a Demo...
2025-07-11T19:23:53.614Z [DEBUG] - 💾 Cache saved to file: 0ff4d87e84bfce9b867fa42f05128899.json (key: rule:WCAG-004:WCAG-004...)
2025-07-11T19:23:53.614Z [DEBUG] - 💾 Cached: rule:rule:WCAG-004:WCAG-004 (49201 bytes)
2025-07-11T19:23:53.615Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-004 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:23:53.616Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:53.617Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:23:53.619Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:23:53.844Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:23:53.845Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-004 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":230}
2025-07-11T19:23:53.846Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-004: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:53.847Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-004"]}
2025-07-11T19:23:53.849Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-004
2025-07-11T19:23:53.850Z [DEBUG] - 🔧 Utility performance recorded for WCAG-004: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:53.851Z [DEBUG] - 🔧 Utility analysis completed for WCAG-004: - {"utilitiesUsed":3,"errors":0,"executionTime":536}
2025-07-11T19:23:53.852Z [DEBUG] - ⏱️ Check WCAG-004 completed in 542ms (success: true)
2025-07-11T19:23:53.853Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.858Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.859Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 6% (4/66)
2025-07-11T19:23:53.860Z [INFO] - ✅ Rule WCAG-004 completed: passed (84/100)
2025-07-11T19:23:53.861Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.867Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.867Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 6% (4/66)
2025-07-11T19:23:53.868Z [INFO] - 🔍 Executing rule: Keyboard (WCAG-005)
2025-07-11T19:23:53.869Z [INFO] - 🎯 Advanced Focus Tracker initialized - {"customIndicators":true,"flowAnalysis":true,"accessibilityTree":true}
2025-07-11T19:23:53.869Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-005: Keyboard
2025-07-11T19:23:53.874Z [DEBUG] - 📁 Cache file valid: eb191ab30edce7125d3414f6fd3d7fdd.json (201min remaining)
2025-07-11T19:23:53.874Z [DEBUG] - 📁 Cache loaded from file: eb191ab30edce7125d3414f6fd3d7fdd.json (key: rule:WCAG-005:053b13d2:add9231...)
2025-07-11T19:23:53.875Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-005:053b13d2:add92319...
2025-07-11T19:23:53.878Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-005:053b13d2:add92319... (accessed 2 times, age: 2334s)
2025-07-11T19:23:53.879Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-005
2025-07-11T19:23:53.880Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-005 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance","maxExecutionTime":6000}}
2025-07-11T19:23:53.881Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:23:53.882Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 4 times, age: 9s)
2025-07-11T19:23:53.883Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:53.884Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:23:53.886Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:23:53.890Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:23:53.890Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:23:53.892Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:23:53.893Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:23:53.894Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:23:53.905Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:23:53.947Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:23:53.947Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-005 - {"utilitiesUsed":["semantic-validation","component-library","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.8,"executionTime":67}
2025-07-11T19:23:53.950Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-005: - {"utilitiesUsed":4,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:53.952Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-005"]}
2025-07-11T19:23:53.954Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-005
2025-07-11T19:23:53.954Z [DEBUG] - 🔧 Utility performance recorded for WCAG-005: - {"utilitiesUsed":4,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:53.955Z [DEBUG] - 🔧 Utility analysis completed for WCAG-005: - {"utilitiesUsed":4,"errors":0,"executionTime":82}
2025-07-11T19:23:53.956Z [DEBUG] - ⏱️ Check WCAG-005 completed in 88ms (success: true)
2025-07-11T19:23:53.957Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.970Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.970Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 8% (5/66)
2025-07-11T19:23:53.971Z [INFO] - ✅ Rule WCAG-005 completed: failed (0/100)
2025-07-11T19:23:53.974Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.980Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:53.983Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 8% (5/66)
2025-07-11T19:23:53.985Z [INFO] - 🔍 Executing rule: Focus Order (WCAG-006)
2025-07-11T19:23:53.987Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-006: Focus Order
2025-07-11T19:23:53.992Z [DEBUG] - 📁 Cache file valid: aded2fa29b7430a4f21506ddc4347201.json (201min remaining)
2025-07-11T19:23:53.992Z [DEBUG] - 📁 Cache loaded from file: aded2fa29b7430a4f21506ddc4347201.json (key: rule:WCAG-006:053b13d2:add9231...)
2025-07-11T19:23:53.994Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-006:053b13d2:add92319...
2025-07-11T19:23:53.994Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-006:053b13d2:add92319... (accessed 2 times, age: 2333s)
2025-07-11T19:23:53.998Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-006
2025-07-11T19:23:53.999Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-006 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:23:54.002Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:54.004Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:23:54.007Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:23:54.285Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:23:54.285Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-006 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":286}
2025-07-11T19:23:54.287Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-006: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:54.289Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-006"]}
2025-07-11T19:23:54.290Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-006
2025-07-11T19:23:54.291Z [DEBUG] - 🔧 Utility performance recorded for WCAG-006: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:54.291Z [DEBUG] - 🔧 Utility analysis completed for WCAG-006: - {"utilitiesUsed":3,"errors":0,"executionTime":302}
2025-07-11T19:23:54.292Z [DEBUG] - ⏱️ Check WCAG-006 completed in 308ms (success: true)
2025-07-11T19:23:54.293Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:54.300Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:54.301Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 9% (6/66)
2025-07-11T19:23:54.301Z [INFO] - ✅ Rule WCAG-006 completed: failed (0/100)
2025-07-11T19:23:54.302Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:54.307Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:54.308Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 9% (6/66)
2025-07-11T19:23:54.309Z [INFO] - 🔍 Executing rule: Focus Visible (WCAG-007)
2025-07-11T19:23:54.310Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-007: Focus Visible
2025-07-11T19:23:54.313Z [DEBUG] - 📁 Cache file valid: 9a4c5d82e84ec7f7b03c5ed3286cff97.json (201min remaining)
2025-07-11T19:23:54.313Z [DEBUG] - 📁 Cache loaded from file: 9a4c5d82e84ec7f7b03c5ed3286cff97.json (key: rule:WCAG-007:053b13d2:add9231...)
2025-07-11T19:23:54.314Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-007:053b13d2:add92319...
2025-07-11T19:23:54.314Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-007:053b13d2:add92319... (accessed 2 times, age: 2327s)
2025-07-11T19:23:54.315Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-007
2025-07-11T19:23:54.316Z [DEBUG] - 📁 Cache file not found: 66590bdb174fd172257ef9c340e7644b.json (key: rule:WCAG-007:WCAG-007...)
2025-07-11T19:23:54.317Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-007:WCAG-007...
2025-07-11T19:23:54.317Z [DEBUG] - 📊 Cache stats: 10 hits, 9 misses, 11 entries
2025-07-11T19:23:54.320Z [DEBUG] - 💾 Cache saved to file: 66590bdb174fd172257ef9c340e7644b.json (key: rule:WCAG-007:WCAG-007...)
2025-07-11T19:23:54.322Z [DEBUG] - 💾 Cached: rule:rule:WCAG-007:WCAG-007 (1151 bytes)
2025-07-11T19:23:54.322Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-007 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-11T19:23:54.324Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:54.325Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:23:54.327Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:23:54.667Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:23:54.668Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-007 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":346}
2025-07-11T19:23:54.670Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-007: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:54.672Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-007"]}
2025-07-11T19:23:54.673Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-007
2025-07-11T19:23:54.674Z [DEBUG] - 🔧 Utility performance recorded for WCAG-007: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:54.675Z [DEBUG] - 🔧 Utility analysis completed for WCAG-007: - {"utilitiesUsed":3,"errors":0,"executionTime":361}
2025-07-11T19:23:54.675Z [DEBUG] - ⏱️ Check WCAG-007 completed in 366ms (success: true)
2025-07-11T19:23:54.676Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:54.697Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:54.697Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 11% (7/66)
2025-07-11T19:23:54.698Z [INFO] - ✅ Rule WCAG-007 completed: failed (0/100)
2025-07-11T19:23:54.699Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:54.710Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:54.711Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 11% (7/66)
2025-07-11T19:23:54.713Z [INFO] - 🔍 Executing rule: Error Identification (WCAG-008)
2025-07-11T19:23:54.715Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-008: Error Identification
2025-07-11T19:23:54.717Z [DEBUG] - 📁 Cache file valid: bb32671dcdbbfbee57b68a03d0095226.json (201min remaining)
2025-07-11T19:23:54.718Z [DEBUG] - 📁 Cache loaded from file: bb32671dcdbbfbee57b68a03d0095226.json (key: rule:WCAG-008:053b13d2:add9231...)
2025-07-11T19:23:54.719Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-008:053b13d2:add92319...
2025-07-11T19:23:54.720Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-008:053b13d2:add92319... (accessed 2 times, age: 2321s)
2025-07-11T19:23:54.721Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-008
2025-07-11T19:23:54.722Z [DEBUG] - 📁 Cache file not found: 5636c0a4dfa080d45f02d992a3ef3ca0.json (key: rule:WCAG-008:WCAG-008...)
2025-07-11T19:23:54.725Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-008:WCAG-008...
2025-07-11T19:23:54.728Z [DEBUG] - 📊 Cache stats: 11 hits, 10 misses, 13 entries
2025-07-11T19:23:54.732Z [DEBUG] - 💾 Cache saved to file: 5636c0a4dfa080d45f02d992a3ef3ca0.json (key: rule:WCAG-008:WCAG-008...)
2025-07-11T19:23:54.732Z [DEBUG] - 💾 Cached: rule:rule:WCAG-008:WCAG-008 (2 bytes)
2025-07-11T19:23:54.733Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-008 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T19:23:54.734Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:23:54.735Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 5 times, age: 10s)
2025-07-11T19:23:54.736Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:54.742Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:23:54.744Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:23:54.745Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:23:54.746Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:23:54.747Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:23:54.748Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:23:54.774Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:23:54.842Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:23:54.843Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-008 - {"utilitiesUsed":["semantic-validation","component-library","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.55,"executionTime":110}
2025-07-11T19:23:54.847Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-008: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:54.848Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-008"]}
2025-07-11T19:23:54.849Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-008
2025-07-11T19:23:54.851Z [DEBUG] - 🔧 Utility performance recorded for WCAG-008: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:54.852Z [DEBUG] - 🔧 Utility analysis completed for WCAG-008: - {"utilitiesUsed":3,"errors":0,"executionTime":134}
2025-07-11T19:23:54.855Z [DEBUG] - ⏱️ Check WCAG-008 completed in 142ms (success: true)
2025-07-11T19:23:54.856Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:54.866Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:54.866Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 12% (8/66)
2025-07-11T19:23:54.867Z [INFO] - ✅ Rule WCAG-008 completed: failed (0/100)
2025-07-11T19:23:54.869Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:54.877Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:54.878Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 12% (8/66)
2025-07-11T19:23:54.880Z [INFO] - 🔍 Executing rule: Name, Role, Value (WCAG-009)
2025-07-11T19:23:54.882Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-009: Name, Role, Value
2025-07-11T19:23:54.900Z [DEBUG] - 📁 Cache file valid: 522df832f3eda7590f6dac8c294d7b41.json (201min remaining)
2025-07-11T19:23:54.901Z [DEBUG] - 📁 Cache loaded from file: 522df832f3eda7590f6dac8c294d7b41.json (key: rule:WCAG-009:053b13d2:add9231...)
2025-07-11T19:23:54.902Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-009:053b13d2:add92319...
2025-07-11T19:23:54.904Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-009:053b13d2:add92319... (accessed 2 times, age: 2321s)
2025-07-11T19:23:54.905Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-009
2025-07-11T19:23:54.911Z [DEBUG] - 📁 Cache file not found: 34916568035c9e734a74675c48ff76be.json (key: rule:WCAG-009:WCAG-009...)
2025-07-11T19:23:54.913Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-009:WCAG-009...
2025-07-11T19:23:54.914Z [DEBUG] - 📊 Cache stats: 13 hits, 11 misses, 15 entries
2025-07-11T19:23:54.916Z [DEBUG] - 🔍 Skipping low-quality evidence: Name, role, value analysis summary
2025-07-11T19:23:54.920Z [DEBUG] - 📊 Reached max evidence items limit: 50
2025-07-11T19:23:54.926Z [DEBUG] - 💾 Cache saved to file: 34916568035c9e734a74675c48ff76be.json (key: rule:WCAG-009:WCAG-009...)
2025-07-11T19:23:54.927Z [DEBUG] - 💾 Cached: rule:rule:WCAG-009:WCAG-009 (40071 bytes)
2025-07-11T19:23:54.930Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-009 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T19:23:54.931Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:23:54.932Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 6 times, age: 10s)
2025-07-11T19:23:54.934Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:54.936Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:23:54.940Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:23:54.948Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:23:54.951Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:23:54.954Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:23:54.958Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:23:54.963Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:23:54.982Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:23:55.067Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:23:55.068Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-009 - {"utilitiesUsed":["semantic-validation","component-library","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.8,"executionTime":138}
2025-07-11T19:23:55.070Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-009: - {"utilitiesUsed":4,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:55.072Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-009"]}
2025-07-11T19:23:55.073Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-009
2025-07-11T19:23:55.075Z [DEBUG] - 🔧 Utility performance recorded for WCAG-009: - {"utilitiesUsed":4,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:55.076Z [DEBUG] - 🔧 Utility analysis completed for WCAG-009: - {"utilitiesUsed":4,"errors":0,"executionTime":190}
2025-07-11T19:23:55.078Z [DEBUG] - ⏱️ Check WCAG-009 completed in 197ms (success: true)
2025-07-11T19:23:55.079Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:55.109Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:55.109Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 14% (9/66)
2025-07-11T19:23:55.113Z [INFO] - ✅ Rule WCAG-009 completed: failed (0/100)
2025-07-11T19:23:55.114Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:55.120Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:55.121Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 14% (9/66)
2025-07-11T19:23:55.123Z [INFO] - 🔍 Executing rule: Focus Not Obscured (Minimum) (WCAG-010)
2025-07-11T19:23:55.125Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-010: Focus Not Obscured (Minimum)
2025-07-11T19:23:55.130Z [DEBUG] - 📁 Cache file valid: c7f67cafb3127a567cf8a3539f1a7177.json (201min remaining)
2025-07-11T19:23:55.131Z [DEBUG] - 📁 Cache loaded from file: c7f67cafb3127a567cf8a3539f1a7177.json (key: rule:WCAG-010:053b13d2:add9231...)
2025-07-11T19:23:55.132Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-010:053b13d2:add92319...
2025-07-11T19:23:55.132Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-010:053b13d2:add92319... (accessed 2 times, age: 2315s)
2025-07-11T19:23:55.133Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-010
2025-07-11T19:23:55.135Z [DEBUG] - 📁 Cache file not found: f6cf8fd93bde44c57db5a55674f627a3.json (key: rule:WCAG-010:WCAG-010...)
2025-07-11T19:23:55.135Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-010:WCAG-010...
2025-07-11T19:23:55.136Z [DEBUG] - 📊 Cache stats: 15 hits, 12 misses, 17 entries
2025-07-11T19:23:55.143Z [DEBUG] - 📊 Reached max evidence items limit: 30
2025-07-11T19:23:55.147Z [DEBUG] - 💾 Cache saved to file: f6cf8fd93bde44c57db5a55674f627a3.json (key: rule:WCAG-010:WCAG-010...)
2025-07-11T19:23:55.147Z [DEBUG] - 💾 Cached: rule:rule:WCAG-010:WCAG-010 (39969 bytes)
2025-07-11T19:23:55.148Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-010 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:23:55.149Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:55.151Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:23:55.152Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:23:55.648Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:23:55.649Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-010 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":501}
2025-07-11T19:23:55.652Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-010: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:55.654Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-010"]}
2025-07-11T19:23:55.656Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-010
2025-07-11T19:23:55.658Z [DEBUG] - 🔧 Utility performance recorded for WCAG-010: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:55.659Z [DEBUG] - 🔧 Utility analysis completed for WCAG-010: - {"utilitiesUsed":3,"errors":0,"executionTime":528}
2025-07-11T19:23:55.662Z [DEBUG] - ⏱️ Check WCAG-010 completed in 539ms (success: true)
2025-07-11T19:23:55.664Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:55.671Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:55.673Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 15% (10/66)
2025-07-11T19:23:55.675Z [INFO] - ✅ Rule WCAG-010 completed: failed (0/100)
2025-07-11T19:23:55.676Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:55.689Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:55.689Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 15% (10/66)
2025-07-11T19:23:55.690Z [INFO] - 🔍 Executing rule: Focus Not Obscured (Enhanced) (WCAG-011)
2025-07-11T19:23:55.693Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-011: Focus Not Obscured (Enhanced)
2025-07-11T19:23:55.699Z [DEBUG] - 📁 Cache file valid: dfd71b9d9cc1800d4496a7c460251c3a.json (201min remaining)
2025-07-11T19:23:55.699Z [DEBUG] - 📁 Cache loaded from file: dfd71b9d9cc1800d4496a7c460251c3a.json (key: rule:WCAG-011:053b13d2:add9231...)
2025-07-11T19:23:55.700Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-011:053b13d2:add92319...
2025-07-11T19:23:55.701Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-011:053b13d2:add92319... (accessed 2 times, age: 2311s)
2025-07-11T19:23:55.702Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-011
2025-07-11T19:23:55.706Z [DEBUG] - 📁 Cache file not found: 2d7893c266112410e8df0c36fce66f10.json (key: rule:WCAG-011:WCAG-011...)
2025-07-11T19:23:55.707Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-011:WCAG-011...
2025-07-11T19:23:55.709Z [DEBUG] - 📊 Cache stats: 16 hits, 13 misses, 19 entries
2025-07-11T19:23:55.710Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced focus visibility analysis summary
2025-07-11T19:23:55.713Z [DEBUG] - 📊 Reached max evidence items limit: 25
2025-07-11T19:23:55.717Z [DEBUG] - 💾 Cache saved to file: 2d7893c266112410e8df0c36fce66f10.json (key: rule:WCAG-011:WCAG-011...)
2025-07-11T19:23:55.718Z [DEBUG] - 💾 Cached: rule:rule:WCAG-011:WCAG-011 (37016 bytes)
2025-07-11T19:23:55.719Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-011 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:23:55.722Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:55.724Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:23:55.725Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:23:55.870Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:23:56.031Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:23:56.032Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-011 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":313}
2025-07-11T19:23:56.034Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-011: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:56.037Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-011"]}
2025-07-11T19:23:56.038Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-011
2025-07-11T19:23:56.039Z [DEBUG] - 🔧 Utility performance recorded for WCAG-011: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:56.039Z [DEBUG] - 🔧 Utility analysis completed for WCAG-011: - {"utilitiesUsed":3,"errors":0,"executionTime":344}
2025-07-11T19:23:56.040Z [DEBUG] - ⏱️ Check WCAG-011 completed in 350ms (success: true)
2025-07-11T19:23:56.041Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:56.047Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:56.047Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 17% (11/66)
2025-07-11T19:23:56.048Z [INFO] - ✅ Rule WCAG-011 completed: passed (96/100)
2025-07-11T19:23:56.049Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:56.062Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:56.063Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 17% (11/66)
2025-07-11T19:23:56.068Z [INFO] - 🔍 Executing rule: Focus Appearance (WCAG-012)
2025-07-11T19:23:56.073Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-012: Focus Appearance
2025-07-11T19:23:56.091Z [DEBUG] - 📁 Cache file valid: 0c667bedbf8513d49159bff73bef4407.json (202min remaining)
2025-07-11T19:23:56.097Z [DEBUG] - 📁 Cache loaded from file: 0c667bedbf8513d49159bff73bef4407.json (key: rule:WCAG-012:053b13d2:add9231...)
2025-07-11T19:23:56.099Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-012:053b13d2:add92319...
2025-07-11T19:23:56.101Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-012:053b13d2:add92319... (accessed 2 times, age: 2303s)
2025-07-11T19:23:56.104Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-012
2025-07-11T19:23:56.106Z [DEBUG] - 📁 Cache file not found: a983ce863de083e10363d128f8b48cef.json (key: rule:WCAG-012:WCAG-012...)
2025-07-11T19:23:56.120Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-012:WCAG-012...
2025-07-11T19:23:56.121Z [DEBUG] - 📊 Cache stats: 17 hits, 14 misses, 21 entries
2025-07-11T19:23:56.132Z [DEBUG] - 📊 Reached max evidence items limit: 25
2025-07-11T19:23:56.137Z [DEBUG] - 💾 Cache saved to file: a983ce863de083e10363d128f8b48cef.json (key: rule:WCAG-012:WCAG-012...)
2025-07-11T19:23:56.139Z [DEBUG] - 💾 Cached: rule:rule:WCAG-012:WCAG-012 (20134 bytes)
2025-07-11T19:23:56.140Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-012 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:23:56.141Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:56.143Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:23:56.147Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:23:56.218Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:23:56.220Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:23:56.221Z [WARN] - ❌ Utility cms-detection failed after 2 attempts, using fallback - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:23:56.605Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:23:56.605Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-012 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":465}
2025-07-11T19:23:56.608Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-012: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:56.610Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-012"]}
2025-07-11T19:23:56.612Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-012
2025-07-11T19:23:56.613Z [DEBUG] - 🔧 Utility performance recorded for WCAG-012: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:56.613Z [DEBUG] - 🔧 Utility analysis completed for WCAG-012: - {"utilitiesUsed":3,"errors":0,"executionTime":540}
2025-07-11T19:23:56.614Z [DEBUG] - ⏱️ Check WCAG-012 completed in 546ms (success: true)
2025-07-11T19:23:56.617Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:56.622Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:56.622Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 18% (12/66)
2025-07-11T19:23:56.623Z [INFO] - ✅ Rule WCAG-012 completed: failed (0/100)
2025-07-11T19:23:56.624Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:56.628Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:56.629Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 18% (12/66)
2025-07-11T19:23:56.630Z [INFO] - 🔍 Executing rule: Dragging Movements (WCAG-013)
2025-07-11T19:23:56.634Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-013: Dragging Movements
2025-07-11T19:23:56.638Z [DEBUG] - 📁 Cache file valid: 8eb5dc08bb8f57622a095101dfffb896.json (202min remaining)
2025-07-11T19:23:56.638Z [DEBUG] - 📁 Cache loaded from file: 8eb5dc08bb8f57622a095101dfffb896.json (key: rule:WCAG-013:053b13d2:add9231...)
2025-07-11T19:23:56.639Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-013:053b13d2:add92319...
2025-07-11T19:23:56.640Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-013:053b13d2:add92319... (accessed 2 times, age: 2302s)
2025-07-11T19:23:56.640Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-013
2025-07-11T19:23:56.642Z [DEBUG] - 📁 Cache file not found: d1078428b048aef2da3ae8d89dafe32f.json (key: rule:WCAG-013:WCAG-013...)
2025-07-11T19:23:56.642Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-013:WCAG-013...
2025-07-11T19:23:56.643Z [DEBUG] - 📊 Cache stats: 18 hits, 15 misses, 23 entries
2025-07-11T19:23:56.648Z [DEBUG] - 💾 Cache saved to file: d1078428b048aef2da3ae8d89dafe32f.json (key: rule:WCAG-013:WCAG-013...)
2025-07-11T19:23:56.649Z [DEBUG] - 💾 Cached: rule:rule:WCAG-013:WCAG-013 (4316 bytes)
2025-07-11T19:23:56.650Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-013 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T19:23:56.651Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:56.653Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:23:56.871Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:23:56.871Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-013 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":221}
2025-07-11T19:23:56.873Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-013: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:56.875Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-013"]}
2025-07-11T19:23:56.876Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-013
2025-07-11T19:23:56.877Z [DEBUG] - 🔧 Utility performance recorded for WCAG-013: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:56.878Z [DEBUG] - 🔧 Utility analysis completed for WCAG-013: - {"utilitiesUsed":2,"errors":0,"executionTime":244}
2025-07-11T19:23:56.879Z [DEBUG] - ⏱️ Check WCAG-013 completed in 250ms (success: true)
2025-07-11T19:23:56.879Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:56.886Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:56.886Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 20% (13/66)
2025-07-11T19:23:56.887Z [INFO] - ✅ Rule WCAG-013 completed: failed (0/100)
2025-07-11T19:23:56.887Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:56.893Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:56.896Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 20% (13/66)
2025-07-11T19:23:56.896Z [INFO] - 🔍 Executing rule: Target Size (Minimum) (WCAG-014)
2025-07-11T19:23:56.898Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-014: Target Size
2025-07-11T19:23:56.902Z [DEBUG] - 📁 Cache file valid: d0669986b28d58902c30404e9beb5b88.json (202min remaining)
2025-07-11T19:23:56.902Z [DEBUG] - 📁 Cache loaded from file: d0669986b28d58902c30404e9beb5b88.json (key: rule:WCAG-014:053b13d2:add9231...)
2025-07-11T19:23:56.903Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-014:053b13d2:add92319...
2025-07-11T19:23:56.904Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-014:053b13d2:add92319... (accessed 2 times, age: 2301s)
2025-07-11T19:23:56.905Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-014
2025-07-11T19:23:56.906Z [DEBUG] - 📁 Cache file not found: a94055f9057efb029ff7b94b4e9d14bf.json (key: rule:WCAG-014:WCAG-014...)
2025-07-11T19:23:56.906Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-014:WCAG-014...
2025-07-11T19:23:56.907Z [DEBUG] - 📊 Cache stats: 19 hits, 16 misses, 25 entries
2025-07-11T19:23:56.912Z [DEBUG] - 📊 Reached max evidence items limit: 40
2025-07-11T19:23:56.915Z [DEBUG] - 💾 Cache saved to file: a94055f9057efb029ff7b94b4e9d14bf.json (key: rule:WCAG-014:WCAG-014...)
2025-07-11T19:23:56.915Z [DEBUG] - 💾 Cached: rule:rule:WCAG-014:WCAG-014 (78203 bytes)
2025-07-11T19:23:56.916Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-014 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-11T19:23:56.917Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:56.918Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:23:57.140Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:23:57.140Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-014 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":224}
2025-07-11T19:23:57.142Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-014: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:57.145Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-014"]}
2025-07-11T19:23:57.145Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-014
2025-07-11T19:23:57.146Z [DEBUG] - 🔧 Utility performance recorded for WCAG-014: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:57.147Z [DEBUG] - 🔧 Utility analysis completed for WCAG-014: - {"utilitiesUsed":2,"errors":0,"executionTime":246}
2025-07-11T19:23:57.148Z [DEBUG] - ⏱️ Check WCAG-014 completed in 252ms (success: true)
2025-07-11T19:23:57.148Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:57.154Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:57.154Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 21% (14/66)
2025-07-11T19:23:57.155Z [INFO] - ✅ Rule WCAG-014 completed: passed (81/100)
2025-07-11T19:23:57.155Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:57.161Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:57.161Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 21% (14/66)
2025-07-11T19:23:57.162Z [INFO] - 🔍 Executing rule: Consistent Help (WCAG-015)
2025-07-11T19:23:57.163Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-015: Consistent Help
2025-07-11T19:23:57.165Z [DEBUG] - 📁 Cache file valid: 615e6a7096e2644d471b6fe2e8e883b6.json (202min remaining)
2025-07-11T19:23:57.166Z [DEBUG] - 📁 Cache loaded from file: 615e6a7096e2644d471b6fe2e8e883b6.json (key: rule:WCAG-015:053b13d2:add9231...)
2025-07-11T19:23:57.166Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-015:053b13d2:add92319...
2025-07-11T19:23:57.168Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-015:053b13d2:add92319... (accessed 2 times, age: 2301s)
2025-07-11T19:23:57.169Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-015
2025-07-11T19:23:57.170Z [DEBUG] - 📁 Cache file not found: 1f44b261232b169a974c86dd17ae2cfd.json (key: rule:WCAG-035:WCAG-035...)
2025-07-11T19:23:57.170Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-035:WCAG-035...
2025-07-11T19:23:57.171Z [DEBUG] - 📊 Cache stats: 20 hits, 17 misses, 27 entries
2025-07-11T19:23:57.174Z [DEBUG] - 💾 Cache saved to file: 1f44b261232b169a974c86dd17ae2cfd.json (key: rule:WCAG-035:WCAG-035...)
2025-07-11T19:23:57.177Z [DEBUG] - 💾 Cached: rule:rule:WCAG-035:WCAG-035 (3118 bytes)
2025-07-11T19:23:57.178Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-015 - {"config":{"enablePatternValidation":true,"enableContentQualityAnalysis":true,"integrationStrategy":"supplement"}}
2025-07-11T19:23:57.178Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:57.180Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:23:57.220Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:23:57.418Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:23:57.419Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-015 - {"utilitiesUsed":["content-quality","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.30000000000000004,"executionTime":241}
2025-07-11T19:23:57.421Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-015: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:57.424Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-015"]}
2025-07-11T19:23:57.425Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-015
2025-07-11T19:23:57.426Z [DEBUG] - 🔧 Utility performance recorded for WCAG-015: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:57.426Z [DEBUG] - 🔧 Utility analysis completed for WCAG-015: - {"utilitiesUsed":2,"errors":0,"executionTime":258}
2025-07-11T19:23:57.427Z [DEBUG] - ⏱️ Check WCAG-015 completed in 265ms (success: true)
2025-07-11T19:23:57.428Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:57.437Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:57.439Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 23% (15/66)
2025-07-11T19:23:57.440Z [INFO] - ✅ Rule WCAG-015 completed: passed (100/100)
2025-07-11T19:23:57.441Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:57.448Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:57.449Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 23% (15/66)
2025-07-11T19:23:57.450Z [INFO] - 🔍 Executing rule: Redundant Entry (WCAG-016)
2025-07-11T19:23:57.452Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-016: Redundant Entry
2025-07-11T19:23:57.456Z [DEBUG] - 📁 Cache file valid: b70f590db3b5e9a41c5f32eaf6a51d68.json (202min remaining)
2025-07-11T19:23:57.456Z [DEBUG] - 📁 Cache loaded from file: b70f590db3b5e9a41c5f32eaf6a51d68.json (key: rule:WCAG-016:053b13d2:add9231...)
2025-07-11T19:23:57.457Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-016:053b13d2:add92319...
2025-07-11T19:23:57.458Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-016:053b13d2:add92319... (accessed 2 times, age: 2300s)
2025-07-11T19:23:57.459Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-016
2025-07-11T19:23:57.460Z [DEBUG] - 📁 Cache file not found: 6da8ac9bbbdbdd7837c3e96457db6c2b.json (key: rule:WCAG-016:WCAG-016...)
2025-07-11T19:23:57.460Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-016:WCAG-016...
2025-07-11T19:23:57.461Z [DEBUG] - 📊 Cache stats: 21 hits, 18 misses, 29 entries
2025-07-11T19:23:57.463Z [DEBUG] - 💾 Cache saved to file: 6da8ac9bbbdbdd7837c3e96457db6c2b.json (key: rule:WCAG-016:WCAG-016...)
2025-07-11T19:23:57.464Z [DEBUG] - 💾 Cached: rule:rule:WCAG-016:WCAG-016 (1576 bytes)
2025-07-11T19:23:57.465Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-016 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T19:23:57.465Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:57.467Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:23:58.060Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:23:58.060Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-016 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":596}
2025-07-11T19:23:58.062Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-016: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:58.064Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-016"]}
2025-07-11T19:23:58.065Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-016
2025-07-11T19:23:58.066Z [DEBUG] - 🔧 Utility performance recorded for WCAG-016: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:58.066Z [DEBUG] - 🔧 Utility analysis completed for WCAG-016: - {"utilitiesUsed":2,"errors":0,"executionTime":612}
2025-07-11T19:23:58.067Z [DEBUG] - ⏱️ Check WCAG-016 completed in 617ms (success: true)
2025-07-11T19:23:58.068Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:58.073Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:58.073Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 24% (16/66)
2025-07-11T19:23:58.074Z [INFO] - ✅ Rule WCAG-016 completed: failed (0/100)
2025-07-11T19:23:58.074Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:58.079Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:58.080Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 24% (16/66)
2025-07-11T19:23:58.081Z [INFO] - 🔍 Executing rule: Image Alternatives (WCAG-017)
2025-07-11T19:23:58.082Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-017: Image Alternatives 3.0
2025-07-11T19:23:58.085Z [DEBUG] - 📁 Cache file valid: 53486f7d95f53e31b4f502c079eb6a98.json (202min remaining)
2025-07-11T19:23:58.085Z [DEBUG] - 📁 Cache loaded from file: 53486f7d95f53e31b4f502c079eb6a98.json (key: rule:WCAG-017:053b13d2:add9231...)
2025-07-11T19:23:58.086Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-017:053b13d2:add92319...
2025-07-11T19:23:58.087Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-017:053b13d2:add92319... (accessed 2 times, age: 2300s)
2025-07-11T19:23:58.088Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-017
2025-07-11T19:23:58.089Z [DEBUG] - 📁 Cache file not found: b716ddf260e1c8c0f7e3ff46e385bea8.json (key: rule:WCAG-017:WCAG-017...)
2025-07-11T19:23:58.089Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-017:WCAG-017...
2025-07-11T19:23:58.090Z [DEBUG] - 📊 Cache stats: 22 hits, 19 misses, 31 entries
2025-07-11T19:23:58.095Z [DEBUG] - 📊 Reached max evidence items limit: 40
2025-07-11T19:23:58.098Z [DEBUG] - 💾 Cache saved to file: b716ddf260e1c8c0f7e3ff46e385bea8.json (key: rule:WCAG-017:WCAG-017...)
2025-07-11T19:23:58.099Z [DEBUG] - 💾 Cached: rule:rule:WCAG-017:WCAG-017 (34781 bytes)
2025-07-11T19:23:58.099Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-017 - {"config":{"enableSemanticValidation":true,"enableContentQualityAnalysis":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-11T19:23:58.100Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:23:58.101Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 7 times, age: 13s)
2025-07-11T19:23:58.102Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:23:58.103Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:23:58.105Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:23:58.105Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:23:58.106Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:23:58.108Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:23:58.108Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:23:58.140Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:23:58.162Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:23:58.169Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:23:58.169Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-017 - {"utilitiesUsed":["content-quality","semantic-validation","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.44999999999999996,"executionTime":70}
2025-07-11T19:23:58.170Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-017: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:23:58.171Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-017"]}
2025-07-11T19:23:58.172Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-017
2025-07-11T19:23:58.172Z [DEBUG] - 🔧 Utility performance recorded for WCAG-017: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:23:58.173Z [DEBUG] - 🔧 Utility analysis completed for WCAG-017: - {"utilitiesUsed":3,"errors":0,"executionTime":89}
2025-07-11T19:23:58.178Z [DEBUG] - ⏱️ Check WCAG-017 completed in 98ms (success: true)
2025-07-11T19:23:58.179Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:58.183Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:58.183Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 26% (17/66)
2025-07-11T19:23:58.184Z [INFO] - ✅ Rule WCAG-017 completed: failed (0/100)
2025-07-11T19:23:58.184Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:58.192Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:23:58.192Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 26% (17/66)
2025-07-11T19:23:58.193Z [INFO] - 🔍 Executing rule: Text and Wording (WCAG-018)
2025-07-11T19:23:58.194Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-018: Text and Wording
2025-07-11T19:23:58.196Z [DEBUG] - 📁 Cache file valid: a2c4aa8bae556a8c52fdf3bbbf5f2a0c.json (202min remaining)
2025-07-11T19:23:58.197Z [DEBUG] - 📁 Cache loaded from file: a2c4aa8bae556a8c52fdf3bbbf5f2a0c.json (key: rule:WCAG-018:053b13d2:add9231...)
2025-07-11T19:23:58.198Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-018:053b13d2:add92319...
2025-07-11T19:23:58.198Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-018:053b13d2:add92319... (accessed 2 times, age: 2295s)
2025-07-11T19:23:58.199Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-018
2025-07-11T19:23:58.201Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-018 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"enableCMSDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T19:23:58.203Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:23:58.204Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 8 times, age: 13s)
2025-07-11T19:23:58.205Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:23:58.206Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:23:58.207Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:23:58.208Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:23:58.209Z [DEBUG] - 📊 Cache stats: 25 hits, 20 misses, 9 entries
2025-07-11T19:23:58.210Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:23:58.211Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:23:58.215Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:23:58.216Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:23:58.217Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:23:58.239Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:23:58.258Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:24:00.886Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:24:03.259Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:24:03.260Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:24:03.262Z [WARN] - ⚠️ Utility cms-detection failed on attempt 1, retrying... - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded","attempt":1,"maxRetries":2}
2025-07-11T19:24:04.269Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:24:04.270Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:24:04.273Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:24:04.274Z [DEBUG] - 📊 Cache stats: 25 hits, 21 misses, 9 entries
2025-07-11T19:24:05.892Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:24:08.220Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis failed for WCAG-018: - {"error":"Utility analysis timeout"}
2025-07-11T19:24:08.221Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-018: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:08.223Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-018","Utility errors detected for WCAG-018: Utility analysis error: Utility analysis timeout"]}
2025-07-11T19:24:08.225Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-018
2025-07-11T19:24:08.226Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Utility errors detected for WCAG-018: Utility analysis error: Utility analysis timeout
2025-07-11T19:24:08.227Z [DEBUG] - 🔧 Utility performance recorded for WCAG-018: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:08.228Z [DEBUG] - 🔧 Utility analysis completed for WCAG-018: - {"utilitiesUsed":2,"errors":1,"executionTime":10028}
2025-07-11T19:24:08.228Z [DEBUG] - ⏱️ Check WCAG-018 completed in 10035ms (success: true)
2025-07-11T19:24:08.229Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:08.240Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:08.240Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 27% (18/66)
2025-07-11T19:24:08.241Z [INFO] - ✅ Rule WCAG-018 completed: failed (0/100)
2025-07-11T19:24:08.242Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:08.249Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:08.250Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 27% (18/66)
2025-07-11T19:24:08.251Z [INFO] - 🔍 Executing rule: Keyboard Focus (WCAG-019)
2025-07-11T19:24:08.254Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-019: Keyboard Focus 3.0
2025-07-11T19:24:08.257Z [DEBUG] - 📁 Cache file valid: 06d0b0a9fda2257d54ba48b9253989fa.json (202min remaining)
2025-07-11T19:24:08.258Z [DEBUG] - 📁 Cache loaded from file: 06d0b0a9fda2257d54ba48b9253989fa.json (key: rule:WCAG-019:053b13d2:add9231...)
2025-07-11T19:24:08.259Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-019:053b13d2:add92319...
2025-07-11T19:24:08.260Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-019:053b13d2:add92319... (accessed 2 times, age: 2295s)
2025-07-11T19:24:08.261Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-019
2025-07-11T19:24:08.262Z [DEBUG] - 📁 Cache file not found: b6c044c1b16e4b7554869b5a2357feeb.json (key: rule:WCAG-019:WCAG-019...)
2025-07-11T19:24:08.263Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-019:WCAG-019...
2025-07-11T19:24:08.264Z [DEBUG] - 📊 Cache stats: 26 hits, 22 misses, 34 entries
2025-07-11T19:24:08.268Z [DEBUG] - 💾 Cache saved to file: b6c044c1b16e4b7554869b5a2357feeb.json (key: rule:WCAG-019:WCAG-019...)
2025-07-11T19:24:08.269Z [DEBUG] - 💾 Cached: rule:rule:WCAG-019:WCAG-019 (7878 bytes)
2025-07-11T19:24:08.270Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-019 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:24:08.271Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:08.273Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:08.274Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:24:08.524Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:08.525Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-019 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":254}
2025-07-11T19:24:08.527Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-019: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:08.529Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-019"]}
2025-07-11T19:24:08.530Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-019
2025-07-11T19:24:08.531Z [DEBUG] - 🔧 Utility performance recorded for WCAG-019: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:08.532Z [DEBUG] - 🔧 Utility analysis completed for WCAG-019: - {"utilitiesUsed":3,"errors":0,"executionTime":276}
2025-07-11T19:24:08.533Z [DEBUG] - ⏱️ Check WCAG-019 completed in 282ms (success: true)
2025-07-11T19:24:08.533Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:08.545Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:08.545Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 29% (19/66)
2025-07-11T19:24:08.546Z [INFO] - ✅ Rule WCAG-019 completed: failed (0/100)
2025-07-11T19:24:08.547Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:08.557Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:08.557Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 29% (19/66)
2025-07-11T19:24:08.559Z [INFO] - 🔍 Executing rule: Motor (WCAG-020)
2025-07-11T19:24:08.561Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-058: Motor
2025-07-11T19:24:08.567Z [DEBUG] - 📁 Cache file valid: 7c907196d9755c62f64c4de5a3c80946.json (202min remaining)
2025-07-11T19:24:08.568Z [DEBUG] - 📁 Cache loaded from file: 7c907196d9755c62f64c4de5a3c80946.json (key: rule:WCAG-058:053b13d2:add9231...)
2025-07-11T19:24:08.568Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-058:053b13d2:add92319...
2025-07-11T19:24:08.569Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-058:053b13d2:add92319... (accessed 2 times, age: 2294s)
2025-07-11T19:24:08.570Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-058
2025-07-11T19:24:08.572Z [DEBUG] - 📁 Cache file not found: 7abaf7eda8ecf9e8bf6d46bbf025d5d5.json (key: rule:WCAG-058:WCAG-058...)
2025-07-11T19:24:08.572Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-058:WCAG-058...
2025-07-11T19:24:08.573Z [DEBUG] - 📊 Cache stats: 27 hits, 23 misses, 36 entries
2025-07-11T19:24:08.577Z [DEBUG] - 📊 Reached max evidence items limit: 30
2025-07-11T19:24:08.580Z [DEBUG] - 💾 Cache saved to file: 7abaf7eda8ecf9e8bf6d46bbf025d5d5.json (key: rule:WCAG-058:WCAG-058...)
2025-07-11T19:24:08.581Z [DEBUG] - 💾 Cached: rule:rule:WCAG-058:WCAG-058 (23465 bytes)
2025-07-11T19:24:08.582Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-020 - {"config":{"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-11T19:24:08.583Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:08.584Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:08.586Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:24:08.862Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:08.862Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-020 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":280}
2025-07-11T19:24:08.864Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-020: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:08.867Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-020"]}
2025-07-11T19:24:08.868Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-020
2025-07-11T19:24:08.869Z [DEBUG] - 🔧 Utility performance recorded for WCAG-020: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:08.870Z [DEBUG] - 🔧 Utility analysis completed for WCAG-020: - {"utilitiesUsed":3,"errors":0,"executionTime":305}
2025-07-11T19:24:08.871Z [DEBUG] - ⏱️ Check WCAG-020 completed in 312ms (success: true)
2025-07-11T19:24:08.871Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:08.885Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:08.886Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 30% (20/66)
2025-07-11T19:24:08.887Z [INFO] - ✅ Rule WCAG-020 completed: failed (0/100)
2025-07-11T19:24:08.888Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:08.898Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:08.898Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 30% (20/66)
2025-07-11T19:24:08.899Z [INFO] - 🔍 Executing rule: Pronunciation & Meaning (WCAG-021)
2025-07-11T19:24:08.903Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-065: Pronunciation & Meaning
2025-07-11T19:24:08.906Z [DEBUG] - 📁 Cache file valid: 9c2a7f06b6cf1f15c73ea6cdc0f779dc.json (202min remaining)
2025-07-11T19:24:08.906Z [DEBUG] - 📁 Cache loaded from file: 9c2a7f06b6cf1f15c73ea6cdc0f779dc.json (key: rule:WCAG-065:053b13d2:add9231...)
2025-07-11T19:24:08.907Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-065:053b13d2:add92319...
2025-07-11T19:24:08.908Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-065:053b13d2:add92319... (accessed 2 times, age: 2288s)
2025-07-11T19:24:08.908Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-065
2025-07-11T19:24:08.910Z [DEBUG] - 📁 Cache file not found: 549983af08b0a1483a750f23436ed910.json (key: rule:WCAG-065:WCAG-065...)
2025-07-11T19:24:08.910Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-065:WCAG-065...
2025-07-11T19:24:08.912Z [DEBUG] - 📊 Cache stats: 28 hits, 24 misses, 38 entries
2025-07-11T19:24:08.923Z [DEBUG] - 💾 Cache saved to file: 549983af08b0a1483a750f23436ed910.json (key: rule:WCAG-065:WCAG-065...)
2025-07-11T19:24:08.924Z [DEBUG] - 💾 Cached: rule:rule:WCAG-065:WCAG-065 (4267 bytes)
2025-07-11T19:24:08.925Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-021 - {"config":{"enableFrameworkOptimization":true,"enableContentQualityAnalysis":true,"integrationStrategy":"supplement"}}
2025-07-11T19:24:08.926Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:24:08.929Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:09.002Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:24:09.018Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-021 - {"utilitiesUsed":["content-quality","framework-optimization"],"confidence":0.7,"accuracy":0.35,"executionTime":93}
2025-07-11T19:24:09.018Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-021: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:09.019Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-021"]}
2025-07-11T19:24:09.020Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-021
2025-07-11T19:24:09.021Z [DEBUG] - 🔧 Utility performance recorded for WCAG-021: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:09.024Z [DEBUG] - 🔧 Utility analysis completed for WCAG-021: - {"utilitiesUsed":2,"errors":0,"executionTime":119}
2025-07-11T19:24:09.026Z [DEBUG] - ⏱️ Check WCAG-021 completed in 127ms (success: true)
2025-07-11T19:24:09.026Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:09.032Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:09.032Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 32% (21/66)
2025-07-11T19:24:09.033Z [INFO] - ✅ Rule WCAG-021 completed: passed (75/100)
2025-07-11T19:24:09.034Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:09.039Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:09.040Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 32% (21/66)
2025-07-11T19:24:09.040Z [INFO] - 🔍 Executing rule: Accessible Authentication (Minimum) (WCAG-022)
2025-07-11T19:24:09.043Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-022: Accessible Authentication (Minimum)
2025-07-11T19:24:09.045Z [DEBUG] - 📁 Cache file valid: 3d7bab369e35af598fc9be2be218445d.json (202min remaining)
2025-07-11T19:24:09.046Z [DEBUG] - 📁 Cache loaded from file: 3d7bab369e35af598fc9be2be218445d.json (key: rule:WCAG-022:053b13d2:add9231...)
2025-07-11T19:24:09.047Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-022:053b13d2:add92319...
2025-07-11T19:24:09.047Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-022:053b13d2:add92319... (accessed 2 times, age: 2287s)
2025-07-11T19:24:09.048Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-022
2025-07-11T19:24:09.049Z [DEBUG] - 📁 Cache file not found: af5f399d3c8ce8f5add144c52f7cdb62.json (key: rule:WCAG-022:WCAG-022...)
2025-07-11T19:24:09.050Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-022:WCAG-022...
2025-07-11T19:24:09.051Z [DEBUG] - 📊 Cache stats: 29 hits, 25 misses, 40 entries
2025-07-11T19:24:09.054Z [DEBUG] - 💾 Cache saved to file: af5f399d3c8ce8f5add144c52f7cdb62.json (key: rule:WCAG-022:WCAG-022...)
2025-07-11T19:24:09.056Z [DEBUG] - 💾 Cached: rule:rule:WCAG-022:WCAG-022 (1368 bytes)
2025-07-11T19:24:09.057Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-022 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:24:09.058Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:09.060Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:09.062Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:24:09.282Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:24:09.282Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:24:09.286Z [WARN] - ❌ Utility cms-detection failed after 2 attempts, using fallback - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:24:09.331Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:09.331Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-022 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":274}
2025-07-11T19:24:09.335Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-022: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:09.336Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-022"]}
2025-07-11T19:24:09.337Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-022
2025-07-11T19:24:09.338Z [DEBUG] - 🔧 Utility performance recorded for WCAG-022: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:09.339Z [DEBUG] - 🔧 Utility analysis completed for WCAG-022: - {"utilitiesUsed":3,"errors":0,"executionTime":295}
2025-07-11T19:24:09.340Z [DEBUG] - ⏱️ Check WCAG-022 completed in 300ms (success: true)
2025-07-11T19:24:09.341Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:09.349Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:09.352Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 33% (22/66)
2025-07-11T19:24:09.353Z [INFO] - ✅ Rule WCAG-022 completed: failed (0/100)
2025-07-11T19:24:09.354Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:09.363Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:09.364Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 33% (22/66)
2025-07-11T19:24:09.365Z [INFO] - 🔍 Executing rule: Accessible Authentication (Enhanced) (WCAG-023)
🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-037: Accessible Authentication (Enhanced) (40% automated)
✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-037 in 27ms - Status: passed (100/100, Manual items: 1)
2025-07-11T19:24:09.397Z [DEBUG] - 📁 Cache file not found: ae74dbcf34d306af21f0f53115d474d0.json (key: rule:WCAG-037:WCAG-037...)
2025-07-11T19:24:09.398Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-037:WCAG-037...
2025-07-11T19:24:09.402Z [DEBUG] - 📊 Cache stats: 29 hits, 26 misses, 41 entries
2025-07-11T19:24:09.404Z [DEBUG] - 💾 Cache saved to file: ae74dbcf34d306af21f0f53115d474d0.json (key: rule:WCAG-037:WCAG-037...)
2025-07-11T19:24:09.405Z [DEBUG] - 💾 Cached: rule:rule:WCAG-037:WCAG-037 (3860 bytes)
2025-07-11T19:24:09.406Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-023 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:24:09.407Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:09.412Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:09.413Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:24:09.701Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:09.702Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-023 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":296}
2025-07-11T19:24:09.704Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-023: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:09.706Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-023"]}
2025-07-11T19:24:09.707Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-023
2025-07-11T19:24:09.708Z [DEBUG] - 🔧 Utility performance recorded for WCAG-023: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:09.709Z [DEBUG] - 🔧 Utility analysis completed for WCAG-023: - {"utilitiesUsed":3,"errors":0,"executionTime":339}
2025-07-11T19:24:09.710Z [DEBUG] - ⏱️ Check WCAG-023 completed in 345ms (success: true)
2025-07-11T19:24:09.711Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:09.727Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:09.728Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 35% (23/66)
2025-07-11T19:24:09.729Z [INFO] - ✅ Rule WCAG-023 completed: passed (100/100)
2025-07-11T19:24:09.730Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:09.740Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:09.740Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 35% (23/66)
2025-07-11T19:24:09.742Z [INFO] - 🔍 Executing rule: Timing Adjustable (WCAG-044)
2025-07-11T19:24:09.743Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-044: Timing Adjustable
2025-07-11T19:24:09.746Z [DEBUG] - 📁 Cache file valid: 704aef67aea9c54d9ef1e6ee79ac0ca6.json (202min remaining)
2025-07-11T19:24:09.746Z [DEBUG] - 📁 Cache loaded from file: 704aef67aea9c54d9ef1e6ee79ac0ca6.json (key: rule:WCAG-044:053b13d2:add9231...)
2025-07-11T19:24:09.747Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-044:053b13d2:add92319...
2025-07-11T19:24:09.750Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-044:053b13d2:add92319... (accessed 2 times, age: 2287s)
2025-07-11T19:24:09.750Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-044
2025-07-11T19:24:09.752Z [DEBUG] - 📁 Cache file not found: d3772bf99a5019bdba9365f64874aa5e.json (key: rule:WCAG-044:WCAG-044...)
2025-07-11T19:24:09.752Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-044:WCAG-044...
2025-07-11T19:24:09.753Z [DEBUG] - 📊 Cache stats: 30 hits, 27 misses, 43 entries
2025-07-11T19:24:09.756Z [DEBUG] - 💾 Cache saved to file: d3772bf99a5019bdba9365f64874aa5e.json (key: rule:WCAG-044:WCAG-044...)
2025-07-11T19:24:09.756Z [DEBUG] - 💾 Cached: rule:rule:WCAG-044:WCAG-044 (3990 bytes)
2025-07-11T19:24:09.757Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-044 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-11T19:24:09.758Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:09.760Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:09.981Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:09.981Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-044 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":224}
2025-07-11T19:24:09.985Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-044: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:09.985Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-044"]}
2025-07-11T19:24:09.986Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-044
2025-07-11T19:24:09.987Z [DEBUG] - 🔧 Utility performance recorded for WCAG-044: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:09.988Z [DEBUG] - 🔧 Utility analysis completed for WCAG-044: - {"utilitiesUsed":2,"errors":0,"executionTime":244}
2025-07-11T19:24:09.989Z [DEBUG] - ⏱️ Check WCAG-044 completed in 248ms (success: true)
2025-07-11T19:24:09.990Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:09.995Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:09.995Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 36% (24/66)
2025-07-11T19:24:09.996Z [INFO] - ✅ Rule WCAG-044 completed: passed (75/100)
2025-07-11T19:24:09.997Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.003Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.004Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 36% (24/66)
2025-07-11T19:24:10.005Z [INFO] - 🔍 Executing rule: Pause, Stop, Hide (WCAG-045)
2025-07-11T19:24:10.006Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-045: Pause, Stop, Hide
2025-07-11T19:24:10.008Z [DEBUG] - 📁 Cache file valid: 6d2097255130d86d77b56cca8dc47d41.json (202min remaining)
2025-07-11T19:24:10.008Z [DEBUG] - 📁 Cache loaded from file: 6d2097255130d86d77b56cca8dc47d41.json (key: rule:WCAG-045:053b13d2:add9231...)
2025-07-11T19:24:10.009Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-045:053b13d2:add92319...
2025-07-11T19:24:10.010Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-045:053b13d2:add92319... (accessed 2 times, age: 2286s)
2025-07-11T19:24:10.011Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-045
2025-07-11T19:24:10.012Z [DEBUG] - 📁 Cache file not found: b4cb195d784710f4a1e483314189758a.json (key: rule:WCAG-045:WCAG-045...)
2025-07-11T19:24:10.015Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-045:WCAG-045...
2025-07-11T19:24:10.018Z [DEBUG] - 📊 Cache stats: 31 hits, 28 misses, 45 entries
2025-07-11T19:24:10.021Z [DEBUG] - 💾 Cache saved to file: b4cb195d784710f4a1e483314189758a.json (key: rule:WCAG-045:WCAG-045...)
2025-07-11T19:24:10.021Z [DEBUG] - 💾 Cached: rule:rule:WCAG-045:WCAG-045 (794 bytes)
2025-07-11T19:24:10.022Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-045 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-11T19:24:10.023Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:10.024Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:10.207Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:10.207Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-045 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":185}
2025-07-11T19:24:10.209Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-045: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:10.211Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-045"]}
2025-07-11T19:24:10.212Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-045
2025-07-11T19:24:10.213Z [DEBUG] - 🔧 Utility performance recorded for WCAG-045: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:10.213Z [DEBUG] - 🔧 Utility analysis completed for WCAG-045: - {"utilitiesUsed":2,"errors":0,"executionTime":204}
2025-07-11T19:24:10.214Z [DEBUG] - ⏱️ Check WCAG-045 completed in 210ms (success: true)
2025-07-11T19:24:10.215Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.221Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.224Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 38% (25/66)
2025-07-11T19:24:10.225Z [INFO] - ✅ Rule WCAG-045 completed: failed (0/100)
2025-07-11T19:24:10.226Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.230Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.230Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 38% (25/66)
2025-07-11T19:24:10.231Z [INFO] - 🔍 Executing rule: Three Flashes or Below Threshold (WCAG-046)
2025-07-11T19:24:10.232Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-046: Three Flashes or Below Threshold
2025-07-11T19:24:10.236Z [DEBUG] - 📁 Cache file valid: 158102e146bc3397eb1885466642b63e.json (202min remaining)
2025-07-11T19:24:10.236Z [DEBUG] - 📁 Cache loaded from file: 158102e146bc3397eb1885466642b63e.json (key: rule:WCAG-046:053b13d2:add9231...)
2025-07-11T19:24:10.237Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-046:053b13d2:add92319...
2025-07-11T19:24:10.239Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-046:053b13d2:add92319... (accessed 2 times, age: 2285s)
2025-07-11T19:24:10.240Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-046
2025-07-11T19:24:10.242Z [DEBUG] - 📁 Cache file not found: 766d672458117452a6514d8b83102944.json (key: rule:WCAG-046:WCAG-046...)
2025-07-11T19:24:10.243Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-046:WCAG-046...
2025-07-11T19:24:10.244Z [DEBUG] - 📊 Cache stats: 32 hits, 29 misses, 47 entries
2025-07-11T19:24:10.244Z [DEBUG] - 📊 Reached max evidence items limit: 20
2025-07-11T19:24:10.247Z [DEBUG] - 💾 Cache saved to file: 766d672458117452a6514d8b83102944.json (key: rule:WCAG-046:WCAG-046...)
2025-07-11T19:24:10.247Z [DEBUG] - 💾 Cached: rule:rule:WCAG-046:WCAG-046 (15786 bytes)
2025-07-11T19:24:10.248Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-046 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-11T19:24:10.249Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:10.251Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:10.426Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:10.426Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-046 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":178}
2025-07-11T19:24:10.428Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-046: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:10.430Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-046"]}
2025-07-11T19:24:10.431Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-046
2025-07-11T19:24:10.432Z [DEBUG] - 🔧 Utility performance recorded for WCAG-046: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:10.433Z [DEBUG] - 🔧 Utility analysis completed for WCAG-046: - {"utilitiesUsed":2,"errors":0,"executionTime":197}
2025-07-11T19:24:10.434Z [DEBUG] - ⏱️ Check WCAG-046 completed in 202ms (success: true)
2025-07-11T19:24:10.434Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.442Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.442Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 39% (26/66)
2025-07-11T19:24:10.443Z [INFO] - ✅ Rule WCAG-046 completed: passed (99/100)
2025-07-11T19:24:10.445Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.450Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.451Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 39% (26/66)
2025-07-11T19:24:10.453Z [INFO] - 🔍 Executing rule: Resize Text (WCAG-037)
2025-07-11T19:24:10.454Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-037: Resize Text
2025-07-11T19:24:10.460Z [DEBUG] - 📁 Cache file valid: b9b75b0ca1ddfe5cab0a3790c96a1e7b.json (202min remaining)
2025-07-11T19:24:10.460Z [DEBUG] - 📁 Cache loaded from file: b9b75b0ca1ddfe5cab0a3790c96a1e7b.json (key: rule:WCAG-037:053b13d2:add9231...)
2025-07-11T19:24:10.461Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-037:053b13d2:add92319...
2025-07-11T19:24:10.461Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-037:053b13d2:add92319... (accessed 2 times, age: 2273s)
2025-07-11T19:24:10.462Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-037
2025-07-11T19:24:10.463Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-037:WCAG-037... (accessed 2 times, age: 1s)
2025-07-11T19:24:10.463Z [DEBUG] - 📋 Using cached evidence for WCAG-037
2025-07-11T19:24:10.464Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-037 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:24:10.465Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:10.466Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:10.468Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:24:10.664Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:10.664Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-037 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":200}
2025-07-11T19:24:10.667Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-037: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:10.668Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-037"]}
2025-07-11T19:24:10.669Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-037
2025-07-11T19:24:10.669Z [DEBUG] - 🔧 Utility performance recorded for WCAG-037: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:10.670Z [DEBUG] - 🔧 Utility analysis completed for WCAG-037: - {"utilitiesUsed":3,"errors":0,"executionTime":214}
2025-07-11T19:24:10.671Z [DEBUG] - ⏱️ Check WCAG-037 completed in 218ms (success: true)
2025-07-11T19:24:10.672Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.676Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.677Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 41% (27/66)
2025-07-11T19:24:10.677Z [INFO] - ✅ Rule WCAG-037 completed: failed (0/100)
2025-07-11T19:24:10.678Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.686Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.686Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 41% (27/66)
2025-07-11T19:24:10.687Z [INFO] - 🔍 Executing rule: Images of Text (WCAG-039)
2025-07-11T19:24:10.687Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-039: Images of Text
2025-07-11T19:24:10.691Z [DEBUG] - 📁 Cache file valid: 5116e3319edb65b50e12b61ab896b2eb.json (202min remaining)
2025-07-11T19:24:10.691Z [DEBUG] - 📁 Cache loaded from file: 5116e3319edb65b50e12b61ab896b2eb.json (key: rule:WCAG-039:053b13d2:add9231...)
2025-07-11T19:24:10.692Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-039:053b13d2:add92319...
2025-07-11T19:24:10.692Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-039:053b13d2:add92319... (accessed 2 times, age: 2272s)
2025-07-11T19:24:10.693Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-039
2025-07-11T19:24:10.696Z [DEBUG] - 📁 Cache file not found: 387b4b2c9fcdaa7fe9d80826269a67f1.json (key: rule:WCAG-039:WCAG-039...)
2025-07-11T19:24:10.698Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-039:WCAG-039...
2025-07-11T19:24:10.701Z [DEBUG] - 📊 Cache stats: 35 hits, 30 misses, 50 entries
2025-07-11T19:24:10.703Z [DEBUG] - 📊 Reached max evidence items limit: 30
2025-07-11T19:24:10.706Z [DEBUG] - 💾 Cache saved to file: 387b4b2c9fcdaa7fe9d80826269a67f1.json (key: rule:WCAG-039:WCAG-039...)
2025-07-11T19:24:10.707Z [DEBUG] - 💾 Cached: rule:rule:WCAG-039:WCAG-039 (31483 bytes)
2025-07-11T19:24:10.708Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-039 - {"config":{"enableContentQualityAnalysis":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-11T19:24:10.709Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:10.710Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:24:10.752Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:24:10.892Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:24:10.929Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:10.929Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-039 - {"utilitiesUsed":["content-quality","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.30000000000000004,"executionTime":221}
2025-07-11T19:24:10.931Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-039: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:10.933Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-039"]}
2025-07-11T19:24:10.933Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-039
2025-07-11T19:24:10.934Z [DEBUG] - 🔧 Utility performance recorded for WCAG-039: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:10.935Z [DEBUG] - 🔧 Utility analysis completed for WCAG-039: - {"utilitiesUsed":2,"errors":0,"executionTime":244}
2025-07-11T19:24:10.936Z [DEBUG] - ⏱️ Check WCAG-039 completed in 249ms (success: true)
2025-07-11T19:24:10.937Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.944Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.945Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 42% (28/66)
2025-07-11T19:24:10.945Z [INFO] - ✅ Rule WCAG-039 completed: failed (0/100)
2025-07-11T19:24:10.946Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.952Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:10.955Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 42% (28/66)
2025-07-11T19:24:10.956Z [INFO] - 🔍 Executing rule: Reflow (WCAG-040)
2025-07-11T19:24:10.957Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-040: Reflow
2025-07-11T19:24:10.962Z [DEBUG] - 📁 Cache file valid: d41b0dfdebaa57efd7482b318c8099d8.json (202min remaining)
2025-07-11T19:24:10.962Z [DEBUG] - 📁 Cache loaded from file: d41b0dfdebaa57efd7482b318c8099d8.json (key: rule:WCAG-040:053b13d2:add9231...)
2025-07-11T19:24:10.963Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-040:053b13d2:add92319...
2025-07-11T19:24:10.964Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-040:053b13d2:add92319... (accessed 2 times, age: 2267s)
2025-07-11T19:24:10.965Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-040
2025-07-11T19:24:10.966Z [DEBUG] - 📁 Cache file not found: 5cb27eea612d2a28522d88f57565d42f.json (key: rule:WCAG-041:WCAG-041...)
2025-07-11T19:24:10.967Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-041:WCAG-041...
2025-07-11T19:24:10.969Z [DEBUG] - 📊 Cache stats: 36 hits, 31 misses, 52 entries
2025-07-11T19:24:10.970Z [DEBUG] - 🔍 Skipping low-quality evidence: Advanced responsive layout analysis
2025-07-11T19:24:10.972Z [DEBUG] - 📊 Reached max evidence items limit: 25
2025-07-11T19:24:10.975Z [DEBUG] - 💾 Cache saved to file: 5cb27eea612d2a28522d88f57565d42f.json (key: rule:WCAG-041:WCAG-041...)
2025-07-11T19:24:10.975Z [DEBUG] - 💾 Cached: rule:rule:WCAG-041:WCAG-041 (19112 bytes)
2025-07-11T19:24:10.976Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-040 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:24:10.977Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:10.978Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:11.153Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:11.154Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-040 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":178}
2025-07-11T19:24:11.155Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-040: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:11.157Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-040"]}
2025-07-11T19:24:11.158Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-040
2025-07-11T19:24:11.159Z [DEBUG] - 🔧 Utility performance recorded for WCAG-040: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:11.160Z [DEBUG] - 🔧 Utility analysis completed for WCAG-040: - {"utilitiesUsed":2,"errors":0,"executionTime":199}
2025-07-11T19:24:11.161Z [DEBUG] - ⏱️ Check WCAG-040 completed in 204ms (success: true)
2025-07-11T19:24:11.161Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:11.166Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:11.166Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 44% (29/66)
2025-07-11T19:24:11.167Z [INFO] - ✅ Rule WCAG-040 completed: failed (0/100)
2025-07-11T19:24:11.168Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:11.177Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:11.177Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 44% (29/66)
2025-07-11T19:24:11.178Z [INFO] - 🔍 Executing rule: Non-text Contrast (WCAG-041)
2025-07-11T19:24:11.180Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-041: Non-text Contrast
2025-07-11T19:24:11.183Z [DEBUG] - 📁 Cache file valid: 2e719d1cb90f54d9b0c937c39a0dfcac.json (202min remaining)
2025-07-11T19:24:11.183Z [DEBUG] - 📁 Cache loaded from file: 2e719d1cb90f54d9b0c937c39a0dfcac.json (key: rule:WCAG-041:053b13d2:add9231...)
2025-07-11T19:24:11.184Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-041:053b13d2:add92319...
2025-07-11T19:24:11.186Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-041:053b13d2:add92319... (accessed 2 times, age: 2260s)
2025-07-11T19:24:11.188Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-041
2025-07-11T19:24:11.189Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-041:WCAG-041... (accessed 2 times, age: 0s)
2025-07-11T19:24:11.189Z [DEBUG] - 📋 Using cached evidence for WCAG-041
2025-07-11T19:24:11.190Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-041 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T19:24:11.191Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:11.192Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:24:11.372Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:11.373Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-041 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":183}
2025-07-11T19:24:11.374Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-041: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:11.377Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-041"]}
2025-07-11T19:24:11.377Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-041
2025-07-11T19:24:11.378Z [DEBUG] - 🔧 Utility performance recorded for WCAG-041: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:11.379Z [DEBUG] - 🔧 Utility analysis completed for WCAG-041: - {"utilitiesUsed":2,"errors":0,"executionTime":196}
2025-07-11T19:24:11.380Z [DEBUG] - ⏱️ Check WCAG-041 completed in 202ms (success: true)
2025-07-11T19:24:11.381Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:11.387Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:11.387Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 45% (30/66)
2025-07-11T19:24:11.389Z [INFO] - ✅ Rule WCAG-041 completed: failed (0/100)
2025-07-11T19:24:11.392Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:11.398Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:11.398Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 45% (30/66)
2025-07-11T19:24:11.399Z [INFO] - 🔍 Executing rule: Text Spacing (WCAG-042)
2025-07-11T19:24:11.400Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-042: Text Spacing
2025-07-11T19:24:11.417Z [DEBUG] - 📁 Cache file valid: 0458bb2d44d039dec47e71e539ae6d22.json (202min remaining)
2025-07-11T19:24:11.417Z [DEBUG] - 📁 Cache loaded from file: 0458bb2d44d039dec47e71e539ae6d22.json (key: rule:WCAG-042:053b13d2:add9231...)
2025-07-11T19:24:11.418Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-042:053b13d2:add92319...
2025-07-11T19:24:11.421Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-042:053b13d2:add92319... (accessed 2 times, age: 2259s)
2025-07-11T19:24:11.421Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-042
2025-07-11T19:24:11.424Z [DEBUG] - 📁 Cache file not found: 1d505218514640abbe95b67bb9dc9504.json (key: rule:WCAG-042:WCAG-042...)
2025-07-11T19:24:11.424Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-042:WCAG-042...
2025-07-11T19:24:11.425Z [DEBUG] - 📊 Cache stats: 39 hits, 32 misses, 55 entries
2025-07-11T19:24:11.450Z [DEBUG] - 📊 Reached max evidence items limit: 30
2025-07-11T19:24:11.480Z [DEBUG] - 💾 Cache saved to file: 1d505218514640abbe95b67bb9dc9504.json (key: rule:WCAG-042:WCAG-042...)
2025-07-11T19:24:11.480Z [DEBUG] - 💾 Cached: rule:rule:WCAG-042:WCAG-042 (1141559 bytes)
2025-07-11T19:24:11.483Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-042 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:24:11.484Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:11.486Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:11.487Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:24:11.718Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:11.718Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-042 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":235}
2025-07-11T19:24:11.720Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-042: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:11.722Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-042"]}
2025-07-11T19:24:11.723Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-042
2025-07-11T19:24:11.724Z [DEBUG] - 🔧 Utility performance recorded for WCAG-042: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:11.724Z [DEBUG] - 🔧 Utility analysis completed for WCAG-042: - {"utilitiesUsed":3,"errors":0,"executionTime":321}
2025-07-11T19:24:11.725Z [DEBUG] - ⏱️ Check WCAG-042 completed in 326ms (success: true)
2025-07-11T19:24:11.726Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:11.730Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:11.730Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 47% (31/66)
2025-07-11T19:24:11.731Z [INFO] - ✅ Rule WCAG-042 completed: failed (0/100)
2025-07-11T19:24:11.732Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:11.735Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:11.736Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 47% (31/66)
2025-07-11T19:24:11.736Z [INFO] - 🔍 Executing rule: Content on Hover or Focus (WCAG-043)
2025-07-11T19:24:11.738Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-043: Content on Hover or Focus
2025-07-11T19:24:11.741Z [DEBUG] - 📁 Cache file valid: b5b31d6b54eece2df024eaf328ae7e6f.json (203min remaining)
2025-07-11T19:24:11.741Z [DEBUG] - 📁 Cache loaded from file: b5b31d6b54eece2df024eaf328ae7e6f.json (key: rule:WCAG-043:053b13d2:add9231...)
2025-07-11T19:24:11.742Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-043:053b13d2:add92319...
2025-07-11T19:24:11.743Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-043:053b13d2:add92319... (accessed 2 times, age: 2245s)
2025-07-11T19:24:11.747Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-043
2025-07-11T19:24:11.749Z [DEBUG] - 📁 Cache file not found: aaa99cde1a7b7bf2ff375426a4a8419f.json (key: rule:WCAG-043:WCAG-043...)
2025-07-11T19:24:11.749Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-043:WCAG-043...
2025-07-11T19:24:11.750Z [DEBUG] - 📊 Cache stats: 40 hits, 33 misses, 57 entries
2025-07-11T19:24:11.751Z [DEBUG] - 📊 Reached max evidence items limit: 25
2025-07-11T19:24:11.754Z [DEBUG] - 💾 Cache saved to file: aaa99cde1a7b7bf2ff375426a4a8419f.json (key: rule:WCAG-043:WCAG-043...)
2025-07-11T19:24:11.754Z [DEBUG] - 💾 Cached: rule:rule:WCAG-043:WCAG-043 (23839 bytes)
2025-07-11T19:24:11.755Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-043 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:24:11.755Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:11.757Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:11.758Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:24:11.994Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:11.994Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-043 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":239}
2025-07-11T19:24:11.996Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-043: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:11.998Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-043"]}
2025-07-11T19:24:11.998Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-043
2025-07-11T19:24:11.999Z [DEBUG] - 🔧 Utility performance recorded for WCAG-043: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:12.000Z [DEBUG] - 🔧 Utility analysis completed for WCAG-043: - {"utilitiesUsed":3,"errors":0,"executionTime":260}
2025-07-11T19:24:12.001Z [DEBUG] - ⏱️ Check WCAG-043 completed in 265ms (success: true)
2025-07-11T19:24:12.001Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.008Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.008Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 48% (32/66)
2025-07-11T19:24:12.009Z [INFO] - ✅ Rule WCAG-043 completed: failed (0/100)
2025-07-11T19:24:12.010Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.014Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.014Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 48% (32/66)
2025-07-11T19:24:12.015Z [INFO] - 🔍 Executing rule: Audio Control (WCAG-050)
2025-07-11T19:24:12.016Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-050: Audio Control
2025-07-11T19:24:12.018Z [DEBUG] - 📁 Cache file valid: 2ac855c7196348cef149270cf09fab49.json (203min remaining)
2025-07-11T19:24:12.018Z [DEBUG] - 📁 Cache loaded from file: 2ac855c7196348cef149270cf09fab49.json (key: rule:WCAG-050:053b13d2:add9231...)
2025-07-11T19:24:12.019Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-050:053b13d2:add92319...
2025-07-11T19:24:12.020Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-050:053b13d2:add92319... (accessed 2 times, age: 2245s)
2025-07-11T19:24:12.023Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-050
2025-07-11T19:24:12.024Z [DEBUG] - 📁 Cache file not found: ce16e1e1bcef738aca522d3c74b42b54.json (key: rule:WCAG-050:WCAG-050...)
2025-07-11T19:24:12.025Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-050:WCAG-050...
2025-07-11T19:24:12.026Z [DEBUG] - 📊 Cache stats: 41 hits, 34 misses, 59 entries
2025-07-11T19:24:12.028Z [DEBUG] - 💾 Cache saved to file: ce16e1e1bcef738aca522d3c74b42b54.json (key: rule:WCAG-050:WCAG-050...)
2025-07-11T19:24:12.028Z [DEBUG] - 💾 Cached: rule:rule:WCAG-050:WCAG-050 (2 bytes)
2025-07-11T19:24:12.029Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-050 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-11T19:24:12.030Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:12.032Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:24:12.217Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:12.217Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-050 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":188}
2025-07-11T19:24:12.219Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-050: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:12.221Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-050"]}
2025-07-11T19:24:12.222Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-050
2025-07-11T19:24:12.223Z [DEBUG] - 🔧 Utility performance recorded for WCAG-050: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:12.224Z [DEBUG] - 🔧 Utility analysis completed for WCAG-050: - {"utilitiesUsed":2,"errors":0,"executionTime":204}
2025-07-11T19:24:12.224Z [DEBUG] - ⏱️ Check WCAG-050 completed in 209ms (success: true)
2025-07-11T19:24:12.225Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.231Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.231Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 50% (33/66)
2025-07-11T19:24:12.232Z [INFO] - ✅ Rule WCAG-050 completed: passed (100/100)
2025-07-11T19:24:12.232Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.236Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.239Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 50% (33/66)
2025-07-11T19:24:12.240Z [INFO] - 🔍 Executing rule: Keyboard Accessible (WCAG-051)
2025-07-11T19:24:12.241Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-051: Keyboard Accessible
2025-07-11T19:24:12.243Z [DEBUG] - 📁 Cache file valid: bca5e2b8423c9d4fbbcf4b0dc032052a.json (203min remaining)
2025-07-11T19:24:12.243Z [DEBUG] - 📁 Cache loaded from file: bca5e2b8423c9d4fbbcf4b0dc032052a.json (key: rule:WCAG-051:053b13d2:add9231...)
2025-07-11T19:24:12.244Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-051:053b13d2:add92319...
2025-07-11T19:24:12.245Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-051:053b13d2:add92319... (accessed 2 times, age: 2242s)
2025-07-11T19:24:12.245Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-051
2025-07-11T19:24:12.246Z [DEBUG] - 📁 Cache file not found: 571b80c008757a3659215be6367148b2.json (key: rule:WCAG-051:WCAG-051...)
2025-07-11T19:24:12.247Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-051:WCAG-051...
2025-07-11T19:24:12.248Z [DEBUG] - 📊 Cache stats: 42 hits, 35 misses, 61 entries
2025-07-11T19:24:12.251Z [DEBUG] - 💾 Cache saved to file: 571b80c008757a3659215be6367148b2.json (key: rule:WCAG-051:WCAG-051...)
2025-07-11T19:24:12.251Z [DEBUG] - 💾 Cached: rule:rule:WCAG-051:WCAG-051 (21051 bytes)
2025-07-11T19:24:12.252Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-051 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:24:12.255Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:12.257Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:12.427Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:12.427Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-051 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":175}
2025-07-11T19:24:12.430Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-051: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:12.432Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-051"]}
2025-07-11T19:24:12.433Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-051
2025-07-11T19:24:12.434Z [DEBUG] - 🔧 Utility performance recorded for WCAG-051: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:12.434Z [DEBUG] - 🔧 Utility analysis completed for WCAG-051: - {"utilitiesUsed":2,"errors":0,"executionTime":190}
2025-07-11T19:24:12.435Z [DEBUG] - ⏱️ Check WCAG-051 completed in 196ms (success: true)
2025-07-11T19:24:12.436Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.440Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.440Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 52% (34/66)
2025-07-11T19:24:12.441Z [INFO] - ✅ Rule WCAG-051 completed: failed (0/100)
2025-07-11T19:24:12.442Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.449Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.449Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 52% (34/66)
2025-07-11T19:24:12.450Z [INFO] - 🔍 Executing rule: Character Key Shortcuts (WCAG-052)
2025-07-11T19:24:12.451Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-052: Character Key Shortcuts
2025-07-11T19:24:12.453Z [DEBUG] - 📁 Cache file valid: cb06838f5d72bd554646e20544e123ca.json (203min remaining)
2025-07-11T19:24:12.453Z [DEBUG] - 📁 Cache loaded from file: cb06838f5d72bd554646e20544e123ca.json (key: rule:WCAG-052:053b13d2:add9231...)
2025-07-11T19:24:12.454Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-052:053b13d2:add92319...
2025-07-11T19:24:12.455Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-052:053b13d2:add92319... (accessed 2 times, age: 2242s)
2025-07-11T19:24:12.456Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-052
2025-07-11T19:24:12.457Z [DEBUG] - 📁 Cache file not found: f425a13748b8f227735f568917210dbf.json (key: rule:WCAG-052:WCAG-052...)
2025-07-11T19:24:12.457Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-052:WCAG-052...
2025-07-11T19:24:12.458Z [DEBUG] - 📊 Cache stats: 43 hits, 36 misses, 63 entries
2025-07-11T19:24:12.464Z [DEBUG] - 💾 Cache saved to file: f425a13748b8f227735f568917210dbf.json (key: rule:WCAG-052:WCAG-052...)
2025-07-11T19:24:12.464Z [DEBUG] - 💾 Cached: rule:rule:WCAG-052:WCAG-052 (2 bytes)
2025-07-11T19:24:12.465Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-052 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"supplement"}}
2025-07-11T19:24:12.465Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:12.467Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:12.629Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:12.629Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-052 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":164}
2025-07-11T19:24:12.632Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-052: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:12.633Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-052"]}
2025-07-11T19:24:12.634Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-052
2025-07-11T19:24:12.635Z [DEBUG] - 🔧 Utility performance recorded for WCAG-052: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:12.635Z [DEBUG] - 🔧 Utility analysis completed for WCAG-052: - {"utilitiesUsed":2,"errors":0,"executionTime":182}
2025-07-11T19:24:12.636Z [DEBUG] - ⏱️ Check WCAG-052 completed in 186ms (success: true)
2025-07-11T19:24:12.637Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.642Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.643Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 53% (35/66)
2025-07-11T19:24:12.643Z [INFO] - ✅ Rule WCAG-052 completed: passed (75/100)
2025-07-11T19:24:12.645Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.649Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.649Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 53% (35/66)
2025-07-11T19:24:12.650Z [INFO] - 🔍 Executing rule: Pointer Gestures (WCAG-053)
2025-07-11T19:24:12.651Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-053: Pointer Gestures
2025-07-11T19:24:12.653Z [DEBUG] - 📁 Cache file valid: 1930a801e32083d4102919ec70ca2021.json (203min remaining)
2025-07-11T19:24:12.653Z [DEBUG] - 📁 Cache loaded from file: 1930a801e32083d4102919ec70ca2021.json (key: rule:WCAG-053:053b13d2:add9231...)
2025-07-11T19:24:12.654Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-053:053b13d2:add92319...
2025-07-11T19:24:12.656Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-053:053b13d2:add92319... (accessed 2 times, age: 2241s)
2025-07-11T19:24:12.657Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-053
2025-07-11T19:24:12.659Z [DEBUG] - 📁 Cache file not found: a6b70abcaea5e06681438868e2970376.json (key: rule:WCAG-049:WCAG-049...)
2025-07-11T19:24:12.660Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-049:WCAG-049...
2025-07-11T19:24:12.660Z [DEBUG] - 📊 Cache stats: 44 hits, 37 misses, 65 entries
2025-07-11T19:24:12.663Z [DEBUG] - 💾 Cache saved to file: a6b70abcaea5e06681438868e2970376.json (key: rule:WCAG-049:WCAG-049...)
2025-07-11T19:24:12.664Z [DEBUG] - 💾 Cached: rule:rule:WCAG-049:WCAG-049 (2348 bytes)
2025-07-11T19:24:12.664Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-053 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T19:24:12.665Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:12.667Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:24:12.856Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:12.857Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-053 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":193}
2025-07-11T19:24:12.858Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-053: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:12.860Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-053"]}
2025-07-11T19:24:12.861Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-053
2025-07-11T19:24:12.862Z [DEBUG] - 🔧 Utility performance recorded for WCAG-053: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:12.863Z [DEBUG] - 🔧 Utility analysis completed for WCAG-053: - {"utilitiesUsed":2,"errors":0,"executionTime":208}
2025-07-11T19:24:12.864Z [DEBUG] - ⏱️ Check WCAG-053 completed in 214ms (success: true)
2025-07-11T19:24:12.865Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.871Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.871Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 55% (36/66)
2025-07-11T19:24:12.872Z [INFO] - ✅ Rule WCAG-053 completed: passed (100/100)
2025-07-11T19:24:12.873Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.877Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:12.878Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 55% (36/66)
2025-07-11T19:24:12.878Z [INFO] - 🔍 Executing rule: Pointer Cancellation (WCAG-054)
2025-07-11T19:24:12.880Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-054: Pointer Cancellation
2025-07-11T19:24:12.882Z [DEBUG] - 📁 Cache file valid: afbb911ef718255baee738a6330a80b5.json (203min remaining)
2025-07-11T19:24:12.882Z [DEBUG] - 📁 Cache loaded from file: afbb911ef718255baee738a6330a80b5.json (key: rule:WCAG-054:053b13d2:add9231...)
2025-07-11T19:24:12.883Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-054:053b13d2:add92319...
2025-07-11T19:24:12.884Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-054:053b13d2:add92319... (accessed 2 times, age: 2240s)
2025-07-11T19:24:12.885Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-054
2025-07-11T19:24:12.886Z [DEBUG] - 📁 Cache file not found: e1e0d0058f54627e40b8105ad9a7bc7b.json (key: rule:WCAG-054:WCAG-054...)
2025-07-11T19:24:12.887Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-054:WCAG-054...
2025-07-11T19:24:12.887Z [DEBUG] - 📊 Cache stats: 45 hits, 38 misses, 67 entries
2025-07-11T19:24:12.890Z [DEBUG] - 💾 Cache saved to file: e1e0d0058f54627e40b8105ad9a7bc7b.json (key: rule:WCAG-054:WCAG-054...)
2025-07-11T19:24:12.892Z [DEBUG] - 💾 Cached: rule:rule:WCAG-054:WCAG-054 (2 bytes)
2025-07-11T19:24:12.893Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-054 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T19:24:12.894Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:12.895Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:24:13.076Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:13.077Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-054 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":184}
2025-07-11T19:24:13.078Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-054: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:13.080Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-054"]}
2025-07-11T19:24:13.081Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-054
2025-07-11T19:24:13.082Z [DEBUG] - 🔧 Utility performance recorded for WCAG-054: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:13.083Z [DEBUG] - 🔧 Utility analysis completed for WCAG-054: - {"utilitiesUsed":2,"errors":0,"executionTime":200}
2025-07-11T19:24:13.083Z [DEBUG] - ⏱️ Check WCAG-054 completed in 205ms (success: true)
2025-07-11T19:24:13.084Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:13.089Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:13.090Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 56% (37/66)
2025-07-11T19:24:13.090Z [INFO] - ✅ Rule WCAG-054 completed: passed (100/100)
2025-07-11T19:24:13.091Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:13.095Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:13.095Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 56% (37/66)
2025-07-11T19:24:13.096Z [INFO] - 🔍 Executing rule: Label in Name (WCAG-055)
2025-07-11T19:24:13.100Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-055: Label in Name
2025-07-11T19:24:13.102Z [DEBUG] - 📁 Cache file valid: 11b2442f56ae7ec967fe4c619d012c96.json (203min remaining)
2025-07-11T19:24:13.103Z [DEBUG] - 📁 Cache loaded from file: 11b2442f56ae7ec967fe4c619d012c96.json (key: rule:WCAG-055:053b13d2:add9231...)
2025-07-11T19:24:13.104Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-055:053b13d2:add92319...
2025-07-11T19:24:13.104Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-055:053b13d2:add92319... (accessed 2 times, age: 2235s)
2025-07-11T19:24:13.105Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-055
2025-07-11T19:24:13.106Z [DEBUG] - 📁 Cache file not found: 204889364bdc37cb54110d503e9b53ab.json (key: rule:WCAG-055:WCAG-055...)
2025-07-11T19:24:13.106Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-055:WCAG-055...
2025-07-11T19:24:13.107Z [DEBUG] - 📊 Cache stats: 46 hits, 39 misses, 69 entries
2025-07-11T19:24:13.110Z [DEBUG] - 💾 Cache saved to file: 204889364bdc37cb54110d503e9b53ab.json (key: rule:WCAG-055:WCAG-055...)
2025-07-11T19:24:13.110Z [DEBUG] - 💾 Cached: rule:rule:WCAG-055:WCAG-055 (870 bytes)
2025-07-11T19:24:13.111Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-055 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-11T19:24:13.111Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:24:13.115Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 9 times, age: 28s)
2025-07-11T19:24:13.116Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:13.118Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:24:13.118Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:24:13.119Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:24:13.120Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:24:13.121Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:24:13.133Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:24:13.142Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:24:13.142Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-055 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":31}
2025-07-11T19:24:13.143Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-055: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:13.144Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-055"]}
2025-07-11T19:24:13.148Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-055
2025-07-11T19:24:13.149Z [DEBUG] - 🔧 Utility performance recorded for WCAG-055: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:13.150Z [DEBUG] - 🔧 Utility analysis completed for WCAG-055: - {"utilitiesUsed":2,"errors":0,"executionTime":47}
2025-07-11T19:24:13.150Z [DEBUG] - ⏱️ Check WCAG-055 completed in 54ms (success: true)
2025-07-11T19:24:13.151Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:13.155Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:13.155Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 58% (38/66)
2025-07-11T19:24:13.156Z [INFO] - ✅ Rule WCAG-055 completed: failed (0/100)
2025-07-11T19:24:13.157Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:13.161Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:13.161Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 58% (38/66)
2025-07-11T19:24:13.162Z [INFO] - 🔍 Executing rule: Motion Actuation (WCAG-056)
2025-07-11T19:24:13.164Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-056: Motion Actuation
2025-07-11T19:24:13.166Z [DEBUG] - 📁 Cache file valid: 1095bcf65bccab483e1a79b49954c867.json (203min remaining)
2025-07-11T19:24:13.166Z [DEBUG] - 📁 Cache loaded from file: 1095bcf65bccab483e1a79b49954c867.json (key: rule:WCAG-056:053b13d2:add9231...)
2025-07-11T19:24:13.167Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-056:053b13d2:add92319...
2025-07-11T19:24:13.168Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-056:053b13d2:add92319... (accessed 2 times, age: 2235s)
2025-07-11T19:24:13.169Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-056
2025-07-11T19:24:13.170Z [DEBUG] - 📁 Cache file not found: 1a59056c19293eb2cff6e03cb6c465ba.json (key: rule:WCAG-056:WCAG-056...)
2025-07-11T19:24:13.170Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-056:WCAG-056...
2025-07-11T19:24:13.171Z [DEBUG] - 📊 Cache stats: 48 hits, 40 misses, 71 entries
2025-07-11T19:24:13.174Z [DEBUG] - 💾 Cache saved to file: 1a59056c19293eb2cff6e03cb6c465ba.json (key: rule:WCAG-056:WCAG-056...)
2025-07-11T19:24:13.174Z [DEBUG] - 💾 Cached: rule:rule:WCAG-056:WCAG-056 (848 bytes)
2025-07-11T19:24:13.175Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-056 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T19:24:13.175Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:13.177Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:24:13.354Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:13.354Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-056 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":179}
2025-07-11T19:24:13.356Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-056: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:13.358Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-056"]}
2025-07-11T19:24:13.359Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-056
2025-07-11T19:24:13.360Z [DEBUG] - 🔧 Utility performance recorded for WCAG-056: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:13.360Z [DEBUG] - 🔧 Utility analysis completed for WCAG-056: - {"utilitiesUsed":2,"errors":0,"executionTime":194}
2025-07-11T19:24:13.361Z [DEBUG] - ⏱️ Check WCAG-056 completed in 199ms (success: true)
2025-07-11T19:24:13.362Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:13.367Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:13.367Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 59% (39/66)
2025-07-11T19:24:13.368Z [INFO] - ✅ Rule WCAG-056 completed: passed (100/100)
2025-07-11T19:24:13.368Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:13.372Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:13.373Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 59% (39/66)
2025-07-11T19:24:13.373Z [INFO] - 🔍 Executing rule: Target Size Enhanced (WCAG-058)
2025-07-11T19:24:13.375Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-058: Target Size Enhanced
2025-07-11T19:24:13.375Z [DEBUG] - 📱 Starting responsive layout analysis
2025-07-11T19:24:14.205Z [WARN] - ⚠️ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-058 failed: 0.0% (threshold: 75%) - FAILED
2025-07-11T19:24:14.206Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-058 in 830ms - Status: failed (0/100)
2025-07-11T19:24:14.208Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-055:WCAG-055... (accessed 2 times, age: 1s)
2025-07-11T19:24:14.210Z [DEBUG] - 📋 Using cached evidence for WCAG-055
2025-07-11T19:24:14.211Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-058 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-11T19:24:14.211Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:24:14.214Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:24:14.431Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:24:14.431Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-058 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":220}
2025-07-11T19:24:14.433Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-058: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:14.435Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-058"]}
2025-07-11T19:24:14.436Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-058
2025-07-11T19:24:14.438Z [DEBUG] - 🔧 Utility performance recorded for WCAG-058: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:14.439Z [DEBUG] - 🔧 Utility analysis completed for WCAG-058: - {"utilitiesUsed":2,"errors":0,"executionTime":1060}
2025-07-11T19:24:14.439Z [DEBUG] - ⏱️ Check WCAG-058 completed in 1066ms (success: true)
2025-07-11T19:24:14.440Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.449Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.449Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 61% (40/66)
2025-07-11T19:24:14.450Z [INFO] - ✅ Rule WCAG-058 completed: failed (0/100)
2025-07-11T19:24:14.451Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.456Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.456Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 61% (40/66)
2025-07-11T19:24:14.457Z [INFO] - 🔍 Executing rule: Concurrent Input Mechanisms (WCAG-059)
2025-07-11T19:24:14.458Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-059: Concurrent Input Mechanisms
2025-07-11T19:24:14.461Z [DEBUG] - 📁 Cache file valid: c70dff940760a9c2695e5ffe212b3a60.json (203min remaining)
2025-07-11T19:24:14.462Z [DEBUG] - 📁 Cache loaded from file: c70dff940760a9c2695e5ffe212b3a60.json (key: rule:WCAG-059:053b13d2:add9231...)
2025-07-11T19:24:14.464Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-059:053b13d2:add92319...
2025-07-11T19:24:14.465Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-059:053b13d2:add92319... (accessed 2 times, age: 2233s)
2025-07-11T19:24:14.465Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-059
2025-07-11T19:24:14.467Z [DEBUG] - 📁 Cache file not found: e3fa72d52bf20b8a10376fd4dc8f9a2a.json (key: rule:WCAG-059:WCAG-059...)
2025-07-11T19:24:14.467Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-059:WCAG-059...
2025-07-11T19:24:14.468Z [DEBUG] - 📊 Cache stats: 50 hits, 41 misses, 73 entries
2025-07-11T19:24:14.469Z [DEBUG] - 📊 Reached max evidence items limit: 25
2025-07-11T19:24:14.472Z [DEBUG] - 💾 Cache saved to file: e3fa72d52bf20b8a10376fd4dc8f9a2a.json (key: rule:WCAG-059:WCAG-059...)
2025-07-11T19:24:14.473Z [DEBUG] - 💾 Cached: rule:rule:WCAG-059:WCAG-059 (20601 bytes)
2025-07-11T19:24:14.476Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-059 - {"config":{"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-11T19:24:14.477Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:24:14.480Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:24:14.523Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-059 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.45,"executionTime":47}
2025-07-11T19:24:14.523Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-059: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:14.525Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-059"]}
2025-07-11T19:24:14.527Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-059
2025-07-11T19:24:14.528Z [DEBUG] - 🔧 Utility performance recorded for WCAG-059: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:14.529Z [DEBUG] - 🔧 Utility analysis completed for WCAG-059: - {"utilitiesUsed":2,"errors":0,"executionTime":66}
2025-07-11T19:24:14.530Z [DEBUG] - ⏱️ Check WCAG-059 completed in 73ms (success: true)
2025-07-11T19:24:14.531Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.537Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.537Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 62% (41/66)
2025-07-11T19:24:14.538Z [INFO] - ✅ Rule WCAG-059 completed: failed (0/100)
2025-07-11T19:24:14.539Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.544Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.544Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 62% (41/66)
2025-07-11T19:24:14.545Z [INFO] - 🔍 Executing rule: Unusual Words (WCAG-060)
2025-07-11T19:24:14.546Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-060: Unusual Words
2025-07-11T19:24:14.549Z [DEBUG] - 📁 Cache file valid: 9c18e1991a7fc5e3c1f14571d0436d5f.json (203min remaining)
2025-07-11T19:24:14.552Z [DEBUG] - 📁 Cache loaded from file: 9c18e1991a7fc5e3c1f14571d0436d5f.json (key: rule:WCAG-060:053b13d2:add9231...)
2025-07-11T19:24:14.553Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-060:053b13d2:add92319...
2025-07-11T19:24:14.554Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-060:053b13d2:add92319... (accessed 2 times, age: 2233s)
2025-07-11T19:24:14.554Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-060
2025-07-11T19:24:14.556Z [DEBUG] - 📁 Cache file not found: bba0cab070e164c9013ae34060f30b10.json (key: rule:WCAG-060:WCAG-060...)
2025-07-11T19:24:14.557Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-060:WCAG-060...
2025-07-11T19:24:14.557Z [DEBUG] - 📊 Cache stats: 51 hits, 42 misses, 75 entries
2025-07-11T19:24:14.561Z [DEBUG] - 💾 Cache saved to file: bba0cab070e164c9013ae34060f30b10.json (key: rule:WCAG-060:WCAG-060...)
2025-07-11T19:24:14.561Z [DEBUG] - 💾 Cached: rule:rule:WCAG-060:WCAG-060 (9150 bytes)
2025-07-11T19:24:14.562Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-060 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-11T19:24:14.563Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:24:14.567Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 10 times, age: 30s)
2025-07-11T19:24:14.568Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:24:14.569Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:24:14.569Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:24:14.571Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:24:14.572Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:24:14.573Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:24:14.604Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:24:14.624Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:24:14.627Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-060 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":65}
2025-07-11T19:24:14.628Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-060: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:14.629Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-060"]}
2025-07-11T19:24:14.630Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-060
2025-07-11T19:24:14.631Z [DEBUG] - 🔧 Utility performance recorded for WCAG-060: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:14.631Z [DEBUG] - 🔧 Utility analysis completed for WCAG-060: - {"utilitiesUsed":2,"errors":0,"executionTime":82}
2025-07-11T19:24:14.632Z [DEBUG] - ⏱️ Check WCAG-060 completed in 87ms (success: true)
2025-07-11T19:24:14.633Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.638Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.638Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 64% (42/66)
2025-07-11T19:24:14.639Z [INFO] - ✅ Rule WCAG-060 completed: failed (0/100)
2025-07-11T19:24:14.644Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.649Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.649Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 64% (42/66)
2025-07-11T19:24:14.650Z [INFO] - 🔍 Executing rule: Abbreviations (WCAG-061)
2025-07-11T19:24:14.651Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-061: Abbreviations
2025-07-11T19:24:14.654Z [DEBUG] - 📁 Cache file valid: 476e9dc095154f46ef0a20ff4731d210.json (203min remaining)
2025-07-11T19:24:14.655Z [DEBUG] - 📁 Cache loaded from file: 476e9dc095154f46ef0a20ff4731d210.json (key: rule:WCAG-061:053b13d2:add9231...)
2025-07-11T19:24:14.655Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-061:053b13d2:add92319...
2025-07-11T19:24:14.657Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-061:053b13d2:add92319... (accessed 2 times, age: 2221s)
2025-07-11T19:24:14.658Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-061
2025-07-11T19:24:14.659Z [DEBUG] - 📁 Cache file not found: 2b732af45d90e7827c1382af73aac6cf.json (key: rule:WCAG-061:WCAG-061...)
2025-07-11T19:24:14.660Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-061:WCAG-061...
2025-07-11T19:24:14.661Z [DEBUG] - 📊 Cache stats: 53 hits, 43 misses, 77 entries
2025-07-11T19:24:14.664Z [DEBUG] - 💾 Cache saved to file: 2b732af45d90e7827c1382af73aac6cf.json (key: rule:WCAG-061:WCAG-061...)
2025-07-11T19:24:14.665Z [DEBUG] - 💾 Cached: rule:rule:WCAG-061:WCAG-061 (10041 bytes)
2025-07-11T19:24:14.666Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-061 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-11T19:24:14.670Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:24:14.671Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 11 times, age: 30s)
2025-07-11T19:24:14.672Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:24:14.674Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:24:14.674Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:24:14.676Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:24:14.677Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:24:14.678Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:24:14.702Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:24:14.721Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:24:14.724Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-061 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":58}
2025-07-11T19:24:14.724Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-061: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:14.725Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-061"]}
2025-07-11T19:24:14.727Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-061
2025-07-11T19:24:14.728Z [DEBUG] - 🔧 Utility performance recorded for WCAG-061: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:14.729Z [DEBUG] - 🔧 Utility analysis completed for WCAG-061: - {"utilitiesUsed":2,"errors":0,"executionTime":74}
2025-07-11T19:24:14.730Z [DEBUG] - ⏱️ Check WCAG-061 completed in 79ms (success: true)
2025-07-11T19:24:14.730Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.767Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.767Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 65% (43/66)
2025-07-11T19:24:14.768Z [INFO] - ✅ Rule WCAG-061 completed: failed (0/100)
2025-07-11T19:24:14.771Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.775Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.775Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 65% (43/66)
2025-07-11T19:24:14.776Z [INFO] - 🔍 Executing rule: Reading Level (WCAG-062)
2025-07-11T19:24:14.777Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-062: Reading Level
2025-07-11T19:24:14.780Z [DEBUG] - 📁 Cache file valid: 9e1b42b3764d26bfd315229c0df3aee1.json (203min remaining)
2025-07-11T19:24:14.780Z [DEBUG] - 📁 Cache loaded from file: 9e1b42b3764d26bfd315229c0df3aee1.json (key: rule:WCAG-062:053b13d2:add9231...)
2025-07-11T19:24:14.781Z [DEBUG] - 📁 Restored from file cache: rule:rule:WCAG-062:053b13d2:add92319...
2025-07-11T19:24:14.782Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-062:053b13d2:add92319... (accessed 2 times, age: 2221s)
2025-07-11T19:24:14.782Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-062
2025-07-11T19:24:14.784Z [DEBUG] - 📁 Cache file not found: 981b1455ff6f3c398942a4dad19cdd21.json (key: rule:WCAG-062:WCAG-062...)
2025-07-11T19:24:14.786Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-062:WCAG-062...
2025-07-11T19:24:14.787Z [DEBUG] - 📊 Cache stats: 55 hits, 44 misses, 79 entries
2025-07-11T19:24:14.791Z [DEBUG] - 💾 Cache saved to file: 981b1455ff6f3c398942a4dad19cdd21.json (key: rule:WCAG-062:WCAG-062...)
2025-07-11T19:24:14.791Z [DEBUG] - 💾 Cached: rule:rule:WCAG-062:WCAG-062 (6359 bytes)
2025-07-11T19:24:14.792Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-062 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-11T19:24:14.793Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:24:14.794Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 12 times, age: 30s)
2025-07-11T19:24:14.795Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:24:14.796Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:24:14.797Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:24:14.798Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:24:14.799Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:24:14.800Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:24:14.826Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:24:14.844Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:24:14.847Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-062 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":55}
2025-07-11T19:24:14.847Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-062: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:24:14.850Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-062"]}
2025-07-11T19:24:14.852Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-062
2025-07-11T19:24:14.854Z [DEBUG] - 🔧 Utility performance recorded for WCAG-062: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:24:14.855Z [DEBUG] - 🔧 Utility analysis completed for WCAG-062: - {"utilitiesUsed":2,"errors":0,"executionTime":71}
2025-07-11T19:24:14.856Z [DEBUG] - ⏱️ Check WCAG-062 completed in 79ms (success: true)
2025-07-11T19:24:14.856Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.862Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.862Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 67% (44/66)
2025-07-11T19:24:14.864Z [INFO] - ✅ Rule WCAG-062 completed: failed (0/100)
2025-07-11T19:24:14.866Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.872Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:24:14.872Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 67% (44/66)
2025-07-11T19:24:14.873Z [INFO] - 🔍 Executing rule: Pronunciation (WCAG-063)
2025-07-11T19:24:14.874Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-063: Pronunciation
2025-07-11T19:24:14.877Z [DEBUG] - 📁 Cache file not found: f404d78af9edf04de02d3ba3cb4bd9fc.json (key: rule:WCAG-063:053b13d2:add9231...)
2025-07-11T19:24:14.877Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-063:053b13d2:add92319...
2025-07-11T19:24:14.879Z [DEBUG] - 📊 Cache stats: 56 hits, 45 misses, 80 entries
2025-07-11T19:24:14.880Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-063, executing check
2025-07-11T19:24:14.881Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-063:053b13d2:add92319
2025-07-11T19:24:14.882Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-063: Pronunciation
2025-07-11T19:24:15.903Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:24:20.905Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:24:25.044Z [DEBUG] - 📊 Memory usage: 483MB / 709MB (growth: -425.8MB/min)
2025-07-11T19:24:25.045Z [WARN] - ⚠️ High memory usage detected: 483MB
2025-07-11T19:24:25.047Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-11T19:24:25.049Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-11T19:24:25.050Z [INFO] - ✅ Proactive cleanup completed
2025-07-11T19:24:25.907Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:24:30.907Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:24:35.866Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T19:24:35.867Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T19:24:35.869Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T19:24:35.871Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T19:24:35.914Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:24:40.919Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:24:45.927Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:24:50.935Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:24:55.041Z [WARN] - ⚠️ High memory usage detected: 464MB
2025-07-11T19:24:55.041Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-11T19:24:55.043Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-11T19:24:55.067Z [INFO] - ✅ Proactive cleanup completed
2025-07-11T19:24:55.948Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:25:00.959Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:25:05.964Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:25:10.975Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:25:15.977Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:25:20.980Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:25:25.055Z [DEBUG] - 📊 Memory usage: 464MB / 508MB (growth: 0.0MB/min)
2025-07-11T19:25:25.055Z [WARN] - ⚠️ High memory usage detected: 464MB
2025-07-11T19:25:25.057Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-11T19:25:25.060Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-11T19:25:25.061Z [INFO] - ✅ Proactive cleanup completed
2025-07-11T19:25:25.990Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:25:30.992Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:25:35.875Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T19:25:35.875Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T19:25:35.877Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T19:25:35.878Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T19:25:35.996Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:25:41.004Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:25:46.006Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:25:51.016Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:25:55.055Z [WARN] - ⚠️ High memory usage detected: 464MB
2025-07-11T19:25:55.056Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-11T19:25:55.060Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-11T19:25:55.061Z [INFO] - ✅ Proactive cleanup completed
2025-07-11T19:25:56.018Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:26:01.025Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:26:06.029Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:26:11.038Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:26:16.076Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:26:21.056Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:26:25.068Z [DEBUG] - 📊 Memory usage: 464MB / 508MB (growth: 0.0MB/min)
2025-07-11T19:26:25.068Z [WARN] - ⚠️ High memory usage detected: 464MB
2025-07-11T19:26:25.076Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-11T19:26:25.078Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-11T19:26:25.080Z [INFO] - ✅ Proactive cleanup completed
2025-07-11T19:26:26.055Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:26:31.066Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:26:36.076Z [WARN] - ⚠️ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-063 failed: 60.0% (threshold: 75%) - FAILED
2025-07-11T19:26:36.080Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-063 in 141189ms - Status: failed (0/100)
2025-07-11T19:26:36.156Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T19:26:36.157Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T19:26:36.158Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T19:26:36.159Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T19:26:36.163Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:26:36.219Z [DEBUG] - Page state validation passed
2025-07-11T19:26:36.235Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-063
2025-07-11T19:26:36.238Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-063 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement","enablePatternValidation":true,"enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T19:26:36.247Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:26:36.276Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 13 times, age: 171s)
2025-07-11T19:26:36.279Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:36.286Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:26:36.293Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:26:36.294Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:26:36.298Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:26:36.303Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:26:36.305Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:26:37.344Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:26:37.369Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:26:37.389Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:26:37.391Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-063 - {"utilitiesUsed":["content-quality","semantic-validation","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.44999999999999996,"executionTime":1156}
2025-07-11T19:26:37.508Z [DEBUG] - 💾 Cache saved to file: f404d78af9edf04de02d3ba3cb4bd9fc.json (key: rule:WCAG-063:053b13d2:add9231...)
2025-07-11T19:26:37.509Z [DEBUG] - 💾 Cached: rule:rule:WCAG-063:053b13d2:add92319 (7528 bytes)
2025-07-11T19:26:37.510Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-063
2025-07-11T19:26:37.522Z [DEBUG] - 📁 Cache file not found: 95b9fdef07bc87e8afdf81a3f003e77b.json (key: rule:WCAG-063:WCAG-063...)
2025-07-11T19:26:37.522Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-063:WCAG-063...
2025-07-11T19:26:37.523Z [DEBUG] - 📊 Cache stats: 57 hits, 46 misses, 81 entries
2025-07-11T19:26:37.532Z [DEBUG] - 💾 Cache saved to file: 95b9fdef07bc87e8afdf81a3f003e77b.json (key: rule:WCAG-063:WCAG-063...)
2025-07-11T19:26:37.533Z [DEBUG] - 💾 Cached: rule:rule:WCAG-063:WCAG-063 (10316 bytes)
2025-07-11T19:26:37.535Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-063 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-11T19:26:37.536Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:26:37.537Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 14 times, age: 172s)
2025-07-11T19:26:37.538Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:26:37.539Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:26:37.540Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:26:37.541Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:26:37.542Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:26:37.543Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:26:37.573Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:26:37.590Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:26:37.591Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-063 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":56}
2025-07-11T19:26:37.592Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-063: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:26:37.595Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-063"]}
2025-07-11T19:26:37.595Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-063
2025-07-11T19:26:37.597Z [DEBUG] - 🔧 Utility performance recorded for WCAG-063: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:26:37.599Z [DEBUG] - 🔧 Utility analysis completed for WCAG-063: - {"utilitiesUsed":2,"errors":0,"executionTime":142718}
2025-07-11T19:26:37.600Z [DEBUG] - ⏱️ Check WCAG-063 completed in 142727ms (success: true)
2025-07-11T19:26:37.601Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:37.645Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:37.645Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 68% (45/66)
2025-07-11T19:26:37.647Z [INFO] - ✅ Rule WCAG-063 completed: failed (0/100)
2025-07-11T19:26:37.650Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:37.655Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:37.655Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 68% (45/66)
2025-07-11T19:26:37.656Z [INFO] - 🔍 Executing rule: Change on Request (WCAG-064)
2025-07-11T19:26:37.664Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-064: Change on Request
2025-07-11T19:26:37.666Z [DEBUG] - 📁 Cache file not found: 93779b753d1c8adea1eb1a69ad737d9c.json (key: rule:WCAG-064:053b13d2:add9231...)
2025-07-11T19:26:37.668Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-064:053b13d2:add92319...
2025-07-11T19:26:37.669Z [DEBUG] - 📊 Cache stats: 58 hits, 47 misses, 82 entries
2025-07-11T19:26:37.671Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-064, executing check
2025-07-11T19:26:37.672Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-064:053b13d2:add92319
2025-07-11T19:26:37.673Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-064: Change on Request
2025-07-11T19:26:37.878Z [WARN] - ⚠️ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-064 failed: 50.0% (threshold: 75%) - FAILED
2025-07-11T19:26:37.879Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-064 in 205ms - Status: failed (0/100)
2025-07-11T19:26:37.887Z [DEBUG] - Page state validation passed
2025-07-11T19:26:37.888Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-064
2025-07-11T19:26:37.889Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-064 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"supplement","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":2000}}
2025-07-11T19:26:37.890Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:37.892Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:26:38.117Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:26:38.117Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-064 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":228}
2025-07-11T19:26:38.123Z [DEBUG] - 💾 Cache saved to file: 93779b753d1c8adea1eb1a69ad737d9c.json (key: rule:WCAG-064:053b13d2:add9231...)
2025-07-11T19:26:38.124Z [DEBUG] - 💾 Cached: rule:rule:WCAG-064:053b13d2:add92319 (2814 bytes)
2025-07-11T19:26:38.124Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-064
2025-07-11T19:26:38.128Z [DEBUG] - 📁 Cache file not found: 3f7adb1b67ca5c8b2c9a4745cfc9442c.json (key: rule:WCAG-064:WCAG-064...)
2025-07-11T19:26:38.128Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-064:WCAG-064...
2025-07-11T19:26:38.129Z [DEBUG] - 📊 Cache stats: 58 hits, 48 misses, 83 entries
2025-07-11T19:26:38.131Z [DEBUG] - 💾 Cache saved to file: 3f7adb1b67ca5c8b2c9a4745cfc9442c.json (key: rule:WCAG-064:WCAG-064...)
2025-07-11T19:26:38.132Z [DEBUG] - 💾 Cached: rule:rule:WCAG-064:WCAG-064 (3832 bytes)
2025-07-11T19:26:38.132Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-064 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"supplement"}}
2025-07-11T19:26:38.133Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:38.135Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:26:38.316Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:26:38.316Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-064 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":184}
2025-07-11T19:26:38.318Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-064: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:26:38.320Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-064"]}
2025-07-11T19:26:38.321Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-064
2025-07-11T19:26:38.321Z [DEBUG] - 🔧 Utility performance recorded for WCAG-064: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:26:38.322Z [DEBUG] - 🔧 Utility analysis completed for WCAG-064: - {"utilitiesUsed":2,"errors":0,"executionTime":662}
2025-07-11T19:26:38.323Z [DEBUG] - ⏱️ Check WCAG-064 completed in 667ms (success: true)
2025-07-11T19:26:38.324Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:38.328Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:38.329Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 70% (46/66)
2025-07-11T19:26:38.329Z [INFO] - ✅ Rule WCAG-064 completed: failed (0/100)
2025-07-11T19:26:38.331Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:38.335Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:38.335Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 70% (46/66)
2025-07-11T19:26:38.336Z [INFO] - 🔍 Executing rule: Help (WCAG-065)
2025-07-11T19:26:38.339Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-065: Help
2025-07-11T19:26:38.339Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-065:053b13d2:add92319... (accessed 3 times, age: 2438s)
2025-07-11T19:26:38.340Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache hit for WCAG-065
2025-07-11T19:26:38.344Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-065:WCAG-065... (accessed 2 times, age: 149s)
2025-07-11T19:26:38.344Z [DEBUG] - 📋 Using cached evidence for WCAG-065
2025-07-11T19:26:38.345Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-065 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-11T19:26:38.346Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:26:38.347Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:26:38.349Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:26:38.351Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:26:38.351Z [DEBUG] - 📊 Cache stats: 60 hits, 49 misses, 9 entries
2025-07-11T19:26:38.390Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:26:41.169Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:26:43.396Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:26:43.397Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:26:43.401Z [WARN] - ⚠️ Utility cms-detection failed on attempt 1, retrying... - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded","attempt":1,"maxRetries":2}
2025-07-11T19:26:44.413Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:26:44.414Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:26:44.417Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:26:44.418Z [DEBUG] - 📊 Cache stats: 60 hits, 50 misses, 9 entries
2025-07-11T19:26:46.178Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:26:48.357Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis failed for WCAG-065: - {"error":"Utility analysis timeout"}
2025-07-11T19:26:48.357Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-065: - {"utilitiesUsed":1,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:26:48.359Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-065","Utility errors detected for WCAG-065: Utility analysis error: Utility analysis timeout"]}
2025-07-11T19:26:48.361Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-065
2025-07-11T19:26:48.362Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Utility errors detected for WCAG-065: Utility analysis error: Utility analysis timeout
2025-07-11T19:26:48.368Z [DEBUG] - 🔧 Utility performance recorded for WCAG-065: - {"utilitiesUsed":1,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:26:48.369Z [DEBUG] - 🔧 Utility analysis completed for WCAG-065: - {"utilitiesUsed":1,"errors":1,"executionTime":10021}
2025-07-11T19:26:48.370Z [DEBUG] - ⏱️ Check WCAG-065 completed in 10035ms (success: true)
2025-07-11T19:26:48.371Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:48.377Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:48.378Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 71% (47/66)
2025-07-11T19:26:48.379Z [INFO] - ✅ Rule WCAG-065 completed: passed (75/100)
2025-07-11T19:26:48.380Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:48.384Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:48.384Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 71% (47/66)
2025-07-11T19:26:48.385Z [INFO] - 🔍 Executing rule: Error Prevention Enhanced (WCAG-066)
2025-07-11T19:26:48.388Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-066: Error Prevention Enhanced
2025-07-11T19:26:48.391Z [DEBUG] - 📁 Cache file not found: d48d459818905b778081b66701550a48.json (key: rule:WCAG-066:053b13d2:add9231...)
2025-07-11T19:26:48.392Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-066:053b13d2:add92319...
2025-07-11T19:26:48.393Z [DEBUG] - 📊 Cache stats: 60 hits, 51 misses, 84 entries
2025-07-11T19:26:48.394Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-066, executing check
2025-07-11T19:26:48.395Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-066:053b13d2:add92319
2025-07-11T19:26:48.396Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-066: Error Prevention Enhanced
2025-07-11T19:26:48.420Z [WARN] - ⚠️ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-066 failed: 55.0% (threshold: 75%) - FAILED
2025-07-11T19:26:48.421Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-066 in 24ms - Status: failed (0/100)
2025-07-11T19:26:48.428Z [DEBUG] - Page state validation passed
2025-07-11T19:26:48.428Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-066
2025-07-11T19:26:48.429Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-066 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T19:26:48.430Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:48.432Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:26:48.691Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:26:48.692Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-066 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":263}
2025-07-11T19:26:48.697Z [DEBUG] - 💾 Cache saved to file: d48d459818905b778081b66701550a48.json (key: rule:WCAG-066:053b13d2:add9231...)
2025-07-11T19:26:48.697Z [DEBUG] - 💾 Cached: rule:rule:WCAG-066:053b13d2:add92319 (1742 bytes)
2025-07-11T19:26:48.698Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-066
2025-07-11T19:26:48.699Z [DEBUG] - 📁 Cache file not found: 4ee2f228158eeecec3420712b90f757c.json (key: rule:WCAG-066:WCAG-066...)
2025-07-11T19:26:48.699Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-066:WCAG-066...
2025-07-11T19:26:48.700Z [DEBUG] - 📊 Cache stats: 60 hits, 52 misses, 85 entries
2025-07-11T19:26:48.704Z [DEBUG] - 💾 Cache saved to file: 4ee2f228158eeecec3420712b90f757c.json (key: rule:WCAG-066:WCAG-066...)
2025-07-11T19:26:48.705Z [DEBUG] - 💾 Cached: rule:rule:WCAG-066:WCAG-066 (1684 bytes)
2025-07-11T19:26:48.705Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-066 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T19:26:48.706Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:48.707Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:26:48.897Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:26:48.897Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-066 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":192}
2025-07-11T19:26:48.899Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-066: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:26:48.900Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-066"]}
2025-07-11T19:26:48.901Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-066
2025-07-11T19:26:48.902Z [DEBUG] - 🔧 Utility performance recorded for WCAG-066: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:26:48.903Z [DEBUG] - 🔧 Utility analysis completed for WCAG-066: - {"utilitiesUsed":2,"errors":0,"executionTime":514}
2025-07-11T19:26:48.905Z [DEBUG] - ⏱️ Check WCAG-066 completed in 519ms (success: true)
2025-07-11T19:26:48.906Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:48.910Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:48.910Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 73% (48/66)
2025-07-11T19:26:48.912Z [INFO] - ✅ Rule WCAG-066 completed: failed (0/100)
2025-07-11T19:26:48.913Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:48.922Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:48.922Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 73% (48/66)
2025-07-11T19:26:48.923Z [INFO] - 🔍 Executing rule: Status Messages (WCAG-057)
2025-07-11T19:26:48.925Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-057: Status Messages
2025-07-11T19:26:48.927Z [DEBUG] - 📁 Cache file not found: dd8868bbbf560a4668e27b99ad1fa6c3.json (key: rule:WCAG-057:053b13d2:add9231...)
2025-07-11T19:26:48.928Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-057:053b13d2:add92319...
2025-07-11T19:26:48.929Z [DEBUG] - 📊 Cache stats: 60 hits, 53 misses, 86 entries
2025-07-11T19:26:48.930Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-057, executing check
2025-07-11T19:26:48.933Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-057:053b13d2:add92319
2025-07-11T19:26:48.935Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-057: Status Messages
2025-07-11T19:26:48.953Z [INFO] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-057 passed: 100.0% (threshold: 75%) - PASSED
2025-07-11T19:26:48.953Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-057 in 17ms - Status: passed (100/100)
2025-07-11T19:26:48.958Z [DEBUG] - Page state validation passed
2025-07-11T19:26:48.958Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-057
2025-07-11T19:26:48.959Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-057 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"supplement","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T19:26:48.960Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:26:48.961Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 15 times, age: 184s)
2025-07-11T19:26:48.964Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:48.965Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:26:48.967Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:26:48.968Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:26:48.969Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:26:48.970Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:26:48.971Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:26:48.988Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:26:49.011Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:26:49.011Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-057 - {"utilitiesUsed":["semantic-validation","framework-optimization","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.6,"executionTime":52}
2025-07-11T19:26:49.016Z [DEBUG] - 💾 Cache saved to file: dd8868bbbf560a4668e27b99ad1fa6c3.json (key: rule:WCAG-057:053b13d2:add9231...)
2025-07-11T19:26:49.016Z [DEBUG] - 💾 Cached: rule:rule:WCAG-057:053b13d2:add92319 (834 bytes)
2025-07-11T19:26:49.017Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-057
2025-07-11T19:26:49.020Z [DEBUG] - 📁 Cache file not found: 8a553885bf23a1ce4f8e3be10d1bfa8e.json (key: rule:WCAG-057:WCAG-057...)
2025-07-11T19:26:49.020Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-057:WCAG-057...
2025-07-11T19:26:49.021Z [DEBUG] - 📊 Cache stats: 61 hits, 54 misses, 87 entries
2025-07-11T19:26:49.024Z [DEBUG] - 💾 Cache saved to file: 8a553885bf23a1ce4f8e3be10d1bfa8e.json (key: rule:WCAG-057:WCAG-057...)
2025-07-11T19:26:49.026Z [DEBUG] - 💾 Cached: rule:rule:WCAG-057:WCAG-057 (787 bytes)
2025-07-11T19:26:49.026Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-057 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-11T19:26:49.028Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:26:49.029Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 16 times, age: 184s)
2025-07-11T19:26:49.030Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:49.032Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:26:49.033Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:26:49.033Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:26:49.035Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:26:49.036Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:26:49.036Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:26:49.048Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:26:49.066Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:26:49.067Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-057 - {"utilitiesUsed":["semantic-validation","framework-optimization","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.6,"executionTime":41}
2025-07-11T19:26:49.067Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-057: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:26:49.068Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-057"]}
2025-07-11T19:26:49.069Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-057
2025-07-11T19:26:49.070Z [DEBUG] - 🔧 Utility performance recorded for WCAG-057: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:26:49.070Z [DEBUG] - 🔧 Utility analysis completed for WCAG-057: - {"utilitiesUsed":3,"errors":0,"executionTime":144}
2025-07-11T19:26:49.076Z [DEBUG] - ⏱️ Check WCAG-057 completed in 153ms (success: true)
2025-07-11T19:26:49.077Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:49.081Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:49.082Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 74% (49/66)
2025-07-11T19:26:49.082Z [INFO] - ✅ Rule WCAG-057 completed: passed (100/100)
2025-07-11T19:26:49.084Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:49.087Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:49.088Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 74% (49/66)
2025-07-11T19:26:49.089Z [INFO] - 🔍 Executing rule: Skip Links (WCAG-047)
2025-07-11T19:26:49.092Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-047: Skip Links
2025-07-11T19:26:49.094Z [DEBUG] - 📁 Cache file not found: a93d1a32f96aba226124f591eae3c89c.json (key: rule:WCAG-047:053b13d2:add9231...)
2025-07-11T19:26:49.094Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-047:053b13d2:add92319...
2025-07-11T19:26:49.095Z [DEBUG] - 📊 Cache stats: 62 hits, 55 misses, 88 entries
2025-07-11T19:26:49.096Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-047, executing check
2025-07-11T19:26:49.096Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-047:053b13d2:add92319
2025-07-11T19:26:49.097Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-047: Skip Links
2025-07-11T19:26:49.105Z [DEBUG] - 📱 Starting responsive layout analysis
2025-07-11T19:26:49.426Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:26:49.427Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:26:49.429Z [WARN] - ❌ Utility cms-detection failed after 2 attempts, using fallback - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:26:50.221Z [WARN] - ⚠️ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-047 failed: 10.0% (threshold: 75%) - FAILED
2025-07-11T19:26:50.221Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-047 in 1124ms - Status: failed (0/100)
2025-07-11T19:26:50.229Z [DEBUG] - Page state validation passed
2025-07-11T19:26:50.229Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-047
2025-07-11T19:26:50.231Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-047 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement","maxExecutionTime":5000,"enableCaching":true,"enableGracefulFallback":true}}
2025-07-11T19:26:50.232Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:50.234Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:26:50.236Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:26:50.531Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:26:50.531Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-047 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":300}
2025-07-11T19:26:50.536Z [DEBUG] - 💾 Cache saved to file: a93d1a32f96aba226124f591eae3c89c.json (key: rule:WCAG-047:053b13d2:add9231...)
2025-07-11T19:26:50.536Z [DEBUG] - 💾 Cached: rule:rule:WCAG-047:053b13d2:add92319 (4193 bytes)
2025-07-11T19:26:50.537Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-047
2025-07-11T19:26:50.539Z [DEBUG] - 📁 Cache file not found: a0ccdf033034a3879539686aaa721ac8.json (key: rule:WCAG-047:WCAG-047...)
2025-07-11T19:26:50.540Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-047:WCAG-047...
2025-07-11T19:26:50.540Z [DEBUG] - 📊 Cache stats: 62 hits, 56 misses, 89 entries
2025-07-11T19:26:50.542Z [DEBUG] - 🔍 Skipping low-quality evidence: Advanced layout analysis for skip links context
2025-07-11T19:26:50.545Z [DEBUG] - 💾 Cache saved to file: a0ccdf033034a3879539686aaa721ac8.json (key: rule:WCAG-047:WCAG-047...)
2025-07-11T19:26:50.546Z [DEBUG] - 💾 Cached: rule:rule:WCAG-047:WCAG-047 (4639 bytes)
2025-07-11T19:26:50.547Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-047 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement","maxExecutionTime":5000}}
2025-07-11T19:26:50.549Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:50.550Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:26:50.551Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:26:50.779Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:26:50.779Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-047 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":232}
2025-07-11T19:26:50.782Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-047: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:26:50.783Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-047"]}
2025-07-11T19:26:50.785Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-047
2025-07-11T19:26:50.786Z [DEBUG] - 🔧 Utility performance recorded for WCAG-047: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:26:50.786Z [DEBUG] - 🔧 Utility analysis completed for WCAG-047: - {"utilitiesUsed":3,"errors":0,"executionTime":1693}
2025-07-11T19:26:50.787Z [DEBUG] - ⏱️ Check WCAG-047 completed in 1698ms (success: true)
2025-07-11T19:26:50.788Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:50.793Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:50.795Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 76% (50/66)
2025-07-11T19:26:50.796Z [INFO] - ✅ Rule WCAG-047 completed: failed (0/100)
2025-07-11T19:26:50.797Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:50.805Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:50.805Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 76% (50/66)
2025-07-11T19:26:50.806Z [INFO] - 🔍 Executing rule: Enhanced Focus Management (WCAG-048)
2025-07-11T19:26:50.813Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-048: Enhanced Focus Management
2025-07-11T19:26:50.815Z [DEBUG] - 📁 Cache file not found: 2b895f8cfe8b7b157d882ade813b5d6c.json (key: rule:WCAG-048:053b13d2:add9231...)
2025-07-11T19:26:50.815Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-048:053b13d2:add92319...
2025-07-11T19:26:50.816Z [DEBUG] - 📊 Cache stats: 62 hits, 57 misses, 90 entries
2025-07-11T19:26:50.817Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-048, executing check
2025-07-11T19:26:50.820Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-048:053b13d2:add92319
2025-07-11T19:26:50.821Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-048: Enhanced Focus Management
2025-07-11T19:26:50.859Z [WARN] - ⚠️ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-048 failed: 0.0% (threshold: 75%) - FAILED
2025-07-11T19:26:50.860Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-048 in 38ms - Status: failed (0/100)
2025-07-11T19:26:50.871Z [DEBUG] - Page state validation passed
2025-07-11T19:26:50.871Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-048
2025-07-11T19:26:50.872Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-048 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance","maxExecutionTime":5000,"enableCaching":true,"enableGracefulFallback":true}}
2025-07-11T19:26:50.873Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:50.874Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:26:50.875Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:26:51.067Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:26:51.067Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-048 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":195}
2025-07-11T19:26:51.073Z [DEBUG] - 💾 Cache saved to file: 2b895f8cfe8b7b157d882ade813b5d6c.json (key: rule:WCAG-048:053b13d2:add9231...)
2025-07-11T19:26:51.074Z [DEBUG] - 💾 Cached: rule:rule:WCAG-048:053b13d2:add92319 (16800 bytes)
2025-07-11T19:26:51.075Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-048
2025-07-11T19:26:51.076Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-056:WCAG-056... (accessed 2 times, age: 158s)
2025-07-11T19:26:51.076Z [DEBUG] - 📋 Using cached evidence for WCAG-056
2025-07-11T19:26:51.077Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-048 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement","maxExecutionTime":5000}}
2025-07-11T19:26:51.078Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:51.079Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:26:51.080Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:26:51.178Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:26:51.282Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:26:51.282Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-048 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":205}
2025-07-11T19:26:51.284Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-048: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:26:51.286Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-048"]}
2025-07-11T19:26:51.287Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-048
2025-07-11T19:26:51.288Z [DEBUG] - 🔧 Utility performance recorded for WCAG-048: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:26:51.288Z [DEBUG] - 🔧 Utility analysis completed for WCAG-048: - {"utilitiesUsed":3,"errors":0,"executionTime":478}
2025-07-11T19:26:51.289Z [DEBUG] - ⏱️ Check WCAG-048 completed in 483ms (success: true)
2025-07-11T19:26:51.290Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:51.295Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:51.295Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 77% (51/66)
2025-07-11T19:26:51.296Z [INFO] - ✅ Rule WCAG-048 completed: failed (0/100)
2025-07-11T19:26:51.297Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:51.301Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:51.302Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 77% (51/66)
2025-07-11T19:26:51.303Z [INFO] - 🔍 Executing rule: Link Context (WCAG-049)
2025-07-11T19:26:51.307Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-049: Link Context
2025-07-11T19:26:51.308Z [DEBUG] - 📁 Cache file not found: 89cc9afbbdc90596fa5d04573e0e1041.json (key: rule:WCAG-049:053b13d2:add9231...)
2025-07-11T19:26:51.309Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-049:053b13d2:add92319...
2025-07-11T19:26:51.309Z [DEBUG] - 📊 Cache stats: 63 hits, 58 misses, 91 entries
2025-07-11T19:26:51.310Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-049, executing check
2025-07-11T19:26:51.311Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-049:053b13d2:add92319
2025-07-11T19:26:51.311Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-049: Link Context
2025-07-11T19:26:51.355Z [WARN] - ⚠️ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-049 failed: 0.0% (threshold: 75%) - FAILED
2025-07-11T19:26:51.356Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-049 in 44ms - Status: failed (0/100)
2025-07-11T19:26:51.362Z [DEBUG] - Page state validation passed
2025-07-11T19:26:51.362Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-049
2025-07-11T19:26:51.363Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-049 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement","maxExecutionTime":5000,"enableCaching":true,"enableGracefulFallback":true}}
2025-07-11T19:26:51.364Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:51.365Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:26:51.366Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:26:51.577Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:26:51.578Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-049 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":215}
2025-07-11T19:26:51.585Z [DEBUG] - 💾 Cache saved to file: 89cc9afbbdc90596fa5d04573e0e1041.json (key: rule:WCAG-049:053b13d2:add9231...)
2025-07-11T19:26:51.585Z [DEBUG] - 💾 Cached: rule:rule:WCAG-049:053b13d2:add92319 (41016 bytes)
2025-07-11T19:26:51.586Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-049
2025-07-11T19:26:51.587Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-049:WCAG-049... (accessed 2 times, age: 159s)
2025-07-11T19:26:51.588Z [DEBUG] - 📋 Using cached evidence for WCAG-049
2025-07-11T19:26:51.589Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-049 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement","maxExecutionTime":5000}}
2025-07-11T19:26:51.589Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:51.591Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:26:51.593Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:26:51.815Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:26:51.815Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-049 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":226}
2025-07-11T19:26:51.817Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-049: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:26:51.819Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-049"]}
2025-07-11T19:26:51.820Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-049
2025-07-11T19:26:51.821Z [DEBUG] - 🔧 Utility performance recorded for WCAG-049: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:26:51.822Z [DEBUG] - 🔧 Utility analysis completed for WCAG-049: - {"utilitiesUsed":3,"errors":0,"executionTime":514}
2025-07-11T19:26:51.823Z [DEBUG] - ⏱️ Check WCAG-049 completed in 520ms (success: true)
2025-07-11T19:26:51.824Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:51.833Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:51.833Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 79% (52/66)
2025-07-11T19:26:51.834Z [INFO] - ✅ Rule WCAG-049 completed: failed (0/100)
2025-07-11T19:26:51.836Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:51.841Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:51.841Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 79% (52/66)
2025-07-11T19:26:51.842Z [INFO] - 🔍 Executing rule: Language of Page (WCAG-024)
2025-07-11T19:26:51.845Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-024: Language of Page
2025-07-11T19:26:51.849Z [DEBUG] - 📁 Cache file not found: b888a079fad4041ad267fd6c5b651bf4.json (key: rule:WCAG-024:053b13d2:add9231...)
2025-07-11T19:26:51.850Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-024:053b13d2:add92319...
2025-07-11T19:26:51.851Z [DEBUG] - 📊 Cache stats: 64 hits, 59 misses, 92 entries
2025-07-11T19:26:51.852Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-024, executing check
2025-07-11T19:26:51.852Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-024:053b13d2:add92319
2025-07-11T19:26:51.853Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-024: Language of Page
2025-07-11T19:26:51.889Z [INFO] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-024 passed: 100.0% (threshold: 75%) - PASSED
2025-07-11T19:26:51.889Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-024 in 36ms - Status: passed (100/100)
2025-07-11T19:26:51.896Z [DEBUG] - Page state validation passed
2025-07-11T19:26:51.896Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-024
2025-07-11T19:26:51.897Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-024 - {"config":{"enableContentQualityAnalysis":true,"enablePatternValidation":true,"integrationStrategy":"supplement","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":1000}}
2025-07-11T19:26:51.898Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:51.900Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:26:51.947Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:26:52.128Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:26:52.129Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-024 - {"utilitiesUsed":["content-quality","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.30000000000000004,"executionTime":232}
2025-07-11T19:26:52.134Z [DEBUG] - 💾 Cache saved to file: b888a079fad4041ad267fd6c5b651bf4.json (key: rule:WCAG-024:053b13d2:add9231...)
2025-07-11T19:26:52.134Z [DEBUG] - 💾 Cached: rule:rule:WCAG-024:053b13d2:add92319 (2732 bytes)
2025-07-11T19:26:52.135Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-024
2025-07-11T19:26:52.137Z [DEBUG] - 📁 Cache file not found: bb0b9f38522895926d1bf1f27c888852.json (key: rule:WCAG-024:WCAG-024...)
2025-07-11T19:26:52.138Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-024:WCAG-024...
2025-07-11T19:26:52.139Z [DEBUG] - 📊 Cache stats: 64 hits, 60 misses, 93 entries
2025-07-11T19:26:52.139Z [DEBUG] - 🔍 Skipping low-quality evidence: Advanced language detection: Language successfully detected
2025-07-11T19:26:52.140Z [DEBUG] - 🔍 Skipping low-quality evidence: Language detection supporting evidence
2025-07-11T19:26:52.142Z [DEBUG] - 🔍 Skipping low-quality evidence: Lang attribute validation: Valid language declaration
2025-07-11T19:26:52.142Z [DEBUG] - 🔍 Skipping low-quality evidence: Multilingual content analysis: Proper language handling
2025-07-11T19:26:52.143Z [DEBUG] - 🔍 Skipping low-quality evidence: Language consistency testing: Consistent language declarations
2025-07-11T19:26:52.147Z [DEBUG] - 💾 Cache saved to file: bb0b9f38522895926d1bf1f27c888852.json (key: rule:WCAG-024:WCAG-024...)
2025-07-11T19:26:52.147Z [DEBUG] - 💾 Cached: rule:rule:WCAG-024:WCAG-024 (2 bytes)
2025-07-11T19:26:52.148Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-024 - {"config":{"enableContentQualityAnalysis":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-11T19:26:52.152Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:52.153Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:26:52.198Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T19:26:52.377Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:26:52.377Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-024 - {"utilitiesUsed":["content-quality","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.30000000000000004,"executionTime":229}
2025-07-11T19:26:52.379Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-024: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:26:52.381Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-024"]}
2025-07-11T19:26:52.382Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-024
2025-07-11T19:26:52.383Z [DEBUG] - 🔧 Utility performance recorded for WCAG-024: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:26:52.383Z [DEBUG] - 🔧 Utility analysis completed for WCAG-024: - {"utilitiesUsed":2,"errors":0,"executionTime":537}
2025-07-11T19:26:52.384Z [DEBUG] - ⏱️ Check WCAG-024 completed in 542ms (success: true)
2025-07-11T19:26:52.385Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:52.395Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:52.395Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 80% (53/66)
2025-07-11T19:26:52.396Z [INFO] - ✅ Rule WCAG-024 completed: passed (100/100)
2025-07-11T19:26:52.400Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:52.410Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:52.410Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 80% (53/66)
2025-07-11T19:26:52.411Z [INFO] - 🔍 Executing rule: Page Content Landmarks (WCAG-025)
2025-07-11T19:26:52.415Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-025: Landmarks
2025-07-11T19:26:52.417Z [DEBUG] - 📁 Cache file not found: e91a062e11db71715b06588166ff4e69.json (key: rule:WCAG-025:053b13d2:add9231...)
2025-07-11T19:26:52.418Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-025:053b13d2:add92319...
2025-07-11T19:26:52.419Z [DEBUG] - 📊 Cache stats: 64 hits, 61 misses, 94 entries
2025-07-11T19:26:52.419Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-025, executing check
2025-07-11T19:26:52.420Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-025:053b13d2:add92319
2025-07-11T19:26:52.421Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-025: Landmarks
2025-07-11T19:26:52.428Z [DEBUG] - 📱 Starting responsive layout analysis
2025-07-11T19:26:53.287Z [INFO] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-025 passed: 75.0% (threshold: 75%) - PASSED
2025-07-11T19:26:53.288Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-025 in 866ms - Status: passed (75/100)
2025-07-11T19:26:53.295Z [DEBUG] - Page state validation passed
2025-07-11T19:26:53.295Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-025
2025-07-11T19:26:53.297Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-025 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"supplement","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T19:26:53.298Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:26:53.299Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 17 times, age: 188s)
2025-07-11T19:26:53.301Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:53.303Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:26:53.305Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:26:53.305Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:26:53.307Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:26:53.308Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:26:53.309Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:26:53.328Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:26:53.346Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:26:53.346Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-025 - {"utilitiesUsed":["semantic-validation","framework-optimization","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.6,"executionTime":50}
2025-07-11T19:26:53.351Z [DEBUG] - 💾 Cache saved to file: e91a062e11db71715b06588166ff4e69.json (key: rule:WCAG-025:053b13d2:add9231...)
2025-07-11T19:26:53.351Z [DEBUG] - 💾 Cached: rule:rule:WCAG-025:053b13d2:add92319 (2001 bytes)
2025-07-11T19:26:53.352Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-025
2025-07-11T19:26:53.355Z [DEBUG] - 📁 Cache file not found: d2f8c4aa4a82ac0e4902227b221628a7.json (key: rule:WCAG-025:WCAG-025...)
2025-07-11T19:26:53.355Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-025:WCAG-025...
2025-07-11T19:26:53.356Z [DEBUG] - 📊 Cache stats: 65 hits, 62 misses, 95 entries
2025-07-11T19:26:53.357Z [DEBUG] - 🔍 Skipping low-quality evidence: Advanced layout analysis for landmark structure validation
2025-07-11T19:26:53.363Z [DEBUG] - 💾 Cache saved to file: d2f8c4aa4a82ac0e4902227b221628a7.json (key: rule:WCAG-025:WCAG-025...)
2025-07-11T19:26:53.363Z [DEBUG] - 💾 Cached: rule:rule:WCAG-025:WCAG-025 (862 bytes)
2025-07-11T19:26:53.367Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-025 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:26:53.367Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:26:53.369Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 18 times, age: 188s)
2025-07-11T19:26:53.370Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:53.371Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:26:53.373Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:26:53.374Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:26:53.375Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:26:53.376Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:26:53.376Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:26:53.386Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:26:53.406Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:26:53.407Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-025 - {"utilitiesUsed":["semantic-validation","framework-optimization","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.6,"executionTime":40}
2025-07-11T19:26:53.409Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-025: - {"utilitiesUsed":3,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:26:53.412Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-025"]}
2025-07-11T19:26:53.414Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-025
2025-07-11T19:26:53.415Z [DEBUG] - 🔧 Utility performance recorded for WCAG-025: - {"utilitiesUsed":3,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:26:53.416Z [DEBUG] - 🔧 Utility analysis completed for WCAG-025: - {"utilitiesUsed":3,"errors":0,"executionTime":998}
2025-07-11T19:26:53.417Z [DEBUG] - ⏱️ Check WCAG-025 completed in 1006ms (success: true)
2025-07-11T19:26:53.418Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:53.422Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:53.422Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 82% (54/66)
2025-07-11T19:26:53.423Z [INFO] - ✅ Rule WCAG-025 completed: passed (75/100)
2025-07-11T19:26:53.424Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:53.429Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:53.430Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 82% (54/66)
2025-07-11T19:26:53.431Z [INFO] - 🔍 Executing rule: Link Purpose (In Context) (WCAG-026)
2025-07-11T19:26:53.433Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-026: Link Purpose
2025-07-11T19:26:53.434Z [DEBUG] - 📁 Cache file not found: 3c311ee3a4f34f0b12ba63c5b04b8b82.json (key: rule:WCAG-026:053b13d2:add9231...)
2025-07-11T19:26:53.434Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-026:053b13d2:add92319...
2025-07-11T19:26:53.435Z [DEBUG] - 📊 Cache stats: 66 hits, 63 misses, 96 entries
2025-07-11T19:26:53.436Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-026, executing check
2025-07-11T19:26:53.436Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-026:053b13d2:add92319
2025-07-11T19:26:53.437Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-026: Link Purpose
2025-07-11T19:26:53.456Z [INFO] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-026 passed: 89.0% (threshold: 75%) - PASSED
2025-07-11T19:26:53.457Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-026 in 19ms - Status: passed (89/100)
2025-07-11T19:26:53.463Z [DEBUG] - Page state validation passed
2025-07-11T19:26:53.463Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-026
2025-07-11T19:26:53.464Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-026 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"supplement","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T19:26:53.465Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:26:53.465Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 19 times, age: 188s)
2025-07-11T19:26:53.466Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:53.467Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:26:53.470Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:26:53.471Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:26:53.473Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:26:53.474Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:26:53.485Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:26:53.503Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:26:53.504Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-026 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":40}
2025-07-11T19:26:53.509Z [DEBUG] - 💾 Cache saved to file: 3c311ee3a4f34f0b12ba63c5b04b8b82.json (key: rule:WCAG-026:053b13d2:add9231...)
2025-07-11T19:26:53.510Z [DEBUG] - 💾 Cached: rule:rule:WCAG-026:053b13d2:add92319 (16783 bytes)
2025-07-11T19:26:53.511Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-026
2025-07-11T19:26:53.514Z [DEBUG] - 📁 Cache file not found: 4dbe555e29814991c586bb38a4ff255a.json (key: rule:WCAG-026:WCAG-026...)
2025-07-11T19:26:53.515Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-026:WCAG-026...
2025-07-11T19:26:53.515Z [DEBUG] - 📊 Cache stats: 67 hits, 64 misses, 97 entries
2025-07-11T19:26:53.519Z [DEBUG] - 💾 Cache saved to file: 4dbe555e29814991c586bb38a4ff255a.json (key: rule:WCAG-026:WCAG-026...)
2025-07-11T19:26:53.519Z [DEBUG] - 💾 Cached: rule:rule:WCAG-026:WCAG-026 (25262 bytes)
2025-07-11T19:26:53.520Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-026 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-11T19:26:53.521Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:26:53.521Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 20 times, age: 188s)
2025-07-11T19:26:53.522Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:53.526Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:26:53.527Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:26:53.528Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:26:53.529Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:26:53.530Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:26:53.538Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:26:53.549Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:26:53.549Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-026 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":29}
2025-07-11T19:26:53.550Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-026: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:26:53.551Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-026"]}
2025-07-11T19:26:53.552Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-026
2025-07-11T19:26:53.553Z [DEBUG] - 🔧 Utility performance recorded for WCAG-026: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:26:53.553Z [DEBUG] - 🔧 Utility analysis completed for WCAG-026: - {"utilitiesUsed":2,"errors":0,"executionTime":119}
2025-07-11T19:26:53.554Z [DEBUG] - ⏱️ Check WCAG-026 completed in 123ms (success: true)
2025-07-11T19:26:53.558Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:53.562Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:53.562Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 83% (55/66)
2025-07-11T19:26:53.563Z [INFO] - ✅ Rule WCAG-026 completed: passed (89/100)
2025-07-11T19:26:53.563Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:53.567Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:53.567Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 83% (55/66)
2025-07-11T19:26:53.568Z [INFO] - 🔍 Executing rule: No Keyboard Trap (WCAG-027)
2025-07-11T19:26:53.570Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-027: No Keyboard Trap
2025-07-11T19:26:53.573Z [DEBUG] - 📁 Cache file not found: 5ae7432e9b50fb546411d20d9e59e734.json (key: rule:WCAG-027:053b13d2:add9231...)
2025-07-11T19:26:53.574Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-027:053b13d2:add92319...
2025-07-11T19:26:53.574Z [DEBUG] - 📊 Cache stats: 68 hits, 65 misses, 98 entries
2025-07-11T19:26:53.575Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-027, executing check
2025-07-11T19:26:53.576Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-027:053b13d2:add92319
2025-07-11T19:26:53.576Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-027: No Keyboard Trap
2025-07-11T19:26:54.020Z [ERROR] - Page script error: - {"error":"$ is not a function","stack":"TypeError: $ is not a function\n    at <anonymous> (:2:33)"}
2025-07-11T19:26:54.060Z [WARN] - ⚠️ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-027 failed: 0.0% (threshold: 75%) - FAILED
2025-07-11T19:26:54.061Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-027 in 484ms - Status: failed (0/100)
2025-07-11T19:26:54.068Z [DEBUG] - Page state validation passed
2025-07-11T19:26:54.068Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-027
2025-07-11T19:26:54.070Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-027 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"supplement","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":3000}}
2025-07-11T19:26:54.071Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:54.073Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:26:54.257Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:26:54.257Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-027 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":187}
2025-07-11T19:26:54.263Z [DEBUG] - 💾 Cache saved to file: 5ae7432e9b50fb546411d20d9e59e734.json (key: rule:WCAG-027:053b13d2:add9231...)
2025-07-11T19:26:54.263Z [DEBUG] - 💾 Cached: rule:rule:WCAG-027:053b13d2:add92319 (4125 bytes)
2025-07-11T19:26:54.264Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-027
2025-07-11T19:26:54.265Z [DEBUG] - 📁 Cache file not found: 5a0add8893b8062eefc704f37364de9d.json (key: rule:WCAG-027:WCAG-027...)
2025-07-11T19:26:54.265Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-027:WCAG-027...
2025-07-11T19:26:54.266Z [DEBUG] - 📊 Cache stats: 68 hits, 66 misses, 99 entries
2025-07-11T19:26:54.269Z [DEBUG] - 💾 Cache saved to file: 5a0add8893b8062eefc704f37364de9d.json (key: rule:WCAG-027:WCAG-027...)
2025-07-11T19:26:54.269Z [DEBUG] - 💾 Cached: rule:rule:WCAG-027:WCAG-027 (4218 bytes)
2025-07-11T19:26:54.270Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-027 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T19:26:54.271Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:54.273Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:26:54.508Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-11T19:26:54.508Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-027 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":238}
2025-07-11T19:26:54.510Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-027: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:26:54.512Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-027"]}
2025-07-11T19:26:54.513Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-027
2025-07-11T19:26:54.514Z [DEBUG] - 🔧 Utility performance recorded for WCAG-027: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:26:54.514Z [DEBUG] - 🔧 Utility analysis completed for WCAG-027: - {"utilitiesUsed":2,"errors":0,"executionTime":942}
2025-07-11T19:26:54.515Z [DEBUG] - ⏱️ Check WCAG-027 completed in 947ms (success: true)
2025-07-11T19:26:54.516Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:54.521Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:54.521Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 85% (56/66)
2025-07-11T19:26:54.522Z [INFO] - ✅ Rule WCAG-027 completed: failed (0/100)
2025-07-11T19:26:54.524Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:54.528Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:54.529Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 85% (56/66)
2025-07-11T19:26:54.530Z [INFO] - 🔍 Executing rule: Bypass Blocks (WCAG-028)
2025-07-11T19:26:54.532Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-028: Bypass Blocks
2025-07-11T19:26:54.533Z [DEBUG] - 📁 Cache file not found: 4d7b3a4d1208223f9ebc9860384af786.json (key: rule:WCAG-028:053b13d2:add9231...)
2025-07-11T19:26:54.534Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-028:053b13d2:add92319...
2025-07-11T19:26:54.535Z [DEBUG] - 📊 Cache stats: 68 hits, 67 misses, 100 entries
2025-07-11T19:26:54.536Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-028, executing check
2025-07-11T19:26:54.536Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-028:053b13d2:add92319
2025-07-11T19:26:54.537Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-028: Bypass Blocks
2025-07-11T19:26:54.560Z [INFO] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-028 passed: 80.0% (threshold: 75%) - PASSED
2025-07-11T19:26:54.560Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-028 in 23ms - Status: passed (80/100)
2025-07-11T19:26:54.575Z [DEBUG] - Page state validation passed
2025-07-11T19:26:54.575Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-028
2025-07-11T19:26:54.576Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-028 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"supplement","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T19:26:54.577Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:26:54.578Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 21 times, age: 190s)
2025-07-11T19:26:54.579Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:54.581Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:26:54.581Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:26:54.583Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:26:54.587Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:26:54.588Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:26:54.610Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:26:54.622Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:26:54.623Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-028 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":47}
2025-07-11T19:26:54.628Z [DEBUG] - 💾 Cache saved to file: 4d7b3a4d1208223f9ebc9860384af786.json (key: rule:WCAG-028:053b13d2:add9231...)
2025-07-11T19:26:54.630Z [DEBUG] - 💾 Cached: rule:rule:WCAG-028:053b13d2:add92319 (1751 bytes)
2025-07-11T19:26:54.632Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-028
2025-07-11T19:26:54.634Z [DEBUG] - 📁 Cache file not found: 1a5010ff0fb79a06ca2f9d7ba84b09ca.json (key: rule:WCAG-028:WCAG-028...)
2025-07-11T19:26:54.634Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-028:WCAG-028...
2025-07-11T19:26:54.635Z [DEBUG] - 📊 Cache stats: 69 hits, 68 misses, 101 entries
2025-07-11T19:26:54.639Z [DEBUG] - 💾 Cache saved to file: 1a5010ff0fb79a06ca2f9d7ba84b09ca.json (key: rule:WCAG-028:WCAG-028...)
2025-07-11T19:26:54.640Z [DEBUG] - 💾 Cached: rule:rule:WCAG-028:WCAG-028 (1762 bytes)
2025-07-11T19:26:54.642Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-028 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-11T19:26:54.643Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:26:54.644Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 22 times, age: 190s)
2025-07-11T19:26:54.649Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:54.653Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:26:54.655Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:26:54.658Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:26:54.660Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:26:54.664Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:26:54.675Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:26:54.722Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:26:54.722Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-028 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":80}
2025-07-11T19:26:54.725Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-028: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:26:54.727Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-028"]}
2025-07-11T19:26:54.728Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-028
2025-07-11T19:26:54.729Z [DEBUG] - 🔧 Utility performance recorded for WCAG-028: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:26:54.730Z [DEBUG] - 🔧 Utility analysis completed for WCAG-028: - {"utilitiesUsed":2,"errors":0,"executionTime":194}
2025-07-11T19:26:54.731Z [DEBUG] - ⏱️ Check WCAG-028 completed in 201ms (success: true)
2025-07-11T19:26:54.732Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:54.749Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:54.749Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 86% (57/66)
2025-07-11T19:26:54.750Z [INFO] - ✅ Rule WCAG-028 completed: passed (80/100)
2025-07-11T19:26:54.751Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:54.759Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:26:54.760Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 86% (57/66)
2025-07-11T19:26:54.761Z [INFO] - 🔍 Executing rule: Page Titled (WCAG-029)
2025-07-11T19:26:54.763Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-029: Page Titled
2025-07-11T19:26:54.764Z [DEBUG] - 📁 Cache file not found: 63b6fb44a1438711a77f80b7d3f608a7.json (key: rule:WCAG-029:053b13d2:add9231...)
2025-07-11T19:26:54.765Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-029:053b13d2:add92319...
2025-07-11T19:26:54.766Z [DEBUG] - 📊 Cache stats: 70 hits, 69 misses, 102 entries
2025-07-11T19:26:54.767Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-029, executing check
2025-07-11T19:26:54.771Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-029:053b13d2:add92319
2025-07-11T19:26:54.772Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-029: Page Titled
2025-07-11T19:26:54.859Z [INFO] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-029 passed: 90.0% (threshold: 75%) - PASSED
2025-07-11T19:26:54.860Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-029 in 87ms - Status: passed (90/100)
2025-07-11T19:26:55.068Z [WARN] - ⚠️ High memory usage detected: 482MB
2025-07-11T19:26:55.069Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-11T19:26:55.071Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-11T19:26:55.073Z [INFO] - ✅ Proactive cleanup completed
2025-07-11T19:26:55.074Z [WARN] - 🔍 Memory leak detected: 36.0MB/min growth
2025-07-11T19:26:55.076Z [WARN] - 🔍 Memory leak investigation: - {"browsers":1,"availablePages":0,"inUsePages":1,"heapUsed":482,"heapTotal":543}
2025-07-11T19:26:55.077Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-11T19:26:55.078Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-11T19:26:55.079Z [INFO] - ✅ Proactive cleanup completed
2025-07-11T19:26:55.306Z [DEBUG] - Page state validation passed
2025-07-11T19:26:55.307Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-029
2025-07-11T19:26:55.309Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-029 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement","enablePatternValidation":true,"enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T19:26:55.311Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:26:55.313Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:26:55.314Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:26:55.315Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:26:55.316Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:26:55.316Z [DEBUG] - 📊 Cache stats: 70 hits, 70 misses, 9 entries
2025-07-11T19:26:55.417Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":19.2,"accessibilityLevel":"fair"}
2025-07-11T19:26:55.534Z [ERROR] - Page script error: - {"error":"$(...).localScroll is not a function\nHTMLDocument.<anonymous> (data:text/javascript;base64,alF1ZXJ5KGRvY3VtZW50KS5yZWFkeShmdW5jdGlvbiAoJCkgewokKCcjbmF2LW1haW4nKS5sb2NhbFNjcm9sbCh7b2Zmc2V0OiAtMTUwfSk7CiQubG9jYWxTY3JvbGwuaGFzaCh7b2Zmc2V0OiAtMTUwfSkKJCgnI2thZC1tb2JpbGUtbmF2JykubG9jYWxTY3JvbGwoe29mZnNldDogMH0pOwppZiggJCh3aW5kb3cpLndpZHRoKCkgPiA3OTAgKSB7CiQubG9jYWxTY3JvbGwuaGFzaCh7b2Zmc2V0OiAwfSkKfQp9KTs=:2:16)","stack":"TypeError: $(...).localScroll is not a function\nHTMLDocument.<anonymous> (data:text/javascript;base64,alF1ZXJ5KGRvY3VtZW50KS5yZWFkeShmdW5jdGlvbiAoJCkgewokKCcjbmF2LW1haW4nKS5sb2NhbFNjcm9sbCh7b2Zmc2V0OiAtMTUwfSk7CiQubG9jYWxTY3JvbGwuaGFzaCh7b2Zmc2V0OiAtMTUwfSkKJCgnI2thZC1tb2JpbGUtbmF2JykubG9jYWxTY3JvbGwoe29mZnNldDogMH0pOwppZiggJCh3aW5kb3cpLndpZHRoKCkgPiA3OTAgKSB7CiQubG9jYWxTY3JvbGwuaGFzaCh7b2Zmc2V0OiAwfSkKfQp9KTs=:2:16)\n    at <anonymous> (:2:16)\n    at <anonymous> (https://tigerconnect.com/:3:8495)\n    at W (https://tigerconnect.com/:3:9387)"}
2025-07-11T19:26:56.191Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:26:56.760Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":40}
2025-07-11T19:27:00.317Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis failed for WCAG-029: - {"error":"Utility analysis timeout"}
2025-07-11T19:27:00.321Z [DEBUG] - 💾 Cache saved to file: 63b6fb44a1438711a77f80b7d3f608a7.json (key: rule:WCAG-029:053b13d2:add9231...)
2025-07-11T19:27:00.322Z [DEBUG] - 💾 Cached: rule:rule:WCAG-029:053b13d2:add92319 (1573 bytes)
2025-07-11T19:27:00.323Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-029
2025-07-11T19:27:00.324Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-013:WCAG-013... (accessed 2 times, age: 184s)
2025-07-11T19:27:00.325Z [DEBUG] - 📋 Using cached evidence for WCAG-013
2025-07-11T19:27:00.326Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-029 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-11T19:27:00.327Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:27:00.329Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:27:00.330Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:00.331Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:27:00.331Z [DEBUG] - 📊 Cache stats: 71 hits, 71 misses, 9 entries
2025-07-11T19:27:00.392Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":20.2,"accessibilityLevel":"fair"}
2025-07-11T19:27:00.395Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:00.396Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:00.398Z [WARN] - ⚠️ Utility cms-detection failed on attempt 1, retrying... - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded","attempt":1,"maxRetries":2}
2025-07-11T19:27:01.205Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:27:01.400Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:27:01.401Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:01.403Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:27:01.405Z [DEBUG] - 📊 Cache stats: 71 hits, 72 misses, 9 entries
2025-07-11T19:27:05.388Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:05.388Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:05.391Z [WARN] - ⚠️ Utility cms-detection failed on attempt 1, retrying... - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded","attempt":1,"maxRetries":2}
2025-07-11T19:27:06.207Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:27:06.396Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:27:06.397Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:06.400Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:27:06.403Z [DEBUG] - 📊 Cache stats: 71 hits, 73 misses, 9 entries
2025-07-11T19:27:06.669Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:06.670Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:06.673Z [WARN] - ❌ Utility cms-detection failed after 2 attempts, using fallback - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:10.346Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis failed for WCAG-029: - {"error":"Utility analysis timeout"}
2025-07-11T19:27:10.347Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-029: - {"utilitiesUsed":1,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:27:10.347Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-029","Utility errors detected for WCAG-029: Utility analysis error: Utility analysis timeout"]}
2025-07-11T19:27:10.348Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-029
2025-07-11T19:27:10.348Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Utility errors detected for WCAG-029: Utility analysis error: Utility analysis timeout
2025-07-11T19:27:10.348Z [DEBUG] - 🔧 Utility performance recorded for WCAG-029: - {"utilitiesUsed":1,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:27:10.349Z [DEBUG] - 🔧 Utility analysis completed for WCAG-029: - {"utilitiesUsed":1,"errors":1,"executionTime":15585}
2025-07-11T19:27:10.350Z [DEBUG] - ⏱️ Check WCAG-029 completed in 15589ms (success: true)
2025-07-11T19:27:10.352Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:10.358Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:10.359Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 88% (58/66)
2025-07-11T19:27:10.360Z [INFO] - ✅ Rule WCAG-029 completed: passed (90/100)
2025-07-11T19:27:10.361Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:10.369Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:10.369Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 88% (58/66)
2025-07-11T19:27:10.370Z [INFO] - 🔍 Executing rule: Labels or Instructions (WCAG-030)
2025-07-11T19:27:10.372Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-030: Labels or Instructions
2025-07-11T19:27:10.373Z [DEBUG] - 📁 Cache file not found: 2f70c2134d3fa2e2b2712750182472a1.json (key: rule:WCAG-030:053b13d2:add9231...)
2025-07-11T19:27:10.373Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-030:053b13d2:add92319...
2025-07-11T19:27:10.374Z [DEBUG] - 📊 Cache stats: 71 hits, 74 misses, 103 entries
2025-07-11T19:27:10.375Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-030, executing check
2025-07-11T19:27:10.377Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-030:053b13d2:add92319
2025-07-11T19:27:10.378Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-030: Labels or Instructions
2025-07-11T19:27:10.384Z [DEBUG] - 📁 Cache file not found: 401c39b0f4deb0470156686e33b5e08a.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:10.385Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:form-analysis-{"ana...
2025-07-11T19:27:10.386Z [DEBUG] - 📊 Cache stats: 71 hits, 75 misses, 9 entries
2025-07-11T19:27:10.387Z [DEBUG] - 📝 Starting advanced form accessibility analysis
2025-07-11T19:27:11.220Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:27:11.422Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:11.423Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:11.423Z [WARN] - ❌ Utility cms-detection failed after 2 attempts, using fallback - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:15.414Z [ERROR] - ❌ Form analysis injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:15.415Z [ERROR] - ❌ Form analysis injection process failed: - {"error":"Form analysis injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:15.507Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Error in WCAG-030 - {"error":{"message":"Form analysis injection process failed: Form analysis injection failed: Waiting failed: 5000ms exceeded","stack":"Error: Form analysis injection process failed: Form analysis injection failed: Waiting failed: 5000ms exceeded\n    at FormAccessibilityAnalyzer.injectFormAnalysisFunctions (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\form-accessibility-analyzer.ts:715:11)\n    at async FormAccessibilityAnalyzer.analyzeFormAccessibility (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\form-accessibility-analyzer.ts:154:5)\n    at async LabelsInstructionsCheck.executeLabelsInstructionsCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\checks\\labels-instructions.ts:127:37)\n    at async EnhancedCheckTemplate.executeCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\check-template.ts:146:24)\n    at async EnhancedCheckTemplate.executeEnhancedCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\enhanced-check-template.ts:226:22)\n    at async LabelsInstructionsCheck.performCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\checks\\labels-instructions.ts:44:20)\n    at async WcagOrchestrator.executeAllChecks (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\orchestrator.ts:855:29)\n    at async WcagOrchestrator.performComprehensiveScan (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\orchestrator.ts:211:27)","name":"Error"},"ruleId":"WCAG-030","ruleName":"Labels or Instructions","executionTime":5039}
2025-07-11T19:27:15.536Z [DEBUG] - Page state validation passed
2025-07-11T19:27:15.536Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-030
2025-07-11T19:27:15.537Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-030 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"integrationStrategy":"supplement","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T19:27:15.538Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:27:15.541Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 23 times, age: 210s)
2025-07-11T19:27:15.543Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:27:15.545Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:27:15.546Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:27:15.547Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:27:15.548Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:27:15.549Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:27:15.566Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:27:15.598Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:27:15.598Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-030 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":61}
2025-07-11T19:27:15.605Z [DEBUG] - 💾 Cache saved to file: 2f70c2134d3fa2e2b2712750182472a1.json (key: rule:WCAG-030:053b13d2:add9231...)
2025-07-11T19:27:15.605Z [DEBUG] - 💾 Cached: rule:rule:WCAG-030:053b13d2:add92319 (1206 bytes)
2025-07-11T19:27:15.606Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-030
2025-07-11T19:27:15.608Z [DEBUG] - 📁 Cache file not found: 9ddedd3980ffc9b2f4a4383ec7704623.json (key: rule:WCAG-030:WCAG-030...)
2025-07-11T19:27:15.609Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-030:WCAG-030...
2025-07-11T19:27:15.610Z [DEBUG] - 📊 Cache stats: 72 hits, 76 misses, 104 entries
2025-07-11T19:27:15.614Z [DEBUG] - 💾 Cache saved to file: 9ddedd3980ffc9b2f4a4383ec7704623.json (key: rule:WCAG-030:WCAG-030...)
2025-07-11T19:27:15.615Z [DEBUG] - 💾 Cached: rule:rule:WCAG-030:WCAG-030 (895 bytes)
2025-07-11T19:27:15.616Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-030 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-11T19:27:15.618Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:27:15.619Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 24 times, age: 211s)
2025-07-11T19:27:15.620Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:27:15.622Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:27:15.622Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:27:15.624Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:27:15.625Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:27:15.626Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:27:15.644Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:27:15.672Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:27:15.673Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-030 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":57}
2025-07-11T19:27:15.674Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-030: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:27:15.674Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-030"]}
2025-07-11T19:27:15.677Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-030
2025-07-11T19:27:15.679Z [DEBUG] - 🔧 Utility performance recorded for WCAG-030: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:27:15.680Z [DEBUG] - 🔧 Utility analysis completed for WCAG-030: - {"utilitiesUsed":2,"errors":0,"executionTime":5303}
2025-07-11T19:27:15.681Z [DEBUG] - ⏱️ Check WCAG-030 completed in 5311ms (success: true)
2025-07-11T19:27:15.682Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:15.689Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:15.692Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 89% (59/66)
2025-07-11T19:27:15.693Z [INFO] - ✅ Rule WCAG-030 completed: failed (0/100)
2025-07-11T19:27:15.695Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:15.709Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:15.709Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 89% (59/66)
2025-07-11T19:27:15.710Z [INFO] - 🔍 Executing rule: Error Suggestion (WCAG-031)
2025-07-11T19:27:15.713Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-031: Error Suggestion
2025-07-11T19:27:15.714Z [DEBUG] - 📁 Cache file not found: cba2c3617daaea7dad7a060e877ec84a.json (key: rule:WCAG-031:053b13d2:add9231...)
2025-07-11T19:27:15.714Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-031:053b13d2:add92319...
2025-07-11T19:27:15.715Z [DEBUG] - 📊 Cache stats: 73 hits, 77 misses, 105 entries
2025-07-11T19:27:15.716Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-031, executing check
2025-07-11T19:27:15.718Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-031:053b13d2:add92319
2025-07-11T19:27:15.724Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-031: Error Suggestion
2025-07-11T19:27:15.727Z [DEBUG] - 📁 Cache file not found: 401c39b0f4deb0470156686e33b5e08a.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:15.727Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:form-analysis-{"ana...
2025-07-11T19:27:15.728Z [DEBUG] - 📊 Cache stats: 73 hits, 78 misses, 9 entries
2025-07-11T19:27:15.729Z [DEBUG] - 📝 Starting advanced form accessibility analysis
2025-07-11T19:27:16.225Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:27:20.760Z [ERROR] - ❌ Form analysis injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:20.761Z [ERROR] - ❌ Form analysis injection process failed: - {"error":"Form analysis injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:20.771Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Error in WCAG-031 - {"error":{"message":"Form analysis injection process failed: Form analysis injection failed: Waiting failed: 5000ms exceeded","stack":"Error: Form analysis injection process failed: Form analysis injection failed: Waiting failed: 5000ms exceeded\n    at FormAccessibilityAnalyzer.injectFormAnalysisFunctions (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\form-accessibility-analyzer.ts:715:11)\n    at async FormAccessibilityAnalyzer.analyzeFormAccessibility (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\form-accessibility-analyzer.ts:154:5)\n    at async ErrorSuggestionCheck.executeErrorSuggestionCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\checks\\error-suggestion.ts:120:37)\n    at async EnhancedCheckTemplate.executeCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\check-template.ts:146:24)\n    at async EnhancedCheckTemplate.executeEnhancedCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\enhanced-check-template.ts:226:22)\n    at async ErrorSuggestionCheck.performCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\checks\\error-suggestion.ts:41:20)\n    at async WcagOrchestrator.executeAllChecks (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\orchestrator.ts:855:29)\n    at async WcagOrchestrator.performComprehensiveScan (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\orchestrator.ts:211:27)","name":"Error"},"ruleId":"WCAG-031","ruleName":"Error Suggestion","executionTime":5037}
2025-07-11T19:27:20.782Z [DEBUG] - Page state validation passed
2025-07-11T19:27:20.782Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-031
2025-07-11T19:27:20.782Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-031 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement","enablePatternValidation":true,"enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T19:27:20.783Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:27:20.785Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:27:20.786Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:27:20.787Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:20.789Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:27:20.789Z [DEBUG] - 📊 Cache stats: 73 hits, 79 misses, 9 entries
2025-07-11T19:27:20.839Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":20.2,"accessibilityLevel":"fair"}
2025-07-11T19:27:21.225Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:27:21.264Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":40}
2025-07-11T19:27:25.071Z [DEBUG] - 📊 Memory usage: 493MB / 544MB (growth: 22.0MB/min)
2025-07-11T19:27:25.072Z [WARN] - ⚠️ High memory usage detected: 493MB
2025-07-11T19:27:25.074Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-11T19:27:25.076Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-11T19:27:25.077Z [INFO] - ✅ Proactive cleanup completed
2025-07-11T19:27:25.078Z [WARN] - 🔍 Memory leak detected: 22.0MB/min growth
2025-07-11T19:27:25.079Z [WARN] - 🔍 Memory leak investigation: - {"browsers":1,"availablePages":0,"inUsePages":1,"heapUsed":493,"heapTotal":544}
2025-07-11T19:27:25.080Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-11T19:27:25.081Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-11T19:27:25.082Z [INFO] - ✅ Proactive cleanup completed
2025-07-11T19:27:25.790Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis failed for WCAG-031: - {"error":"Utility analysis timeout"}
2025-07-11T19:27:25.794Z [DEBUG] - 💾 Cache saved to file: cba2c3617daaea7dad7a060e877ec84a.json (key: rule:WCAG-031:053b13d2:add9231...)
2025-07-11T19:27:25.795Z [DEBUG] - 💾 Cached: rule:rule:WCAG-031:053b13d2:add92319 (1196 bytes)
2025-07-11T19:27:25.796Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-031
2025-07-11T19:27:25.798Z [DEBUG] - 📁 Cache file not found: 2c3ef60da8090f3069a1823b4aad3445.json (key: rule:WCAG-031:WCAG-031...)
2025-07-11T19:27:25.798Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-031:WCAG-031...
2025-07-11T19:27:25.799Z [DEBUG] - 📊 Cache stats: 73 hits, 80 misses, 106 entries
2025-07-11T19:27:25.802Z [DEBUG] - 💾 Cache saved to file: 2c3ef60da8090f3069a1823b4aad3445.json (key: rule:WCAG-031:WCAG-031...)
2025-07-11T19:27:25.803Z [DEBUG] - 💾 Cached: rule:rule:WCAG-031:WCAG-031 (871 bytes)
2025-07-11T19:27:25.804Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-031 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-11T19:27:25.805Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:27:25.806Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:27:25.811Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:25.813Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:27:25.813Z [DEBUG] - 📊 Cache stats: 73 hits, 81 misses, 9 entries
2025-07-11T19:27:25.838Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:25.839Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:25.841Z [WARN] - ⚠️ Utility cms-detection failed on attempt 1, retrying... - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded","attempt":1,"maxRetries":2}
2025-07-11T19:27:25.866Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":20.2,"accessibilityLevel":"fair"}
2025-07-11T19:27:26.234Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:27:26.713Z [INFO] - 🔓 [92f9156a-76ad-418d-a502-3ed3af6cff0d] Development mode: Mock authentication applied
2025-07-11T19:27:26.721Z [INFO] - 📋 [92f9156a-76ad-418d-a502-3ed3af6cff0d] Fetching WCAG scans for user: 9fed30a7-64b4-4ffe-b531-6e9cf592952b
2025-07-11T19:27:26.724Z [INFO] - 📊 Fetching WCAG scans for user: 9fed30a7-64b4-4ffe-b531-6e9cf592952b - {"options":{"page":1,"limit":20,"sortBy":"scanTimestamp","sortOrder":"desc"}}
2025-07-11T19:27:26.858Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:27:26.859Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:26.861Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:27:26.864Z [DEBUG] - 📊 Cache stats: 73 hits, 82 misses, 9 entries
2025-07-11T19:27:30.862Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:30.864Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:30.870Z [WARN] - ⚠️ Utility cms-detection failed on attempt 1, retrying... - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded","attempt":1,"maxRetries":2}
2025-07-11T19:27:31.235Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:27:31.882Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:27:31.895Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:31.898Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:27:31.900Z [DEBUG] - 📊 Cache stats: 73 hits, 83 misses, 9 entries
2025-07-11T19:27:31.918Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:31.927Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:31.931Z [WARN] - ❌ Utility cms-detection failed after 2 attempts, using fallback - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:35.821Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis failed for WCAG-031: - {"error":"Utility analysis timeout"}
2025-07-11T19:27:35.822Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-031: - {"utilitiesUsed":1,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:27:35.824Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-031","Utility errors detected for WCAG-031: Utility analysis error: Utility analysis timeout"]}
2025-07-11T19:27:35.826Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-031
2025-07-11T19:27:35.827Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Utility errors detected for WCAG-031: Utility analysis error: Utility analysis timeout
2025-07-11T19:27:35.828Z [DEBUG] - 🔧 Utility performance recorded for WCAG-031: - {"utilitiesUsed":1,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:27:35.829Z [DEBUG] - 🔧 Utility analysis completed for WCAG-031: - {"utilitiesUsed":1,"errors":1,"executionTime":20112}
2025-07-11T19:27:35.830Z [DEBUG] - ⏱️ Check WCAG-031 completed in 20120ms (success: true)
2025-07-11T19:27:35.831Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:35.837Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:35.840Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 91% (60/66)
2025-07-11T19:27:35.841Z [INFO] - ✅ Rule WCAG-031 completed: failed (0/100)
2025-07-11T19:27:35.843Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:35.848Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:35.848Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 91% (60/66)
2025-07-11T19:27:35.849Z [INFO] - 🔍 Executing rule: Error Prevention (WCAG-032)
2025-07-11T19:27:35.851Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-032: Error Prevention
2025-07-11T19:27:35.852Z [DEBUG] - 📁 Cache file not found: bbbf8c6b8bbb601d8a1705847212c0e6.json (key: rule:WCAG-032:053b13d2:add9231...)
2025-07-11T19:27:35.852Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-032:053b13d2:add92319...
2025-07-11T19:27:35.854Z [DEBUG] - 📊 Cache stats: 73 hits, 84 misses, 107 entries
2025-07-11T19:27:35.857Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-032, executing check
2025-07-11T19:27:35.858Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-032:053b13d2:add92319
2025-07-11T19:27:35.860Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-032: Error Prevention
2025-07-11T19:27:35.861Z [DEBUG] - 📁 Cache file not found: 401c39b0f4deb0470156686e33b5e08a.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:35.862Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:form-analysis-{"ana...
2025-07-11T19:27:35.863Z [DEBUG] - 📊 Cache stats: 73 hits, 85 misses, 9 entries
2025-07-11T19:27:35.864Z [DEBUG] - 📝 Starting advanced form accessibility analysis
2025-07-11T19:27:36.164Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T19:27:36.165Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T19:27:36.167Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T19:27:36.169Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T19:27:36.242Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:27:37.199Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:37.199Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:37.201Z [WARN] - ❌ Utility cms-detection failed after 2 attempts, using fallback - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:40.884Z [ERROR] - ❌ Form analysis injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:40.884Z [ERROR] - ❌ Form analysis injection process failed: - {"error":"Form analysis injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:40.891Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Error in WCAG-032 - {"error":{"message":"Form analysis injection process failed: Form analysis injection failed: Waiting failed: 5000ms exceeded","stack":"Error: Form analysis injection process failed: Form analysis injection failed: Waiting failed: 5000ms exceeded\n    at FormAccessibilityAnalyzer.injectFormAnalysisFunctions (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\form-accessibility-analyzer.ts:715:11)\n    at async FormAccessibilityAnalyzer.analyzeFormAccessibility (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\form-accessibility-analyzer.ts:154:5)\n    at async ErrorPreventionCheck.executeErrorPreventionCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\checks\\error-prevention.ts:148:37)\n    at async EnhancedCheckTemplate.executeCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\check-template.ts:146:24)\n    at async EnhancedCheckTemplate.executeEnhancedCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\enhanced-check-template.ts:226:22)\n    at async ErrorPreventionCheck.performCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\checks\\error-prevention.ts:69:20)\n    at async WcagOrchestrator.executeAllChecks (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\orchestrator.ts:855:29)\n    at async WcagOrchestrator.performComprehensiveScan (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\orchestrator.ts:211:27)","name":"Error"},"ruleId":"WCAG-032","ruleName":"Error Prevention","executionTime":5027}
2025-07-11T19:27:40.897Z [DEBUG] - Page state validation passed
2025-07-11T19:27:40.897Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-032
2025-07-11T19:27:40.899Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-032 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T19:27:40.904Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:27:40.906Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:27:41.242Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:27:41.399Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":40}
2025-07-11T19:27:41.400Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-032 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.76,"accuracy":0.4,"executionTime":501}
2025-07-11T19:27:41.405Z [DEBUG] - 💾 Cache saved to file: bbbf8c6b8bbb601d8a1705847212c0e6.json (key: rule:WCAG-032:053b13d2:add9231...)
2025-07-11T19:27:41.405Z [DEBUG] - 💾 Cached: rule:rule:WCAG-032:053b13d2:add92319 (1195 bytes)
2025-07-11T19:27:41.408Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-032
2025-07-11T19:27:41.409Z [DEBUG] - 📁 Cache file not found: d01427268bff7ae74cf5232671b3f9c2.json (key: rule:WCAG-032:WCAG-032...)
2025-07-11T19:27:41.410Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-032:WCAG-032...
2025-07-11T19:27:41.411Z [DEBUG] - 📊 Cache stats: 73 hits, 86 misses, 108 entries
2025-07-11T19:27:41.417Z [DEBUG] - 💾 Cache saved to file: d01427268bff7ae74cf5232671b3f9c2.json (key: rule:WCAG-032:WCAG-032...)
2025-07-11T19:27:41.418Z [DEBUG] - 💾 Cached: rule:rule:WCAG-032:WCAG-032 (856 bytes)
2025-07-11T19:27:41.419Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-032 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T19:27:41.420Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:27:41.422Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T19:27:41.770Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":40}
2025-07-11T19:27:41.770Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-032 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.76,"accuracy":0.4,"executionTime":351}
2025-07-11T19:27:41.772Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-032: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:27:41.774Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-032"]}
2025-07-11T19:27:41.775Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-032
2025-07-11T19:27:41.775Z [DEBUG] - 🔧 Utility performance recorded for WCAG-032: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:27:41.776Z [DEBUG] - 🔧 Utility analysis completed for WCAG-032: - {"utilitiesUsed":2,"errors":0,"executionTime":5923}
2025-07-11T19:27:41.778Z [DEBUG] - ⏱️ Check WCAG-032 completed in 5929ms (success: true)
2025-07-11T19:27:41.778Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:41.783Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:41.783Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 92% (61/66)
2025-07-11T19:27:41.784Z [INFO] - ✅ Rule WCAG-032 completed: failed (0/100)
2025-07-11T19:27:41.785Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:41.789Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:41.791Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 92% (61/66)
2025-07-11T19:27:41.792Z [INFO] - 🔍 Executing rule: Audio-only and Video-only (WCAG-033)
2025-07-11T19:27:41.793Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-033: Audio-only and Video-only
2025-07-11T19:27:41.794Z [DEBUG] - 📁 Cache file not found: d6fe14a255b188319a1ea0f691f867fb.json (key: rule:WCAG-033:053b13d2:add9231...)
2025-07-11T19:27:41.794Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-033:053b13d2:add92319...
2025-07-11T19:27:41.795Z [DEBUG] - 📊 Cache stats: 73 hits, 87 misses, 109 entries
2025-07-11T19:27:41.796Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-033, executing check
2025-07-11T19:27:41.796Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-033:053b13d2:add92319
2025-07-11T19:27:41.797Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-033: Audio-only and Video-only
2025-07-11T19:27:41.799Z [DEBUG] - 🎬 Starting multimedia accessibility testing
2025-07-11T19:27:41.811Z [INFO] - ✅ Multimedia accessibility testing completed - {"totalElements":0,"accessibleElements":0,"overallScore":100,"complianceLevel":"AAA"}
2025-07-11T19:27:41.813Z [INFO] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-033 passed: 100.0% (threshold: 75%) - PASSED
2025-07-11T19:27:41.813Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-033 in 16ms - Status: passed (100/100)
2025-07-11T19:27:41.818Z [DEBUG] - Page state validation passed
2025-07-11T19:27:41.819Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-033
2025-07-11T19:27:41.820Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-033 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement","enablePatternValidation":true,"enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":3000}}
2025-07-11T19:27:41.822Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:27:41.823Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:27:41.825Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:27:41.826Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:41.827Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:27:41.827Z [DEBUG] - 📊 Cache stats: 73 hits, 88 misses, 9 entries
2025-07-11T19:27:41.880Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":20.2,"accessibilityLevel":"fair"}
2025-07-11T19:27:42.192Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":40}
2025-07-11T19:27:44.842Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis failed for WCAG-033: - {"error":"Utility analysis timeout"}
2025-07-11T19:27:44.844Z [DEBUG] - 💾 Cache saved to file: d6fe14a255b188319a1ea0f691f867fb.json (key: rule:WCAG-033:053b13d2:add9231...)
2025-07-11T19:27:44.845Z [DEBUG] - 💾 Cached: rule:rule:WCAG-033:053b13d2:add92319 (372 bytes)
2025-07-11T19:27:44.847Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-033
2025-07-11T19:27:44.848Z [DEBUG] - 📁 Cache file not found: 5ae7b8d829e766d4274f3aa7efaad223.json (key: rule:WCAG-033:WCAG-033...)
2025-07-11T19:27:44.849Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-033:WCAG-033...
2025-07-11T19:27:44.849Z [DEBUG] - 📊 Cache stats: 73 hits, 89 misses, 110 entries
2025-07-11T19:27:44.854Z [DEBUG] - 💾 Cache saved to file: 5ae7b8d829e766d4274f3aa7efaad223.json (key: rule:WCAG-033:WCAG-033...)
2025-07-11T19:27:44.854Z [DEBUG] - 💾 Cached: rule:rule:WCAG-033:WCAG-033 (2 bytes)
2025-07-11T19:27:44.855Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-033 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-11T19:27:44.856Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:27:44.858Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:27:44.862Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:44.864Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:27:44.864Z [DEBUG] - 📊 Cache stats: 73 hits, 90 misses, 9 entries
2025-07-11T19:27:44.915Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":20.2,"accessibilityLevel":"fair"}
2025-07-11T19:27:46.243Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:27:46.876Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:46.877Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:46.879Z [WARN] - ⚠️ Utility cms-detection failed on attempt 1, retrying... - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded","attempt":1,"maxRetries":2}
2025-07-11T19:27:47.885Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:27:47.886Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:47.887Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:27:47.889Z [DEBUG] - 📊 Cache stats: 73 hits, 91 misses, 9 entries
2025-07-11T19:27:49.902Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:49.902Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:49.904Z [WARN] - ⚠️ Utility cms-detection failed on attempt 1, retrying... - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded","attempt":1,"maxRetries":2}
2025-07-11T19:27:50.907Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:27:50.908Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:50.909Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:27:50.911Z [DEBUG] - 📊 Cache stats: 73 hits, 92 misses, 9 entries
2025-07-11T19:27:51.253Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:27:52.903Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:52.904Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:52.907Z [WARN] - ❌ Utility cms-detection failed after 2 attempts, using fallback - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:54.870Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis failed for WCAG-033: - {"error":"Utility analysis timeout"}
2025-07-11T19:27:54.870Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-033: - {"utilitiesUsed":1,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:27:54.873Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-033","Utility errors detected for WCAG-033: Utility analysis error: Utility analysis timeout"]}
2025-07-11T19:27:54.875Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-033
2025-07-11T19:27:54.876Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Utility errors detected for WCAG-033: Utility analysis error: Utility analysis timeout
2025-07-11T19:27:54.877Z [DEBUG] - 🔧 Utility performance recorded for WCAG-033: - {"utilitiesUsed":1,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:27:54.878Z [DEBUG] - 🔧 Utility analysis completed for WCAG-033: - {"utilitiesUsed":1,"errors":1,"executionTime":13078}
2025-07-11T19:27:54.879Z [DEBUG] - ⏱️ Check WCAG-033 completed in 13088ms (success: true)
2025-07-11T19:27:54.880Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:54.887Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:54.889Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 94% (62/66)
2025-07-11T19:27:54.890Z [INFO] - ✅ Rule WCAG-033 completed: passed (100/100)
2025-07-11T19:27:54.891Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:54.897Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:27:54.897Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 94% (62/66)
2025-07-11T19:27:54.898Z [INFO] - 🔍 Executing rule: Audio Description (WCAG-034)
2025-07-11T19:27:54.900Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-034: Audio Description
2025-07-11T19:27:54.901Z [DEBUG] - 📁 Cache file not found: cd2fb5168a783fb1744f04f726e58fd1.json (key: rule:WCAG-034:053b13d2:add9231...)
2025-07-11T19:27:54.901Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-034:053b13d2:add92319...
2025-07-11T19:27:54.906Z [DEBUG] - 📊 Cache stats: 73 hits, 93 misses, 111 entries
2025-07-11T19:27:54.907Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-034, executing check
2025-07-11T19:27:54.908Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-034:053b13d2:add92319
2025-07-11T19:27:54.910Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-034: Audio Description
2025-07-11T19:27:54.911Z [DEBUG] - 🎬 Starting multimedia accessibility testing
2025-07-11T19:27:54.924Z [INFO] - ✅ Multimedia accessibility testing completed - {"totalElements":0,"accessibleElements":0,"overallScore":100,"complianceLevel":"AAA"}
2025-07-11T19:27:54.926Z [INFO] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-034 passed: 100.0% (threshold: 75%) - PASSED
2025-07-11T19:27:54.927Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-034 in 16ms - Status: passed (100/100)
2025-07-11T19:27:54.932Z [DEBUG] - Page state validation passed
2025-07-11T19:27:54.934Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-034
2025-07-11T19:27:54.935Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-034 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement","enablePatternValidation":true,"enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":4000}}
2025-07-11T19:27:54.937Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:27:54.939Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:27:54.940Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:27:54.941Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:54.942Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:27:54.943Z [DEBUG] - 📊 Cache stats: 73 hits, 94 misses, 9 entries
2025-07-11T19:27:54.988Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":20.2,"accessibilityLevel":"fair"}
2025-07-11T19:27:55.072Z [WARN] - ⚠️ High memory usage detected: 497MB
2025-07-11T19:27:55.072Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-11T19:27:55.074Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-11T19:27:55.075Z [INFO] - ✅ Proactive cleanup completed
2025-07-11T19:27:55.077Z [WARN] - 🔍 Memory leak detected: 8.0MB/min growth
2025-07-11T19:27:55.078Z [WARN] - 🔍 Memory leak investigation: - {"browsers":1,"availablePages":0,"inUsePages":1,"heapUsed":497,"heapTotal":519}
2025-07-11T19:27:55.079Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-11T19:27:55.079Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-11T19:27:55.080Z [INFO] - ✅ Proactive cleanup completed
2025-07-11T19:27:55.326Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":40}
2025-07-11T19:27:55.923Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:55.924Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:55.925Z [WARN] - ❌ Utility cms-detection failed after 2 attempts, using fallback - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:56.262Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:27:58.954Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis failed for WCAG-034: - {"error":"Utility analysis timeout"}
2025-07-11T19:27:58.962Z [DEBUG] - 💾 Cache saved to file: cd2fb5168a783fb1744f04f726e58fd1.json (key: rule:WCAG-034:053b13d2:add9231...)
2025-07-11T19:27:58.962Z [DEBUG] - 💾 Cached: rule:rule:WCAG-034:053b13d2:add92319 (364 bytes)
2025-07-11T19:27:58.963Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-034
2025-07-11T19:27:58.964Z [DEBUG] - 📁 Cache file not found: d625765fc63973c3ce3823d15152e55e.json (key: rule:WCAG-034:WCAG-034...)
2025-07-11T19:27:58.965Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-034:WCAG-034...
2025-07-11T19:27:58.966Z [DEBUG] - 📊 Cache stats: 73 hits, 95 misses, 112 entries
2025-07-11T19:27:58.969Z [DEBUG] - 💾 Cache saved to file: d625765fc63973c3ce3823d15152e55e.json (key: rule:WCAG-034:WCAG-034...)
2025-07-11T19:27:58.970Z [DEBUG] - 💾 Cached: rule:rule:WCAG-034:WCAG-034 (2 bytes)
2025-07-11T19:27:58.972Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-034 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-11T19:27:58.974Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:27:58.975Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:27:58.977Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:27:58.978Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:27:58.978Z [DEBUG] - 📊 Cache stats: 73 hits, 96 misses, 9 entries
2025-07-11T19:27:59.026Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":20.2,"accessibilityLevel":"fair"}
2025-07-11T19:27:59.993Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:59.994Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:27:59.996Z [WARN] - ⚠️ Utility cms-detection failed on attempt 1, retrying... - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded","attempt":1,"maxRetries":2}
2025-07-11T19:28:01.009Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:28:01.010Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:28:01.011Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:28:01.012Z [DEBUG] - 📊 Cache stats: 73 hits, 97 misses, 9 entries
2025-07-11T19:28:01.274Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:28:04.021Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:28:04.022Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:28:04.024Z [WARN] - ⚠️ Utility cms-detection failed on attempt 1, retrying... - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded","attempt":1,"maxRetries":2}
2025-07-11T19:28:05.037Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-11T19:28:05.038Z [DEBUG] - 📁 Cache file not found: 4eb48f50d1ad753dd779885ae0ac22d7.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:28:05.039Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:cms-analysis...
2025-07-11T19:28:05.041Z [DEBUG] - 📊 Cache stats: 73 hits, 98 misses, 9 entries
2025-07-11T19:28:06.034Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:28:06.035Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:28:06.038Z [WARN] - ❌ Utility cms-detection failed after 2 attempts, using fallback - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:28:06.284Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:28:08.986Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis failed for WCAG-034: - {"error":"Utility analysis timeout"}
2025-07-11T19:28:08.986Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-034: - {"utilitiesUsed":1,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:28:08.988Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-034","Utility errors detected for WCAG-034: Utility analysis error: Utility analysis timeout"]}
2025-07-11T19:28:08.990Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-034
2025-07-11T19:28:08.992Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Utility errors detected for WCAG-034: Utility analysis error: Utility analysis timeout
2025-07-11T19:28:08.993Z [DEBUG] - 🔧 Utility performance recorded for WCAG-034: - {"utilitiesUsed":1,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:28:08.994Z [DEBUG] - 🔧 Utility analysis completed for WCAG-034: - {"utilitiesUsed":1,"errors":1,"executionTime":14088}
2025-07-11T19:28:08.995Z [DEBUG] - ⏱️ Check WCAG-034 completed in 14097ms (success: true)
2025-07-11T19:28:08.996Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:28:09.000Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:28:09.001Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 95% (63/66)
2025-07-11T19:28:09.004Z [INFO] - ✅ Rule WCAG-034 completed: passed (100/100)
2025-07-11T19:28:09.006Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:28:09.010Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:28:09.011Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 95% (63/66)
2025-07-11T19:28:09.011Z [INFO] - 🔍 Executing rule: Multiple Ways (WCAG-035)
2025-07-11T19:28:09.013Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-035: Multiple Ways
2025-07-11T19:28:09.014Z [DEBUG] - 📁 Cache file not found: cc7ff2a9bda9ab11c362680288fd25f0.json (key: rule:WCAG-035:053b13d2:add9231...)
2025-07-11T19:28:09.014Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-035:053b13d2:add92319...
2025-07-11T19:28:09.015Z [DEBUG] - 📊 Cache stats: 73 hits, 99 misses, 113 entries
2025-07-11T19:28:09.016Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-035, executing check
2025-07-11T19:28:09.017Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-035:053b13d2:add92319
2025-07-11T19:28:09.020Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-035: Multiple Ways
2025-07-11T19:28:09.024Z [DEBUG] - 📱 Starting responsive layout analysis
2025-07-11T19:28:10.056Z [ERROR] - ❌ CMS detection injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:28:10.056Z [ERROR] - ❌ CMS detection injection process failed: - {"error":"CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:28:10.059Z [WARN] - ❌ Utility cms-detection failed after 2 attempts, using fallback - {"error":"CMS detection injection process failed: CMS detection injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:28:10.615Z [INFO] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-035 passed: 100.0% (threshold: 75%) - PASSED
2025-07-11T19:28:10.615Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-035 in 1595ms - Status: passed (100/100)
2025-07-11T19:28:10.626Z [DEBUG] - Page state validation passed
2025-07-11T19:28:10.627Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-035
2025-07-11T19:28:10.627Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-035 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T19:28:10.628Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:28:10.630Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:28:11.028Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":40}
2025-07-11T19:28:11.029Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-035 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.76,"accuracy":0.45,"executionTime":402}
2025-07-11T19:28:11.036Z [DEBUG] - 💾 Cache saved to file: cc7ff2a9bda9ab11c362680288fd25f0.json (key: rule:WCAG-035:053b13d2:add9231...)
2025-07-11T19:28:11.036Z [DEBUG] - 💾 Cached: rule:rule:WCAG-035:053b13d2:add92319 (1858 bytes)
2025-07-11T19:28:11.037Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-035
2025-07-11T19:28:11.038Z [DEBUG] - ✅ Cache hit: rule:rule:WCAG-035:WCAG-035... (accessed 2 times, age: 254s)
2025-07-11T19:28:11.039Z [DEBUG] - 📋 Using cached evidence for WCAG-035
2025-07-11T19:28:11.040Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-035 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-11T19:28:11.041Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:28:11.042Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T19:28:11.284Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:28:11.374Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":40}
2025-07-11T19:28:11.374Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-035 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.76,"accuracy":0.45,"executionTime":334}
2025-07-11T19:28:11.376Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-035: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:28:11.378Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-035"]}
2025-07-11T19:28:11.379Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-035
2025-07-11T19:28:11.379Z [DEBUG] - 🔧 Utility performance recorded for WCAG-035: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:28:11.380Z [DEBUG] - 🔧 Utility analysis completed for WCAG-035: - {"utilitiesUsed":2,"errors":0,"executionTime":2365}
2025-07-11T19:28:11.381Z [DEBUG] - ⏱️ Check WCAG-035 completed in 2370ms (success: true)
2025-07-11T19:28:11.382Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:28:11.387Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:28:11.387Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 97% (64/66)
2025-07-11T19:28:11.388Z [INFO] - ✅ Rule WCAG-035 completed: passed (100/100)
2025-07-11T19:28:11.390Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:28:11.394Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:28:11.395Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 97% (64/66)
2025-07-11T19:28:11.395Z [INFO] - 🔍 Executing rule: Headings and Labels (WCAG-036)
2025-07-11T19:28:11.398Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-036: Headings and Labels
2025-07-11T19:28:11.401Z [DEBUG] - 📁 Cache file not found: 5f405b4a636eed25901701ce70a8f360.json (key: rule:WCAG-036:053b13d2:add9231...)
2025-07-11T19:28:11.402Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-036:053b13d2:add92319...
2025-07-11T19:28:11.403Z [DEBUG] - 📊 Cache stats: 74 hits, 100 misses, 114 entries
2025-07-11T19:28:11.403Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-036, executing check
2025-07-11T19:28:11.404Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-036:053b13d2:add92319
2025-07-11T19:28:11.405Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-036: Headings and Labels
2025-07-11T19:28:11.408Z [DEBUG] - 📋 Using unified DOM structure: 64 headings found
2025-07-11T19:28:11.408Z [DEBUG] - 📋 Using unified DOM structure: 1 forms found
2025-07-11T19:28:11.409Z [DEBUG] - 📁 Cache file not found: 401c39b0f4deb0470156686e33b5e08a.json (key: site:https://tigerconnect.com/...)
2025-07-11T19:28:11.409Z [DEBUG] - 🔍 Cache miss: site:site:https://tigerconnect.com/:form-analysis-{"ana...
2025-07-11T19:28:11.410Z [DEBUG] - 📊 Cache stats: 74 hits, 101 misses, 9 entries
2025-07-11T19:28:11.411Z [DEBUG] - 📝 Starting advanced form accessibility analysis
2025-07-11T19:28:16.293Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:28:16.432Z [ERROR] - ❌ Form analysis injection validation failed: - {"error":"Waiting failed: 5000ms exceeded"}
2025-07-11T19:28:16.433Z [ERROR] - ❌ Form analysis injection process failed: - {"error":"Form analysis injection failed: Waiting failed: 5000ms exceeded"}
2025-07-11T19:28:16.442Z [ERROR] - ❌ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Error in WCAG-036 - {"error":{"message":"Form analysis injection process failed: Form analysis injection failed: Waiting failed: 5000ms exceeded","stack":"Error: Form analysis injection process failed: Form analysis injection failed: Waiting failed: 5000ms exceeded\n    at FormAccessibilityAnalyzer.injectFormAnalysisFunctions (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\form-accessibility-analyzer.ts:715:11)\n    at async FormAccessibilityAnalyzer.analyzeFormAccessibility (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\form-accessibility-analyzer.ts:154:5)\n    at async HeadingsLabelsCheck.executeHeadingsLabelsCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\checks\\headings-labels.ts:136:37)\n    at async EnhancedCheckTemplate.executeCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\check-template.ts:146:24)\n    at async EnhancedCheckTemplate.executeEnhancedCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\enhanced-check-template.ts:226:22)\n    at async HeadingsLabelsCheck.performCheck (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\checks\\headings-labels.ts:44:20)\n    at async WcagOrchestrator.executeAllChecks (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\orchestrator.ts:855:29)\n    at async WcagOrchestrator.performComprehensiveScan (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\orchestrator.ts:211:27)","name":"Error"},"ruleId":"WCAG-036","ruleName":"Headings and Labels","executionTime":5032}
2025-07-11T19:28:16.454Z [DEBUG] - Page state validation passed
2025-07-11T19:28:16.454Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-036
2025-07-11T19:28:16.455Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-036 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"integrationStrategy":"supplement","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T19:28:16.456Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:28:16.457Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 25 times, age: 271s)
2025-07-11T19:28:16.458Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:28:16.460Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:28:16.460Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:28:16.462Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:28:16.464Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:28:16.467Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:28:16.484Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:28:16.506Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:28:16.506Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-036 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":51}
2025-07-11T19:28:16.509Z [DEBUG] - 💾 Cache saved to file: 5f405b4a636eed25901701ce70a8f360.json (key: rule:WCAG-036:053b13d2:add9231...)
2025-07-11T19:28:16.510Z [DEBUG] - 💾 Cached: rule:rule:WCAG-036:053b13d2:add92319 (1195 bytes)
2025-07-11T19:28:16.511Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-036
2025-07-11T19:28:16.514Z [DEBUG] - 📁 Cache file not found: e67352293b7b7cd8ecb16e718a1be3d4.json (key: rule:WCAG-036:WCAG-036...)
2025-07-11T19:28:16.514Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-036:WCAG-036...
2025-07-11T19:28:16.515Z [DEBUG] - 📊 Cache stats: 75 hits, 102 misses, 115 entries
2025-07-11T19:28:16.519Z [DEBUG] - 💾 Cache saved to file: e67352293b7b7cd8ecb16e718a1be3d4.json (key: rule:WCAG-036:WCAG-036...)
2025-07-11T19:28:16.520Z [DEBUG] - 💾 Cached: rule:rule:WCAG-036:WCAG-036 (852 bytes)
2025-07-11T19:28:16.521Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-036 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-11T19:28:16.523Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:28:16.525Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 26 times, age: 271s)
2025-07-11T19:28:16.530Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:28:16.532Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:28:16.532Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:28:16.533Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:28:16.535Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:28:16.536Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:28:16.552Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:28:16.573Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:28:16.573Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-036 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":52}
2025-07-11T19:28:16.575Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-036: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:28:16.577Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-036"]}
2025-07-11T19:28:16.578Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-036
2025-07-11T19:28:16.579Z [DEBUG] - 🔧 Utility performance recorded for WCAG-036: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:28:16.580Z [DEBUG] - 🔧 Utility analysis completed for WCAG-036: - {"utilitiesUsed":2,"errors":0,"executionTime":5180}
2025-07-11T19:28:16.580Z [DEBUG] - ⏱️ Check WCAG-036 completed in 5185ms (success: true)
2025-07-11T19:28:16.581Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:28:16.588Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:28:16.589Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 98% (65/66)
2025-07-11T19:28:16.590Z [INFO] - ✅ Rule WCAG-036 completed: failed (0/100)
2025-07-11T19:28:16.592Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:28:16.598Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:28:16.599Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 98% (65/66)
2025-07-11T19:28:16.600Z [INFO] - 🔍 Executing rule: Language of Parts (WCAG-038)
2025-07-11T19:28:16.603Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting enhanced WCAG-038: Language of Parts
2025-07-11T19:28:16.605Z [DEBUG] - 📁 Cache file not found: 04dd7978f6302fb0dadb528663a4957b.json (key: rule:WCAG-038:053b13d2:add9231...)
2025-07-11T19:28:16.606Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-038:053b13d2:add92319...
2025-07-11T19:28:16.607Z [DEBUG] - 📊 Cache stats: 76 hits, 103 misses, 116 entries
2025-07-11T19:28:16.608Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache miss for WCAG-038, executing check
2025-07-11T19:28:16.609Z [DEBUG] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cache key: rule:WCAG-038:053b13d2:add92319
2025-07-11T19:28:16.610Z [INFO] - 🔍 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting WCAG-038: Language of Parts
2025-07-11T19:28:16.708Z [INFO] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] WCAG-038 passed: 90.0% (threshold: 75%) - PASSED
2025-07-11T19:28:16.708Z [INFO] - 🎯 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Completed WCAG-038 in 99ms - Status: passed (90/100)
2025-07-11T19:28:16.720Z [DEBUG] - Page state validation passed
2025-07-11T19:28:16.720Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Applying utility enhancements for WCAG-038
2025-07-11T19:28:16.721Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-038 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement","enablePatternValidation":true,"enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T19:28:16.722Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:28:16.722Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 27 times, age: 272s)
2025-07-11T19:28:16.723Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T19:28:16.725Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:28:16.728Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:28:16.728Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:28:16.730Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:28:16.731Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:28:16.732Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:28:16.764Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:28:16.786Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":20.2,"accessibilityLevel":"fair"}
2025-07-11T19:28:16.805Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-11T19:28:16.805Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-038 - {"utilitiesUsed":["content-quality","semantic-validation","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.44999999999999996,"executionTime":84}
2025-07-11T19:28:16.810Z [DEBUG] - 💾 Cache saved to file: 04dd7978f6302fb0dadb528663a4957b.json (key: rule:WCAG-038:053b13d2:add9231...)
2025-07-11T19:28:16.811Z [DEBUG] - 💾 Cached: rule:rule:WCAG-038:053b13d2:add92319 (1759 bytes)
2025-07-11T19:28:16.812Z [DEBUG] - 💾 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Cached result for WCAG-038
2025-07-11T19:28:16.813Z [DEBUG] - 📁 Cache file not found: 6669603e6643c9f91e0c1c8e7cd592cc.json (key: rule:WCAG-038:WCAG-038...)
2025-07-11T19:28:16.814Z [DEBUG] - 🔍 Cache miss: rule:rule:WCAG-038:WCAG-038...
2025-07-11T19:28:16.815Z [DEBUG] - 📊 Cache stats: 77 hits, 104 misses, 117 entries
2025-07-11T19:28:16.818Z [DEBUG] - 💾 Cache saved to file: 6669603e6643c9f91e0c1c8e7cd592cc.json (key: rule:WCAG-038:WCAG-038...)
2025-07-11T19:28:16.821Z [DEBUG] - 💾 Cached: rule:rule:WCAG-038:WCAG-038 (1768 bytes)
2025-07-11T19:28:16.822Z [DEBUG] - 🔧 [1de36534-70ae-401c-b6b9-93552ec2dfaa] Starting utility analysis for WCAG-038 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-11T19:28:16.823Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-11T19:28:16.824Z [DEBUG] - ✅ Cache hit: site:site:https://tigerconnect.com/:semantic-validation... (accessed 28 times, age: 272s)
2025-07-11T19:28:16.825Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T19:28:16.826Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-11T19:28:16.826Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-11T19:28:16.828Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-11T19:28:16.829Z [DEBUG] - 🔗 Validating cross-references
2025-07-11T19:28:16.830Z [DEBUG] - 🎯 Analyzing page context
2025-07-11T19:28:16.859Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-11T19:28:16.881Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":20.2,"accessibilityLevel":"fair"}
2025-07-11T19:28:16.884Z [DEBUG] - ✅ [1de36534-70ae-401c-b6b9-93552ec2dfaa] Utility analysis completed for WCAG-038 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":62}
2025-07-11T19:28:16.885Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-038: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T19:28:16.886Z [WARN] - ⚠️ Performance alerts for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"alerts":["Low cache hit rate (0.0%) for WCAG-038"]}
2025-07-11T19:28:16.887Z [INFO] - Alert for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: Low cache hit rate (0.0%) for WCAG-038
2025-07-11T19:28:16.888Z [DEBUG] - 🔧 Utility performance recorded for WCAG-038: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T19:28:16.889Z [DEBUG] - 🔧 Utility analysis completed for WCAG-038: - {"utilitiesUsed":2,"errors":0,"executionTime":285}
2025-07-11T19:28:16.890Z [DEBUG] - ⏱️ Check WCAG-038 completed in 290ms (success: true)
2025-07-11T19:28:16.891Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:28:16.896Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> running
2025-07-11T19:28:16.897Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 100% (66/66)
2025-07-11T19:28:16.898Z [INFO] - ✅ Rule WCAG-038 completed: passed (90/100)
2025-07-11T19:28:16.899Z [INFO] - ✅ All checks completed: 66 results
🔍 WCAG Scoring Debug - Input Results: [
  {
    ruleId: 'WCAG-001',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-002',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-003',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-004',
    status: 'passed',
    score: 84,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0763
  },
  {
    ruleId: 'WCAG-005',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-006',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-007',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-008',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0534
  },
  {
    ruleId: 'WCAG-009',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'robust',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-010',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-011',
    status: 'passed',
    score: 96,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-012',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-013',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0382
  },
  {
    ruleId: 'WCAG-014',
    status: 'passed',
    score: 81,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-015',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-016',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.0382
  },
  {
    ruleId: 'WCAG-017',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '3.0',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-018',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '3.0',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-019',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '3.0',
    weight: 0.0382
  },
  {
    ruleId: 'WCAG-020',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '3.0',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-021',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '3.0',
    weight: 0.0229
  },
  {
    ruleId: 'WCAG-022',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.05
  },
  {
    ruleId: 'WCAG-023',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.03
  },
  {
    ruleId: 'WCAG-044',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-045',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-046',
    status: 'passed',
    score: 99,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-037',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-039',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-040',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-041',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-042',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-043',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-050',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-051',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-052',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-053',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-054',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-055',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-056',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-058',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-059',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-060',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-061',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-062',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-063',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-064',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-065',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'robust',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-066',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'robust',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-057',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'robust',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-047',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-048',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-049',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-024',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-025',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-026',
    status: 'passed',
    score: 89,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-027',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0916
  },
  {
    ruleId: 'WCAG-028',
    status: 'passed',
    score: 80,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-029',
    status: 'passed',
    score: 90,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-030',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0534
  },
  {
    ruleId: 'WCAG-031',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-032',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-033',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-034',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-035',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-036',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-038',
    status: 'passed',
    score: 90,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0535
  }
]
📊 Rule WCAG-001: score=0/100 (0.0%), weight=0.008
📊 Rule WCAG-002: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-003: score=0/100 (0.0%), weight=0.009
📊 Rule WCAG-004: score=84/100 (84.0%), weight=0.010
📊 Rule WCAG-005: score=0/100 (0.0%), weight=0.012
📊 Rule WCAG-006: score=0/100 (0.0%), weight=0.011
📊 Rule WCAG-007: score=0/100 (0.0%), weight=0.012
📊 Rule WCAG-008: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-009: score=0/100 (0.0%), weight=0.005
📊 Rule WCAG-010: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-011: score=96/100 (96.0%), weight=0.004
📊 Rule WCAG-012: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-013: score=0/100 (0.0%), weight=0.005
📊 Rule WCAG-014: score=81/100 (81.0%), weight=0.006
📊 Rule WCAG-015: score=100/100 (100.0%), weight=0.003
📊 Rule WCAG-016: score=0/100 (0.0%), weight=0.003
📊 Rule WCAG-017: score=0/100 (0.0%), weight=0.001
📊 Rule WCAG-018: score=0/100 (0.0%), weight=0.001
📊 Rule WCAG-019: score=0/100 (0.0%), weight=0.002
📊 Rule WCAG-020: score=0/100 (0.0%), weight=0.002
📊 Rule WCAG-021: score=75/100 (75.0%), weight=0.001
📊 Rule WCAG-022: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-023: score=100/100 (100.0%), weight=0.003
📊 Rule WCAG-044: score=75/100 (75.0%), weight=0.012
📊 Rule WCAG-045: score=0/100 (0.0%), weight=0.011
📊 Rule WCAG-046: score=99/100 (99.0%), weight=0.008
📊 Rule WCAG-037: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-039: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-040: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-041: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-042: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-043: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-050: score=100/100 (100.0%), weight=0.006
📊 Rule WCAG-051: score=0/100 (0.0%), weight=0.012
📊 Rule WCAG-052: score=75/100 (75.0%), weight=0.008
📊 Rule WCAG-053: score=100/100 (100.0%), weight=0.008
📊 Rule WCAG-054: score=100/100 (100.0%), weight=0.008
📊 Rule WCAG-055: score=0/100 (0.0%), weight=0.008
📊 Rule WCAG-056: score=100/100 (100.0%), weight=0.008
📊 Rule WCAG-058: score=0/100 (0.0%), weight=0.005
📊 Rule WCAG-059: score=0/100 (0.0%), weight=0.005
📊 Rule WCAG-060: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-061: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-062: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-063: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-064: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-065: score=75/100 (75.0%), weight=0.002
📊 Rule WCAG-066: score=0/100 (0.0%), weight=0.002
📊 Rule WCAG-057: score=100/100 (100.0%), weight=0.003
📊 Rule WCAG-047: score=0/100 (0.0%), weight=0.011
📊 Rule WCAG-048: score=0/100 (0.0%), weight=0.009
📊 Rule WCAG-049: score=0/100 (0.0%), weight=0.009
📊 Rule WCAG-024: score=100/100 (100.0%), weight=0.008
📊 Rule WCAG-025: score=75/100 (75.0%), weight=0.009
📊 Rule WCAG-026: score=89/100 (89.0%), weight=0.011
📊 Rule WCAG-027: score=0/100 (0.0%), weight=0.016
📊 Rule WCAG-028: score=80/100 (80.0%), weight=0.011
📊 Rule WCAG-029: score=90/100 (90.0%), weight=0.011
📊 Rule WCAG-030: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-031: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-032: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-033: score=100/100 (100.0%), weight=0.006
📊 Rule WCAG-034: score=100/100 (100.0%), weight=0.006
📊 Rule WCAG-035: score=100/100 (100.0%), weight=0.009
📊 Rule WCAG-036: score=0/100 (0.0%), weight=0.009
📊 Rule WCAG-038: score=90/100 (90.0%), weight=0.007
🎯 WCAG Final Score Calculation:
      - Total Weighted Score: 14.86
      - Total Weight: 0.436
      - Final Score: 34%
      - Rules Processed: 66/66
      - Passed Rules: 24
      - Failed Rules: 42
2025-07-11T19:28:17.002Z [INFO] - 💾 Saving WCAG scan result: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:28:17.003Z [INFO] - 📊 Scan summary: {"scanId":"1de36534-70ae-401c-b6b9-93552ec2dfaa","totalRules":66,"overallScore":34,"levelAchieved":"None","riskLevel":"Critical"}
2025-07-11T19:28:17.005Z [INFO] - 💾 Saving WCAG scan result: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:28:17.357Z [INFO] - ✅ WCAG scan result saved: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:28:17.357Z [INFO] - ✅ WCAG scan result saved successfully: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:28:17.361Z [INFO] - 📝 Storing 1 manual review items for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:28:17.370Z [INFO] - ✅ Stored 1 manual review items for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:28:17.370Z [INFO] - ✅ Stored 1 manual review items for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:28:17.373Z [INFO] - 📝 Updating WCAG scan status: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> completed
2025-07-11T19:28:17.402Z [INFO] - ✅ WCAG scan status updated: 1de36534-70ae-401c-b6b9-93552ec2dfaa -> completed
2025-07-11T19:28:17.403Z [INFO] - 📈 Scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: 100% (66/66)
2025-07-11T19:28:17.405Z [INFO] - ✅ Comprehensive WCAG scan completed: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:28:17.407Z [INFO] - 📊 Overall Score: 34% | Level: None | Risk: Critical
2025-07-11T19:28:17.410Z [INFO] - 📈 New performance baseline established - {"performanceScore":80,"averageCheckDuration":4102.712121212121,"memoryPeakMB":695}
2025-07-11T19:28:17.410Z [INFO] - 📊 Performance report generated for scan: 1de36534-70ae-401c-b6b9-93552ec2dfaa - {"duration":281564,"checksExecuted":66,"successRate":100,"memoryPeak":695,"performanceScore":80}
2025-07-11T19:28:17.411Z [INFO] - 📊 Performance report for scan 1de36534-70ae-401c-b6b9-93552ec2dfaa: - {"duration":281564,"performanceScore":80,"memoryPeak":695,"recommendations":["Consider enabling more aggressive caching to reduce scan time","Optimize slow checks: WCAG-063","Low browser pool efficiency - consider increasing pool size"]}
2025-07-11T19:28:18.659Z [DEBUG] - ✅ Released page back to pool for scan: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:28:18.660Z [DEBUG] - 📊 Unregistered active scan: 1de36534-70ae-401c-b6b9-93552ec2dfaa (total: 0)
2025-07-11T19:28:18.662Z [INFO] - ✅ [6e320ec8-5eb3-4651-ad3d-6c3ca8575707] WCAG scan completed successfully: 1de36534-70ae-401c-b6b9-93552ec2dfaa
2025-07-11T19:28:21.306Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:28:24.912Z [WARN] - ⚠️ Insufficient data for trend analysis
2025-07-11T19:28:24.913Z [WARN] - ⚠️ Insufficient data for correlation analysis
2025-07-11T19:28:25.073Z [DEBUG] - 📊 Memory usage: 476MB / 529MB (growth: -42.0MB/min)
2025-07-11T19:28:25.073Z [WARN] - ⚠️ High memory usage detected: 476MB
2025-07-11T19:28:25.075Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-11T19:28:25.077Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-11T19:28:25.078Z [INFO] - ✅ Proactive cleanup completed
2025-07-11T19:28:26.315Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:28:31.322Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:28:36.179Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T19:28:36.179Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T19:28:36.181Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T19:28:36.183Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T19:28:36.336Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:28:41.351Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:28:44.166Z [INFO] - 🔓 [1fd0ef86-0033-41d5-bdf8-c98ec85c6e23] Development mode: Mock authentication applied
2025-07-11T19:28:44.170Z [INFO] - 📋 [1fd0ef86-0033-41d5-bdf8-c98ec85c6e23] Fetching WCAG scans for user: 9fed30a7-64b4-4ffe-b531-6e9cf592952b
2025-07-11T19:28:44.172Z [INFO] - 📊 Fetching WCAG scans for user: 9fed30a7-64b4-4ffe-b531-6e9cf592952b - {"options":{"page":1,"limit":20,"sortBy":"scanTimestamp","sortOrder":"desc"}}
User found: {
  id: '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
  keycloak_id: '4eb85cce-8784-4bf1-b50a-9ce3a80fb67b',
  email: '<EMAIL>',
  created_at: 2025-06-20T10:13:02.039Z,
  updated_at: 2025-06-20T10:13:02.039Z
}
2025-07-11T19:28:46.366Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:28:51.379Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T19:28:55.077Z [WARN] - ⚠️ High memory usage detected: 481MB
2025-07-11T19:28:55.078Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-11T19:28:55.081Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-11T19:28:55.083Z [INFO] - ✅ Proactive cleanup completed
2025-07-11T19:28:55.085Z [WARN] - 🔍 Memory leak detected: 10.0MB/min growth
2025-07-11T19:28:55.086Z [WARN] - 🔍 Memory leak investigation: - {"browsers":1,"availablePages":1,"inUsePages":0,"heapUsed":481,"heapTotal":545}
2025-07-11T19:28:55.087Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-11T19:28:55.088Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-11T19:28:55.090Z [INFO] - ✅ Proactive cleanup completed
2025-07-11T19:28:56.380Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}

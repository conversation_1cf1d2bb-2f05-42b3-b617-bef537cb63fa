{"data": {"ruleId": "WCAG-063", "ruleName": "Pronunciation", "category": "understandable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0305, "automated": true, "evidence": [{"type": "text", "description": "Words that may need pronunciation guidance", "value": "Found 83 words that could benefit from pronunciation guidance for AAA compliance", "elementCount": 1, "affectedSelectors": ["Found", "words", "that", "could", "benefit", "from", "pronunciation", "guidance", "for", "AAA", "compliance"], "severity": "warning", "metadata": {"scanDuration": 129437, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T10:48:56.882Z"}}}, {"type": "text", "description": "Word may need pronunciation guidance: \"contact\"", "value": "Context: \"Contact Sales: (800) 572-0470\nContact\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 129437, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T10:48:56.882Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "Contact", "Sales", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"tigerconnect\"", "value": "Context: \"ct Support\nLoginExpand\n\t\t\t\t\n\n\tTigerConnect\n\tPhysician Scheduling\n\tTigerC\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 129437, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T10:48:56.882Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "ct", "Support", "LoginExpand", "TigerConnect", "Physician", "Scheduling", "TigerC", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"wrap\"", "value": "Context: \"19_f82b81-6e > .kt-row-column-wrap{align-content:start;}:where(.\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 129437, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T10:48:56.882Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "f82b81-6e", "kt-row-column-wrap", "align-content", "start", "where", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"align\"", "value": "Context: \"2b81-6e > .kt-row-column-wrap{align-content:start;}:where(.kb-row\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 129437, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T10:48:56.882Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "b81-6e", "kt-row-column-wrap", "align-content", "start", "where", "kb-row", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"left\"", "value": "Context: \"ntent-width, 1290px );padding-left:var(--global-content-edge-pad\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 129437, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T10:48:56.882Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "ntent-width", "px", "padding-left", "var", "global-content-edge-pad", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"right\"", "value": "Context: \"content-edge-padding);padding-right:var(--global-content-edge-pad\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 129437, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T10:48:56.882Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "content-edge-padding", "padding-right", "var", "global-content-edge-pad", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"height\"", "value": "Context: \"obal-kb-spacing-md, 2rem);min-height:500px;}.kb-row-layout-id419_f\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 129437, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T10:48:56.882Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "obal-kb-spacing-md", "rem", "min-height", "px", "kb-row-layout-id419_f", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"isolation\"", "value": "Context: \"eft-radius:15px;overflow:clip;isolation:isolate;}.kb-row-layout-id419\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 129437, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T10:48:56.882Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "eft-radius", "px", "overflow", "clip", "isolation", "isolate", "kb-row-layout-id419", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"direction\"", "value": "Context: \"0 > .kt-inside-inner-col{flex-direction:column;}.kadence-column419_53\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 129437, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T10:48:56.882Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "kt-inside-inner-col", "flex-direction", "column", "kadence-column419_53", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"aligncenter\"", "value": "Context: \"-b0 > .kt-inside-inner-col > .aligncenter{width:100%;}.kadence-column41\" (Category: complex-word)", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 129437, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T10:48:56.882Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "b0", "kt-inside-inner-col", "aligncenter", "width", "kadence-column41", "Category", "complex-word"]}], "recommendations": ["Provide pronunciation guidance for foreign words and technical terms", "Use phonetic notation (IPA) or simple pronunciation guides", "Consider audio pronunciation for complex words", "Use tooltips or expandable sections for pronunciation information", "Test pronunciation guidance with users who may be unfamiliar with the terms"], "executionTime": 126150, "originalScore": 60}, "timestamp": 1752230936882, "hash": "4ea97d1e777c70a82c9af4941ac82549", "accessCount": 1, "lastAccessed": 1752230936882, "size": 7455}
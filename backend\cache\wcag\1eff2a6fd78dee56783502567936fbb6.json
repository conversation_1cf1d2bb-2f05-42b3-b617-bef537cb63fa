{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "metadata": {"scanDuration": 20, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.8, "checkType": "status-message-analysis", "ariaLiveRegionDetection": true, "statusMessageAccessibility": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-057", "ruleName": "Status Messages", "timestamp": "2025-07-11T18:03:43.231Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752257023231, "hash": "746b1e00d8e4fab080c4be9da9748e07", "accessCount": 1, "lastAccessed": 1752257023231, "size": 827, "metadata": {"originalKey": "WCAG-057:WCAG-057:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-057_wcag-057_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752257023231, "version": "1.1", "keyHash": "38a8fbebc5364a62f5299dea0dea9ec1"}}
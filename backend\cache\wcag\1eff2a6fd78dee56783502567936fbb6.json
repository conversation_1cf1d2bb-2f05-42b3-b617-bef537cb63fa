{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame 'A362B1B11B79F8E7E1503ABC8B8AFA25'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A362B1B11B79F8E7E1503ABC8B8AFA25"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.8, "checkType": "status-message-analysis", "ariaLiveRegionDetection": true, "statusMessageAccessibility": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-057", "ruleName": "Status Messages", "timestamp": "2025-07-11T18:49:44.839Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259784839, "hash": "22f2ca398e22e5927fd48541ac975a8a", "accessCount": 1, "lastAccessed": 1752259784839, "size": 827, "metadata": {"originalKey": "WCAG-057:WCAG-057:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-057_wcag-057_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752259784839, "version": "1.1", "keyHash": "38a8fbebc5364a62f5299dea0dea9ec1"}}
{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "metadata": {"scanDuration": 62, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "pointer-gesture-analysis", "gestureDetection": true, "alternativeInputValidation": true, "advancedGestureDetection": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-049", "ruleName": "Pointer Gestures", "timestamp": "2025-07-11T18:03:22.349Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752257002349, "hash": "3fdee34b48bffefe7089c0c61f138362", "accessCount": 1, "lastAccessed": 1752257002349, "size": 827, "metadata": {"originalKey": "WCAG-049:WCAG-049:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-049_wcag-049_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752257002349, "version": "1.1", "keyHash": "54eaf720114bf4df815b0a6676d18afc"}}
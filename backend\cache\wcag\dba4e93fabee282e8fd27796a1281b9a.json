{"data": {"ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "passed", "score": 100, "maxScore": 100, "weight": 0.0611, "automated": true, "evidence": [{"type": "info", "description": "Advanced layout analysis for skip links context", "element": "skip-links", "value": "{\"overallScore\":80,\"criticalIssues\":[],\"recommendations\":[\"Increase target sizes to meet WCAG minimum requirements\"],\"performanceMetrics\":{\"analysisTime\":712,\"elementsAnalyzed\":1000,\"breakpointsTested\":5}}", "severity": "info", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "recommendations", "Increase", "target", "sizes", "to", "meet", "WCAG", "minimum", "requirements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "breakpointsTested"], "metadata": {"scanDuration": 2082, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T11:39:18.562Z"}}}], "recommendations": ["Add skip links to bypass repetitive navigation", "Implement proper heading structure for navigation", "Use landmark elements (main, nav, aside) for page structure"], "executionTime": 718, "originalScore": 100}, "timestamp": 1752233958562, "hash": "a6a156f1034323754b4d3a08303a43c3", "accessCount": 1, "lastAccessed": 1752233958562, "size": 1262}
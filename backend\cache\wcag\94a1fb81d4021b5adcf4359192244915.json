{"data": {"detectedCMS": [], "primaryCMS": {"cms": "none", "confidence": 0, "apiEndpoints": [], "contentTypes": [], "jamstackArchitecture": {"isJAMStack": false}, "accessibility": {"score": 50, "hasStructuredContent": false, "hasAccessibilityFields": false, "hasImageAltText": false, "hasSemanticMarkup": false, "contentQuality": "medium", "issues": [], "recommendations": []}}, "jamstackAnalysis": {"architecture": "traditional", "features": {"hasPrerendering": false, "hasStaticGeneration": false, "hasIncrementalRegeneration": false, "hasEdgeFunctions": false}, "performance": {"hasOptimizedImages": false, "hasLazyLoading": false, "hasCDN": false, "hasServiceWorker": false}, "accessibility": {"hasStaticAccessibility": false, "hasRuntimeAccessibility": true, "score": 50}}, "contentAnalysis": [], "overallAccessibilityScore": 0, "contentQualityScore": 0, "recommendations": ["CMS analysis failed - manual review recommended"], "bestPractices": [], "modernPatterns": []}, "timestamp": 1752264411860, "hash": "cce583be3e78339941ae42b6eeb4bb92", "accessCount": 1, "lastAccessed": 1752264411860, "size": 895, "metadata": {"originalKey": "https://tigerconnect.com/:cms-analysis", "normalizedKey": "https_tigerconnect.com_cms-analysis", "savedAt": 1752264411861, "version": "1.1", "keyHash": "86585c4365b06adf39994c4d3e884bc5"}}
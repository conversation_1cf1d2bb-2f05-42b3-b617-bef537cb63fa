{"data": [{"type": "info", "description": "Link with descriptive text", "value": "<a href=\"#main\">Skip to content</a>", "selector": "a:nth-of-type(1)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(1)", "a", "href", "#main", "<PERSON><PERSON>", "to", "content"], "severity": "info", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "info", "description": "Link with descriptive text", "value": "<a href=\"tel:************\">Contact Sales: (*************</a>", "selector": "a:nth-of-type(2)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(2)", "a", "href", "tel", "Contact", "Sales"], "severity": "info", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "info", "description": "Link with descriptive text", "value": "<a href=\"https://tigerconnect.com/about/contact-us/#tab-contactsupport\">Contact Support</a>", "selector": "a:nth-of-type(3)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(3)", "a", "href", "https", "tigerconnect.com", "about", "contact-us", "#tab-contactsupport", "Contact", "Support", "//tigerconnect.com/about/contact-us/#tab-contactsupport\">Contact"], "severity": "info", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/\"></a>", "selector": "a:nth-of-type(9)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(9)", "a", "href", "https", "tigerconnect.com", "//tigerconnect.com/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/healthcare-professionals/\"></a>", "selector": "a:nth-of-type(12)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(12)", "a", "href", "https", "tigerconnect.com", "healthcare-professionals", "//tigerconnect.com/healthcare-professionals/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/products/\"></a>", "selector": "a:nth-of-type(33)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(33)", "a", "href", "https", "tigerconnect.com", "products", "//tigerconnect.com/products/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/resource-center/\"></a>", "selector": "a:nth-of-type(52)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(52)", "a", "href", "https", "tigerconnect.com", "resource-center", "//tigerconnect.com/resource-center/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 6, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/about/\"></a>", "selector": "a:nth-of-type(77)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(77)", "a", "href", "https", "tigerconnect.com", "about", "//tigerconnect.com/about/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 7, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/\"></a>", "selector": "a:nth-of-type(92)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(92)", "a", "href", "https", "tigerconnect.com", "//tigerconnect.com/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 8, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/products/patient-engagement-software/\"></a>", "selector": "a:nth-of-type(96)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(96)", "a", "href", "https", "tigerconnect.com", "products", "patient-engagement-software", "//tigerconnect.com/products/patient-engagement-software/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 9, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://newtc.kinsta.cloud/products/clinical-collaboration/\"></a>", "selector": "a:nth-of-type(100)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(100)", "a", "href", "https", "newtc.kinsta.cloud", "products", "clinical-collaboration", "//newtc.kinsta.cloud/products/clinical-collaboration/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 10, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://newtc.kinsta.cloud/products/clinical-collaboration/\"></a>", "selector": "a:nth-of-type(102)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(102)", "a", "href", "https", "newtc.kinsta.cloud", "products", "clinical-collaboration", "//newtc.kinsta.cloud/products/clinical-collaboration/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 11, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/products/alarm-management-event-notification-software/\"></a>", "selector": "a:nth-of-type(104)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(104)", "a", "href", "https", "tigerconnect.com", "products", "alarm-management-event-notification-software", "//tigerconnect.com/products/alarm-management-event-notification-software/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 12, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/products/patient-engagement-software/\"></a>", "selector": "a:nth-of-type(106)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(106)", "a", "href", "https", "tigerconnect.com", "products", "patient-engagement-software", "//tigerconnect.com/products/patient-engagement-software/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 13, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://newtc.kinsta.cloud/products/clinical-collaboration/\"></a>", "selector": "a:nth-of-type(110)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(110)", "a", "href", "https", "newtc.kinsta.cloud", "products", "clinical-collaboration", "//newtc.kinsta.cloud/products/clinical-collaboration/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 14, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://newtc.kinsta.cloud/products/clinical-collaboration/\"></a>", "selector": "a:nth-of-type(112)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(112)", "a", "href", "https", "newtc.kinsta.cloud", "products", "clinical-collaboration", "//newtc.kinsta.cloud/products/clinical-collaboration/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 15, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/products/alarm-management-event-notification-software/\"></a>", "selector": "a:nth-of-type(114)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(114)", "a", "href", "https", "tigerconnect.com", "products", "alarm-management-event-notification-software", "//tigerconnect.com/products/alarm-management-event-notification-software/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 16, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/products/patient-engagement-software/\"></a>", "selector": "a:nth-of-type(116)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(116)", "a", "href", "https", "tigerconnect.com", "products", "patient-engagement-software", "//tigerconnect.com/products/patient-engagement-software/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 17, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://newtc.kinsta.cloud/products/clinical-collaboration/\"></a>", "selector": "a:nth-of-type(120)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(120)", "a", "href", "https", "newtc.kinsta.cloud", "products", "clinical-collaboration", "//newtc.kinsta.cloud/products/clinical-collaboration/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 18, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://newtc.kinsta.cloud/products/clinical-collaboration/\"></a>", "selector": "a:nth-of-type(122)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(122)", "a", "href", "https", "newtc.kinsta.cloud", "products", "clinical-collaboration", "//newtc.kinsta.cloud/products/clinical-collaboration/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 19, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/products/alarm-management-event-notification-software/\"></a>", "selector": "a:nth-of-type(124)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(124)", "a", "href", "https", "tigerconnect.com", "products", "alarm-management-event-notification-software", "//tigerconnect.com/products/alarm-management-event-notification-software/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 20, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/products/patient-engagement-software/\"></a>", "selector": "a:nth-of-type(126)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(126)", "a", "href", "https", "tigerconnect.com", "products", "patient-engagement-software", "//tigerconnect.com/products/patient-engagement-software/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 21, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.461Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://newtc.kinsta.cloud/products/clinical-collaboration/\"></a>", "selector": "a:nth-of-type(130)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(130)", "a", "href", "https", "newtc.kinsta.cloud", "products", "clinical-collaboration", "//newtc.kinsta.cloud/products/clinical-collaboration/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 22, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"https://tigerconnect.com/workflows/critical-response-workflows/\">Learn More</a>", "selector": "a:nth-of-type(136)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(136)", "a", "href", "https", "tigerconnect.com", "workflows", "critical-response-workflows", "Learn", "More", "//tigerconnect.com/workflows/critical-response-workflows/\">Learn"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 23, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"/healthcare-professionals/physicians/\">Read More</a>", "selector": "a:nth-of-type(151)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(151)", "a", "href", "healthcare-professionals", "physicians", "Read", "More"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 24, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"https://tigerconnect.com/healthcare-professionals/it/\">Read More</a>", "selector": "a:nth-of-type(152)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(152)", "a", "href", "https", "tigerconnect.com", "healthcare-professionals", "it", "Read", "More", "//tigerconnect.com/healthcare-professionals/it/\">Read"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 25, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"https://tigerconnect.com/healthcare-professionals/nurses/\">Read More</a>", "selector": "a:nth-of-type(153)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(153)", "a", "href", "https", "tigerconnect.com", "healthcare-professionals", "nurses", "Read", "More", "//tigerconnect.com/healthcare-professionals/nurses/\">Read"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 26, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"https://tigerconnect.com/healthcare-professionals/execs/\">Read More</a>", "selector": "a:nth-of-type(154)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(154)", "a", "href", "https", "tigerconnect.com", "healthcare-professionals", "execs", "Read", "More", "//tigerconnect.com/healthcare-professionals/execs/\">Read"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 27, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/resources/reports/2024-state-of-healthcare-collaboration/\"></a>", "selector": "a:nth-of-type(165)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(165)", "a", "href", "https", "tigerconnect.com", "resources", "reports", "state-of-healthcare-collaboration", "//tigerconnect.com/resources/reports/2024-state-of-healthcare-collaboration/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 28, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/resources/reports/gartner-report-2024-gartner-magic-quadrant-for-clinical-communication-and-collaboration\"></a>", "selector": "a:nth-of-type(167)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(167)", "a", "href", "https", "tigerconnect.com", "resources", "reports", "gartner-report-2024-gartner-magic-quadrant-for-clinical-communication-and-collaboration", "//tigerconnect.com/resources/reports/gartner-report-2024-gartner-magic-quadrant-for-clinical-communication-and-collaboration\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 29, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://apps.apple.com/rs/app/tigerconnect/id355832697\"></a>", "selector": "a:nth-of-type(203)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(203)", "a", "href", "https", "apps.apple.com", "rs", "app", "tigerconnect", "id355832697", "//apps.apple.com/rs/app/tigerconnect/id355832697\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 30, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://play.google.com/store/apps/details?id=com.tigertext&hl=en_US&gl=US&pli=1\"></a>", "selector": "a:nth-of-type(204)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(204)", "a", "href", "https", "play.google.com", "store", "apps", "details", "id", "com.tigertext", "hl", "en_US", "gl", "US", "pli", "//play.google.com/store/apps/details?id=com.tigertext&hl=en_US&gl=US&pli=1\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 31, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://www.facebook.com/TigerConnected\"></a>", "selector": "a:nth-of-type(205)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(205)", "a", "href", "https", "www.facebook.com", "TigerConnected", "//www.facebook.com/TigerConnected\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 32, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://www.instagram.com/lifeattigerconnect/\"></a>", "selector": "a:nth-of-type(206)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(206)", "a", "href", "https", "www.instagram.com", "lifeattigerconnect", "//www.instagram.com/lifeattigerconnect/\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 33, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://www.linkedin.com/company/tigerconnect\"></a>", "selector": "a:nth-of-type(207)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(207)", "a", "href", "https", "www.linkedin.com", "company", "tigerconnect", "//www.linkedin.com/company/tigerconnect\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 34, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://twitter.com/TigerConnect\"></a>", "selector": "a:nth-of-type(208)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(208)", "a", "href", "https", "twitter.com", "TigerConnect", "//twitter.com/TigerConnect\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 35, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://www.youtube.com/c/tigerconnect\"></a>", "selector": "a:nth-of-type(209)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(209)", "a", "href", "https", "www.youtube.com", "c", "tigerconnect", "//www.youtube.com/c/tigerconnect\"></a>"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 36, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"https://tigerconnect.com/products/careconduit/\">Learn More</a>", "selector": "a:nth-of-type(211)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(211)", "a", "href", "https", "tigerconnect.com", "products", "careconduit", "Learn", "More", "//tigerconnect.com/products/careconduit/\">Learn"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 37, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"https://tigerconnect.com/products/careconduit/\">Learn More</a>", "selector": "a:nth-of-type(214)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(214)", "a", "href", "https", "tigerconnect.com", "products", "careconduit", "Learn", "More", "//tigerconnect.com/products/careconduit/\">Learn"], "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 38, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "warning", "description": "Link purpose analysis summary", "value": "260 total links, 36 problematic", "selector": "a[href]", "elementCount": 1, "affectedSelectors": ["a[href]", "total", "links", "problematic"], "severity": "warning", "metadata": {"scanDuration": 17, "elementsAnalyzed": 40, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 39, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T20:11:38.462Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true}}}], "timestamp": 1752264698462, "hash": "eed8f338faeeed4133488ee2cae63192", "accessCount": 1, "lastAccessed": 1752264698462, "size": 35272, "metadata": {"originalKey": "WCAG-026:WCAG-026", "normalizedKey": "wcag-026_wcag-026", "savedAt": 1752264698463, "version": "1.1", "keyHash": "8622bf89e4129cc5a8bd60c69a775111"}}
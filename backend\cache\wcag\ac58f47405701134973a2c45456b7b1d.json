{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Cannot read properties of undefined (reading 'getElementSelector')\npptr:evaluate;FormAccessibilityAnalyzer.getForms%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cutils%5Cform-accessibility-analyzer.ts%3A434%3A27):7:44", "severity": "error", "elementCount": 1, "affectedSelectors": ["Cannot", "read", "properties", "of", "undefined", "reading", "getElementSelector", "pptr", "evaluate", "FormAccessibilityAnalyzer.getForms", "D", "A", "CWeb", "projects", "CCom<PERSON>ly", "Checker", "Cbackend", "Csrc", "Ccompliance", "Cwcag", "Cutils", "Cform-accessibility-analyzer.ts", "A434", "A27"], "metadata": {"scanDuration": 8, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "error-prevention-analysis", "formAnalysis": true, "criticalOperationDetection": true, "preventionMethodValidation": true, "riskAssessment": true, "evidenceIndex": 0, "ruleId": "WCAG-032", "ruleName": "Error Prevention", "timestamp": "2025-07-11T11:39:39.754Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752233979754, "hash": "842e24bc1ba36573ff794a926a0d4f3d", "accessCount": 1, "lastAccessed": 1752233979754, "size": 1203}
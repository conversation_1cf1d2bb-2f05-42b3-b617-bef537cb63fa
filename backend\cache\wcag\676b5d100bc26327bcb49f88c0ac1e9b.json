{"data": {"ruleId": "WCAG-016", "ruleName": "Redundant Entry", "category": "understandable", "wcagVersion": "2.2", "successCriterion": "0.0.0", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.07, "automated": false, "evidence": [{"type": "text", "description": "Redundant entry analysis summary", "value": "2/1 checks pass automated tests, 0 require manual review", "severity": "info", "elementCount": 1, "affectedSelectors": ["checks", "pass", "automated", "tests", "require", "manual", "review"], "metadata": {"scanDuration": 520, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-016", "ruleName": "Redundant Entry", "timestamp": "2025-07-11T10:35:05.979Z"}}}, {"type": "text", "description": "No multi-step form indicators found", "value": "Single-page form or no complex form flow detected", "severity": "info", "elementCount": 1, "affectedSelectors": ["Single-page", "form", "or", "no", "complex", "flow", "detected"], "metadata": {"scanDuration": 520, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-016", "ruleName": "Redundant Entry", "timestamp": "2025-07-11T10:35:05.979Z"}}}], "recommendations": [], "executionTime": 0, "errorMessage": "", "manualReviewItems": []}, "timestamp": 1752230105979, "hash": "9ce4b0be5d21df6dffaf3a26c3ee9a5a", "accessCount": 1, "lastAccessed": 1752230105979, "size": 1273}
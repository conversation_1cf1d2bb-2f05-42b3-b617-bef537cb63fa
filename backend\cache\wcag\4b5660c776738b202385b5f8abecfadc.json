{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame 'A362B1B11B79F8E7E1503ABC8B8AFA25'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A362B1B11B79F8E7E1503ABC8B8AFA25"], "metadata": {"scanDuration": 2, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T18:49:46.006Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259786006, "hash": "38eb6b377846e9bf3446441a86bfa12f", "accessCount": 1, "lastAccessed": 1752259786006, "size": 809, "metadata": {"originalKey": "WCAG-047:WCAG-047:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-047_wcag-047_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752259786006, "version": "1.1", "keyHash": "e9d422d098ce7304072df9b3aadcdc19"}}
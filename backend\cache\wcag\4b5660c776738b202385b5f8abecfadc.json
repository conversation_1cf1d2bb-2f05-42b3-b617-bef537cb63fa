{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "metadata": {"scanDuration": 30, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T18:03:44.741Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752257024741, "hash": "aacdbc0fd7fb82669030414cba13571d", "accessCount": 1, "lastAccessed": 1752257024741, "size": 809, "metadata": {"originalKey": "WCAG-047:WCAG-047:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-047_wcag-047_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752257024742, "version": "1.1", "keyHash": "e9d422d098ce7304072df9b3aadcdc19"}}
/**
 * WCAG-036: Headings and Labels Check
 * Success Criterion: 2.4.6 Headings and Labels (Level AA)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { FormAccessibilityAnalyzer } from '../utils/form-accessibility-analyzer';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import { DOMIntegrationHelper } from '../utils/dom-integration-helper';

export interface HeadingsLabelsConfig extends EnhancedCheckConfig {
  enableFormAccessibilityAnalysis?: boolean;
  enableAISemanticValidation?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableAccessibilityPatterns?: boolean;
}

export class HeadingsLabelsCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private formAccessibilityAnalyzer = FormAccessibilityAnalyzer.getInstance();
  private domHelper = DOMIntegrationHelper.getInstance();

  async performCheck(config: HeadingsLabelsConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: HeadingsLabelsConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAISemanticValidation: true,
      enableContentQualityAnalysis: true,
      enableAccessibilityPatterns: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-036',
      'Headings and Labels',
      'operable',
      0.0815,
      'AA',
      enhancedConfig,
      this.executeHeadingsLabelsCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with heading/label structure analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-036',
        ruleName: 'Headings and Labels',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.85,
          checkType: 'content-structure-analysis',
          headingAnalysis: true,
          labelAnalysis: true,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.75,
        maxEvidenceItems: 50,
      },
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter((ev) => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'headings-labels-analysis',
        confidence: 0.8,
        additionalData: {
          checkType: 'content-accessibility',
          automationLevel: 'high',
        },
      },
    };
  }

  private async executeHeadingsLabelsCheck(
    page: Page,
    config: HeadingsLabelsConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // ✅ USE UNIFIED DOM EXTRACTOR: Get headings and forms from unified structure
    const headings = await this.domHelper.getHeadings(config, page, {
      requiresPageStructure: false,
      fallbackToDirectExtraction: true,
      logMissingStructure: true,
    });

    const forms = await this.domHelper.getForms(config, page, {
      requiresPageStructure: false,
      fallbackToDirectExtraction: true,
      logMissingStructure: true,
    });

    // Enhanced form accessibility analysis using FormAccessibilityAnalyzer
    const formAccessibilityReport = await this.formAccessibilityAnalyzer.analyzeFormAccessibility(
      page,
      {
        analyzeLabels: true,
        analyzeGrouping: true,
        analyzeKeyboardAccess: true,
        strictMode: true,
      },
    );

    // ✅ ANALYZE HEADINGS: Use unified structure data
    const headingsAnalysis = await this.analyzeHeadingsHierarchyFromStructure(headings);

    // Analyze form labels quality using FormAccessibilityAnalyzer
    const formLabelsAnalysis = await this.analyzeFormLabelsQuality(
      formAccessibilityReport as unknown as {
        forms: Array<{
          fields: Array<{
            selector: string;
            hasLabel: boolean;
            isAccessible: boolean;
            labelText: string;
          }>;
        }>;
      },
    );

    // Analyze semantic relationships
    const semanticRelationshipsAnalysis = await this.analyzeSemanticRelationships(page);

    // Combine analysis results
    const allAnalyses = [headingsAnalysis, formLabelsAnalysis, semanticRelationshipsAnalysis];
    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * ✅ NEW: Analyze headings hierarchical structure using unified DOM structure
   */
  private async analyzeHeadingsHierarchyFromStructure(headings: any[]) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    if (headings.length === 0) {
      issues.push('No headings found on the page');
      recommendations.push('Add descriptive headings to structure content');
      return { evidence, issues, recommendations, totalChecks: 1, passedChecks: 0 };
    }

    let totalChecks = headings.length;
    let passedChecks = 0;
    let previousLevel = 0;
    let hasH1 = false;
    const headingTexts = new Set<string>();

    for (const heading of headings) {
      const level = heading.level;
      const text = heading.text;
      const isEmpty = !heading.hasText;
      const isDescriptive = text.length > 5 && !text.match(/^(heading|title|section|item)$/i);
      let headingPassed = true;

      // Check for H1
      if (level === 1) {
        hasH1 = true;
      }

      // Check hierarchy
      if (previousLevel > 0 && level > previousLevel + 1) {
        issues.push(`Heading hierarchy skip: ${heading.tagName} follows h${previousLevel}`);
        headingPassed = false;
      }

      // Check for empty headings
      if (isEmpty) {
        issues.push(`Empty heading found: ${heading.selector}`);
        headingPassed = false;
      }

      // Check for descriptive text
      if (!isEmpty && !isDescriptive) {
        issues.push(`Non-descriptive heading: "${text}" at ${heading.selector}`);
        headingPassed = false;
      }

      // Check for duplicate headings
      if (headingTexts.has(text) && !isEmpty) {
        issues.push(`Duplicate heading text: "${text}" at ${heading.selector}`);
        headingPassed = false;
      }

      if (headingPassed) {
        passedChecks++;
      }

      headingTexts.add(text);
      previousLevel = level;

      evidence.push({
        type: 'text',
        description: `Heading analysis: ${heading.tagName}`,
        value: text || '[empty]',
        selector: heading.selector,
        severity: isEmpty ? 'error' : !isDescriptive ? 'warning' : 'info',
      });
    }

    // Check for missing H1 (add as separate check)
    if (!hasH1) {
      totalChecks++;
      issues.push('No H1 heading found on the page');
      recommendations.push('Add a main H1 heading to identify the page topic');
    } else {
      totalChecks++;
      passedChecks++;
    }

    if (issues.length === 0) {
      recommendations.push('Heading structure is well-organized');
    } else {
      recommendations.push('Ensure proper heading hierarchy (H1 → H2 → H3, etc.)');
      recommendations.push('Use descriptive heading text that summarizes section content');
    }

    return {
      evidence,
      issues,
      recommendations,
      totalChecks,
      passedChecks,
    };
  }

  /**
   * LEGACY: Analyze headings hierarchical structure (fallback method)
   */
  private async analyzeHeadingsHierarchy(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const headingsAnalysis = await page.$$eval('h1, h2, h3, h4, h5, h6', (headings) => {
      return headings.map((heading, index) => {
        const level = parseInt(heading.tagName.charAt(1));
        const text = heading.textContent?.trim() || '';
        const isEmpty = text.length === 0;
        const isDescriptive = text.length > 5 && !text.match(/^(heading|title|section|item)$/i);
        const isGeneric =
          text.match(/^(click here|read more|more|link|button|heading|title)$/i) !== null;

        return {
          index,
          level,
          text,
          selector: `${heading.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          isEmpty,
          isDescriptive,
          isGeneric,
          wordCount: text.split(/\s+/).length,
        };
      });
    });

    const totalChecks = headingsAnalysis.length;
    let passedChecks = 0;

    // Check heading hierarchy
    let previousLevel = 0;
    headingsAnalysis.forEach((heading, index) => {
      let headingPassed = true;

      // Check if heading is empty
      if (heading.isEmpty) {
        headingPassed = false;
        issues.push(`Heading ${index + 1} (${heading.selector}) is empty`);
        evidence.push({
          type: 'code',
          description: `Empty heading ${index + 1}`,
          value: `${heading.selector} - empty heading`,
          selector: heading.selector,
          severity: 'error',
        });
        recommendations.push(`Add descriptive text to heading ${index + 1}`);
      }

      // Check if heading is descriptive
      if (!heading.isDescriptive || heading.isGeneric) {
        headingPassed = false;
        issues.push(`Heading ${index + 1} is not descriptive`);
        evidence.push({
          type: 'code',
          description: `Non-descriptive heading ${index + 1}`,
          value: `${heading.selector} - "${heading.text}" (${heading.wordCount} words)`,
          selector: heading.selector,
          severity: 'warning',
        });
        recommendations.push(`Make heading ${index + 1} more descriptive and specific`);
      }

      // Check heading hierarchy
      if (previousLevel > 0 && heading.level > previousLevel + 1) {
        issues.push(`Heading ${index + 1} skips hierarchy levels`);
        evidence.push({
          type: 'code',
          description: `Heading hierarchy issue at heading ${index + 1}`,
          value: `${heading.selector} - jumps from h${previousLevel} to h${heading.level}`,
          selector: heading.selector,
          severity: 'warning',
        });
        recommendations.push(`Fix heading hierarchy - don't skip levels`);
      }

      if (headingPassed) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Heading ${index + 1} is descriptive and properly structured`,
          value: `${heading.selector} - "${heading.text}"`,
          selector: heading.selector,
          severity: 'info',
        });
      }

      previousLevel = heading.level;
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze form labels quality using FormAccessibilityAnalyzer
   */
  private async analyzeFormLabelsQuality(formAccessibilityReport: {
    forms: Array<{
      fields: Array<{
        selector: string;
        hasLabel: boolean;
        isAccessible: boolean;
        labelText: string;
      }>;
    }>;
  }) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    let totalChecks = 0;
    let passedChecks = 0;

    formAccessibilityReport.forms.forEach((form, formIndex: number) => {
      form.fields.forEach((field, fieldIndex: number) => {
        totalChecks++;

        // Check label quality
        if (field.hasLabel && field.isAccessible && field.labelText && field.labelText.length > 3) {
          // Check if label is descriptive
          const isDescriptive =
            !field.labelText.match(/^(label|field|input|text|enter|type)$/i) &&
            field.labelText.length > 5;

          if (isDescriptive) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: `Form field ${fieldIndex + 1} in form ${formIndex + 1} has descriptive label`,
              value: `${field.selector} - label: "${field.labelText}"`,
              selector: field.selector,
              severity: 'info',
            });
          } else {
            issues.push(`Form field ${fieldIndex + 1} label is not descriptive`);
            evidence.push({
              type: 'code',
              description: `Form field ${fieldIndex + 1} needs more descriptive label`,
              value: `${field.selector} - label: "${field.labelText}"`,
              selector: field.selector,
              severity: 'warning',
            });
            recommendations.push(
              `Make label for field ${fieldIndex + 1} more descriptive and specific`,
            );
          }
        } else {
          issues.push(`Form field ${fieldIndex + 1} lacks proper label`);
          evidence.push({
            type: 'code',
            description: `Form field ${fieldIndex + 1} requires proper label`,
            value: `${field.selector} - hasLabel: ${field.hasLabel}, isAccessible: ${field.isAccessible}`,
            selector: field.selector,
            severity: 'error',
          });
          recommendations.push(`Add descriptive label to form field ${fieldIndex + 1}`);
        }
      });
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze semantic relationships between headings and labels
   */
  private async analyzeSemanticRelationships(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for proper semantic relationships
    const relationshipAnalysis = await page.$$eval('fieldset, section, article', (containers) => {
      return containers.map((container, index) => {
        const hasHeading = container.querySelector('h1, h2, h3, h4, h5, h6, legend') !== null;
        const hasFormFields = container.querySelector('input, select, textarea') !== null;
        const hasProperGrouping =
          container.tagName === 'FIELDSET'
            ? container.querySelector('legend') !== null
            : hasHeading;

        return {
          index,
          tagName: container.tagName.toLowerCase(),
          selector: `${container.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          hasHeading,
          hasFormFields,
          hasProperGrouping,
          needsGrouping: hasFormFields,
        };
      });
    });

    const totalChecks = relationshipAnalysis.filter((item) => item.needsGrouping).length;
    let passedChecks = 0;

    relationshipAnalysis.forEach((container, index) => {
      if (container.needsGrouping) {
        if (container.hasProperGrouping) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: `Container ${index + 1} has proper semantic grouping`,
            value: `${container.selector} - has heading/legend for form fields`,
            selector: container.selector,
            severity: 'info',
          });
        } else {
          issues.push(`Container ${index + 1} lacks proper semantic grouping`);
          evidence.push({
            type: 'code',
            description: `Container ${index + 1} needs proper heading or legend`,
            value: `${container.selector} - ${container.tagName} with form fields but no heading/legend`,
            selector: container.selector,
            severity: 'warning',
          });
          recommendations.push(
            `Add proper heading or legend to container ${index + 1} with form fields`,
          );
        }
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }
}

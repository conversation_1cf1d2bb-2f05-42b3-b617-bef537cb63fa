/**
 * Headless CMS Detector
 * Detects and analyzes Strapi, Contentful, Sanity and other headless CMS platforms
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';
import SmartCache from './smart-cache';
import EnhancedColorAnalyzer from './enhanced-color-analyzer';
import { AdvancedPatternDetector } from './advanced-pattern-detector';

export interface HeadlessCMSDetection {
  cms: string;
  version?: string;
  confidence: number;
  apiEndpoints: string[];
  contentTypes: string[];
  jamstackArchitecture: {
    isJAMStack: boolean;
    staticSiteGenerator?: string;
    buildTool?: string;
    deploymentPlatform?: string;
  };
  accessibility: {
    score: number;
    hasStructuredContent: boolean;
    hasAccessibilityFields: boolean;
    hasImageAltText: boolean;
    hasSemanticMarkup: boolean;
    contentQuality: 'high' | 'medium' | 'low';
    issues: string[];
    recommendations: string[];
  };
}

export interface HeadlessCMSConfig {
  enableStrapiDetection: boolean;
  enableContentfulDetection: boolean;
  enableSanityDetection: boolean;
  enableGhostDetection: boolean;
  enablePrismicDetection: boolean;
  enableDirectusDetection: boolean;
  enableGraphCMSDetection: boolean;
  enableDatoCMSDetection: boolean;
  analyzeJAMStackArchitecture: boolean;
  analyzeContentAccessibility: boolean;
  deepContentAnalysis: boolean;
}

export interface ContentAccessibilityAnalysis {
  contentType: string;
  hasProperHeadings: boolean;
  hasImageAltText: boolean;
  hasDescriptiveLinks: boolean;
  hasStructuredData: boolean;
  hasAccessibilityMetadata: boolean;
  contentQualityScore: number;
  issues: string[];
}

export interface JAMStackAnalysis {
  architecture: 'jamstack' | 'traditional' | 'hybrid';
  staticSiteGenerator?: string;
  buildTool?: string;
  deploymentPlatform?: string;
  features: {
    hasPrerendering: boolean;
    hasStaticGeneration: boolean;
    hasIncrementalRegeneration: boolean;
    hasEdgeFunctions: boolean;
  };
  performance: {
    hasOptimizedImages: boolean;
    hasLazyLoading: boolean;
    hasCDN: boolean;
    hasServiceWorker: boolean;
  };
  accessibility: {
    hasStaticAccessibility: boolean;
    hasRuntimeAccessibility: boolean;
    score: number;
  };
}

export interface HeadlessCMSAnalysis {
  detectedCMS: HeadlessCMSDetection[];
  primaryCMS: HeadlessCMSDetection;
  jamstackAnalysis: JAMStackAnalysis;
  contentAnalysis: ContentAccessibilityAnalysis[];
  overallAccessibilityScore: number;
  contentQualityScore: number;
  recommendations: string[];
  bestPractices: string[];
  modernPatterns: string[];
}

/**
 * Headless CMS detector with JAMstack and accessibility analysis
 */
export class HeadlessCMSDetector {
  private static instance: HeadlessCMSDetector;
  private config: HeadlessCMSConfig;
  private smartCache: SmartCache;
  private colorAnalyzer: EnhancedColorAnalyzer;
  private patternDetector: AdvancedPatternDetector;

  private constructor(config?: Partial<HeadlessCMSConfig>) {
    this.config = {
      enableStrapiDetection: config?.enableStrapiDetection ?? true,
      enableContentfulDetection: config?.enableContentfulDetection ?? true,
      enableSanityDetection: config?.enableSanityDetection ?? true,
      enableGhostDetection: config?.enableGhostDetection ?? true,
      enablePrismicDetection: config?.enablePrismicDetection ?? true,
      enableDirectusDetection: config?.enableDirectusDetection ?? true,
      enableGraphCMSDetection: config?.enableGraphCMSDetection ?? true,
      enableDatoCMSDetection: config?.enableDatoCMSDetection ?? true,
      analyzeJAMStackArchitecture: config?.analyzeJAMStackArchitecture ?? true,
      analyzeContentAccessibility: config?.analyzeContentAccessibility ?? true,
      deepContentAnalysis: config?.deepContentAnalysis ?? true,
    };

    // Initialize utility instances
    this.smartCache = SmartCache.getInstance();
    this.colorAnalyzer = EnhancedColorAnalyzer.getInstance();
    this.patternDetector = AdvancedPatternDetector.getInstance();

    logger.info('📄 Headless CMS Detector initialized', {
      strapi: this.config.enableStrapiDetection,
      contentful: this.config.enableContentfulDetection,
      sanity: this.config.enableSanityDetection,
      jamstack: this.config.analyzeJAMStackArchitecture,
    });
  }

  static getInstance(config?: Partial<HeadlessCMSConfig>): HeadlessCMSDetector {
    if (!HeadlessCMSDetector.instance) {
      HeadlessCMSDetector.instance = new HeadlessCMSDetector(config);
    }
    return HeadlessCMSDetector.instance;
  }

  /**
   * Analyze headless CMS and JAMstack architecture
   */
  async analyzeHeadlessCMS(page: Page): Promise<HeadlessCMSAnalysis> {
    logger.debug('📄 Starting headless CMS analysis');

    const url = page.url();

    // Check cache first
    const cachedResult = await this.smartCache.getSiteAnalysis<HeadlessCMSAnalysis>(
      url,
      'cms-analysis',
    );
    if (cachedResult) {
      logger.debug('📄 Using cached CMS analysis result');
      return cachedResult;
    }

    // Inject CMS detection functions
    await this.injectCMSDetection(page);

    // Detect all CMS platforms
    const detectedCMS = await this.detectCMSPlatforms(page);

    // Determine primary CMS
    const primaryCMS = detectedCMS.reduce(
      (prev, current) => (current.confidence > prev.confidence ? current : prev),
      detectedCMS[0] || this.getDefaultCMS(),
    );

    // Analyze JAMstack architecture
    const jamstackAnalysis = this.config.analyzeJAMStackArchitecture
      ? await this.analyzeJAMStack(page)
      : this.getDefaultJAMStackAnalysis();

    // Analyze content accessibility
    const contentAnalysis = this.config.analyzeContentAccessibility
      ? await this.analyzeContentAccessibility(page, detectedCMS)
      : [];

    // Calculate scores
    const overallAccessibilityScore = this.calculateOverallAccessibilityScore(
      detectedCMS,
      jamstackAnalysis,
      contentAnalysis,
    );
    const contentQualityScore = this.calculateContentQualityScore(contentAnalysis);

    // Generate recommendations and best practices
    const recommendations = this.generateRecommendations(
      detectedCMS,
      jamstackAnalysis,
      contentAnalysis,
    );
    const bestPractices = this.generateBestPractices(detectedCMS, jamstackAnalysis);
    const modernPatterns = this.identifyModernPatterns(detectedCMS, jamstackAnalysis);

    const result: HeadlessCMSAnalysis = {
      detectedCMS,
      primaryCMS,
      jamstackAnalysis,
      contentAnalysis,
      overallAccessibilityScore,
      contentQualityScore,
      recommendations,
      bestPractices,
      modernPatterns,
    };

    // Cache the result
    await this.smartCache.cacheSiteAnalysis(url, 'cms-analysis', result);

    return result;
  }

  /**
   * Inject CMS detection functions with enhanced error handling and validation
   */
  private async injectCMSDetection(page: Page): Promise<void> {
    try {
      await page.evaluateOnNewDocument(() => {
        // Ensure window is available
        if (typeof window === 'undefined') {
          console.error('❌ Window object not available for CMS detection injection');
          return;
        }

        const headlessCMSDetection = {
        /**
         * Detect all CMS platforms with enhanced error handling
         */
        detectAllCMS() {
          try {
            const cmsPlatforms = [
              headlessCMSDetection.detectStrapi(),
              headlessCMSDetection.detectContentful(),
              headlessCMSDetection.detectSanity(),
              headlessCMSDetection.detectGhost(),
              headlessCMSDetection.detectPrismic(),
              headlessCMSDetection.detectDirectus(),
              headlessCMSDetection.detectGraphCMS(),
              headlessCMSDetection.detectDatoCMS(),
            ].filter((cms) => cms && cms.confidence > 0);

            console.log(`✅ CMS detection completed: ${cmsPlatforms.length} platforms found`);
            return cmsPlatforms;
          } catch (error) {
            console.error('❌ CMS detection error:', error);
            return [];
          }
        },

        /**
         * Detect Strapi
         */
        detectStrapi() {
          let confidence = 0;
          const apiEndpoints: string[] = [];
          const contentTypes: string[] = [];

          // Check for Strapi API patterns
          const scripts = Array.from(document.querySelectorAll('script'));
          const hasStrapiFetch = scripts.some(
            (script) => script.textContent?.includes('strapi') || script.src.includes('strapi'),
          );

          if (hasStrapiFetch) {
            confidence += 0.3;
            apiEndpoints.push('/api');
          }

          // Check for Strapi-specific meta tags
          const strapiMeta = document.querySelector('meta[name="strapi"]');
          if (strapiMeta) {
            confidence += 0.4;
          }

          // Check for Strapi admin panel indicators
          if (document.querySelector('[data-strapi]')) {
            confidence += 0.2;
          }

          // Analyze content structure for Strapi patterns
          const contentStructure = headlessCMSDetection.analyzeContentStructure();
          if (contentStructure.hasAPIStructure) {
            confidence += 0.1;
            contentTypes.push(...contentStructure.types);
          }

          const jamstackArchitecture = headlessCMSDetection.analyzeJAMStackArchitecture();
          const accessibility = headlessCMSDetection.analyzeCMSAccessibility('strapi');

          return {
            cms: 'strapi',
            confidence: Math.min(confidence, 1),
            apiEndpoints,
            contentTypes,
            jamstackArchitecture,
            accessibility,
          };
        },

        /**
         * Detect Contentful
         */
        detectContentful() {
          let confidence = 0;
          const apiEndpoints: string[] = [];
          const contentTypes: string[] = [];

          // Check for Contentful API calls
          const scripts = Array.from(document.querySelectorAll('script'));
          const hasContentfulFetch = scripts.some(
            (script) =>
              script.textContent?.includes('contentful') ||
              script.textContent?.includes('cdn.contentful.com'),
          );

          if (hasContentfulFetch) {
            confidence += 0.4;
            apiEndpoints.push('cdn.contentful.com');
          }

          // Check for Contentful-specific attributes
          if (document.querySelector('[data-contentful]')) {
            confidence += 0.3;
          }

          // Check for rich text content patterns (Contentful rich text)
          const richTextElements = document.querySelectorAll('[data-rich-text], .rich-text');
          if (richTextElements.length > 0) {
            confidence += 0.2;
            contentTypes.push('rich-text');
          }

          const jamstackArchitecture = headlessCMSDetection.analyzeJAMStackArchitecture();
          const accessibility = headlessCMSDetection.analyzeCMSAccessibility('contentful');

          return {
            cms: 'contentful',
            confidence: Math.min(confidence, 1),
            apiEndpoints,
            contentTypes,
            jamstackArchitecture,
            accessibility,
          };
        },

        /**
         * Detect Sanity
         */
        detectSanity() {
          let confidence = 0;
          const apiEndpoints: string[] = [];
          const contentTypes: string[] = [];

          // Check for Sanity API patterns
          const scripts = Array.from(document.querySelectorAll('script'));
          const hasSanityFetch = scripts.some(
            (script) =>
              script.textContent?.includes('sanity') ||
              script.textContent?.includes('.sanitycdn.com'),
          );

          if (hasSanityFetch) {
            confidence += 0.4;
            apiEndpoints.push('sanitycdn.com');
          }

          // Check for Sanity-specific attributes
          if (document.querySelector('[data-sanity]')) {
            confidence += 0.3;
          }

          // Check for portable text (Sanity's rich text format)
          if (document.querySelector('[data-portable-text]')) {
            confidence += 0.2;
            contentTypes.push('portable-text');
          }

          const jamstackArchitecture = headlessCMSDetection.analyzeJAMStackArchitecture();
          const accessibility = headlessCMSDetection.analyzeCMSAccessibility('sanity');

          return {
            cms: 'sanity',
            confidence: Math.min(confidence, 1),
            apiEndpoints,
            contentTypes,
            jamstackArchitecture,
            accessibility,
          };
        },

        /**
         * Detect Ghost
         */
        detectGhost() {
          let confidence = 0;
          const apiEndpoints: string[] = [];
          const contentTypes: string[] = [];

          // Check for Ghost API patterns
          const scripts = Array.from(document.querySelectorAll('script'));
          const hasGhostFetch = scripts.some(
            (script) => script.textContent?.includes('ghost') || script.src.includes('ghost'),
          );

          if (hasGhostFetch) {
            confidence += 0.3;
            apiEndpoints.push('/ghost/api');
          }

          // Check for Ghost-specific meta tags
          const ghostMeta = document.querySelector('meta[name="generator"][content*="Ghost"]');
          if (ghostMeta) {
            confidence += 0.4;
          }

          // Check for Ghost content structure
          if (document.querySelector('.post-content, .kg-card')) {
            confidence += 0.2;
            contentTypes.push('post', 'page');
          }

          const jamstackArchitecture = headlessCMSDetection.analyzeJAMStackArchitecture();
          const accessibility = headlessCMSDetection.analyzeCMSAccessibility('ghost');

          return {
            cms: 'ghost',
            confidence: Math.min(confidence, 1),
            apiEndpoints,
            contentTypes,
            jamstackArchitecture,
            accessibility,
          };
        },

        /**
         * Detect Prismic
         */
        detectPrismic() {
          let confidence = 0;
          const apiEndpoints: string[] = [];
          const contentTypes: string[] = [];

          // Check for Prismic API patterns
          const scripts = Array.from(document.querySelectorAll('script'));
          const hasPrismicFetch = scripts.some(
            (script) =>
              script.textContent?.includes('prismic') ||
              script.textContent?.includes('.cdn.prismic.io'),
          );

          if (hasPrismicFetch) {
            confidence += 0.4;
            apiEndpoints.push('cdn.prismic.io');
          }

          // Check for Prismic-specific attributes
          if (document.querySelector('[data-prismic]')) {
            confidence += 0.3;
          }

          const jamstackArchitecture = headlessCMSDetection.analyzeJAMStackArchitecture();
          const accessibility = headlessCMSDetection.analyzeCMSAccessibility('prismic');

          return {
            cms: 'prismic',
            confidence: Math.min(confidence, 1),
            apiEndpoints,
            contentTypes,
            jamstackArchitecture,
            accessibility,
          };
        },

        /**
         * Detect Directus
         */
        detectDirectus() {
          let confidence = 0;
          const apiEndpoints: string[] = [];

          // Check for Directus API patterns
          const scripts = Array.from(document.querySelectorAll('script'));
          const hasDirectusFetch = scripts.some((script) =>
            script.textContent?.includes('directus'),
          );

          if (hasDirectusFetch) {
            confidence += 0.4;
            apiEndpoints.push('/items');
          }

          const jamstackArchitecture = headlessCMSDetection.analyzeJAMStackArchitecture();
          const accessibility = headlessCMSDetection.analyzeCMSAccessibility('directus');

          return {
            cms: 'directus',
            confidence: Math.min(confidence, 1),
            apiEndpoints,
            contentTypes: [],
            jamstackArchitecture,
            accessibility,
          };
        },

        /**
         * Detect GraphCMS (Hygraph)
         */
        detectGraphCMS() {
          let confidence = 0;
          const apiEndpoints: string[] = [];

          // Check for GraphCMS/Hygraph patterns
          const scripts = Array.from(document.querySelectorAll('script'));
          const hasGraphCMSFetch = scripts.some(
            (script) =>
              script.textContent?.includes('graphcms') || script.textContent?.includes('hygraph'),
          );

          if (hasGraphCMSFetch) {
            confidence += 0.4;
            apiEndpoints.push('graphcms.com', 'hygraph.com');
          }

          const jamstackArchitecture = headlessCMSDetection.analyzeJAMStackArchitecture();
          const accessibility = headlessCMSDetection.analyzeCMSAccessibility('graphcms');

          return {
            cms: 'graphcms',
            confidence: Math.min(confidence, 1),
            apiEndpoints,
            contentTypes: [],
            jamstackArchitecture,
            accessibility,
          };
        },

        /**
         * Detect DatoCMS
         */
        detectDatoCMS() {
          let confidence = 0;
          const apiEndpoints: string[] = [];

          // Check for DatoCMS patterns
          const scripts = Array.from(document.querySelectorAll('script'));
          const hasDatoFetch = scripts.some((script) => script.textContent?.includes('datocms'));

          if (hasDatoFetch) {
            confidence += 0.4;
            apiEndpoints.push('graphql.datocms.com');
          }

          const jamstackArchitecture = headlessCMSDetection.analyzeJAMStackArchitecture();
          const accessibility = headlessCMSDetection.analyzeCMSAccessibility('datocms');

          return {
            cms: 'datocms',
            confidence: Math.min(confidence, 1),
            apiEndpoints,
            contentTypes: [],
            jamstackArchitecture,
            accessibility,
          };
        },

        /**
         * Analyze content structure
         */
        analyzeContentStructure() {
          const hasAPIStructure = !!document.querySelector('[data-api], [data-content-type]');
          const types: string[] = [];

          // Look for common content type indicators
          if (document.querySelector('article, .post, .blog-post')) types.push('article');
          if (document.querySelector('.product, [data-product]')) types.push('product');
          if (document.querySelector('.page, [data-page]')) types.push('page');
          if (document.querySelector('.event, [data-event]')) types.push('event');

          return { hasAPIStructure, types };
        },

        /**
         * Analyze JAMstack architecture
         */
        analyzeJAMStackArchitecture() {
          const isJAMStack = headlessCMSDetection.detectJAMStackPatterns();
          const staticSiteGenerator = headlessCMSDetection.detectStaticSiteGenerator();
          const buildTool = headlessCMSDetection.detectBuildTool();
          const deploymentPlatform = headlessCMSDetection.detectDeploymentPlatform();

          return {
            isJAMStack,
            staticSiteGenerator,
            buildTool,
            deploymentPlatform,
          };
        },

        /**
         * Detect JAMstack patterns
         */
        detectJAMStackPatterns() {
          // Check for static site indicators
          const hasPrebuiltMarkup = !document.querySelector('script[src*="runtime"]');
          const hasAPIFetching = !!document
            .querySelector('script')
            ?.textContent?.includes('fetch(');
          const hasStaticAssets = document.querySelectorAll('link[rel="preload"]').length > 0;

          return hasPrebuiltMarkup && (hasAPIFetching || hasStaticAssets);
        },

        /**
         * Detect static site generator
         */
        detectStaticSiteGenerator() {
          if ((window as unknown as Record<string, unknown>).__NEXT_DATA__) return 'Next.js';
          if ((window as unknown as Record<string, unknown>).__NUXT__) return 'Nuxt.js';
          if ((window as unknown as Record<string, unknown>).___gatsby) return 'Gatsby';
          if ((window as unknown as Record<string, unknown>).__sveltekit) return 'SvelteKit';
          if (document.querySelector('[data-astro-cid]')) return 'Astro';
          if (document.querySelector('script[src*="gridsome"]')) return 'Gridsome';
          if (document.querySelector('script[src*="vuepress"]')) return 'VuePress';

          return undefined;
        },

        /**
         * Detect build tool
         */
        detectBuildTool() {
          if (document.querySelector('script[src*="/@vite/"]')) return 'Vite';
          if ((window as unknown as Record<string, unknown>).webpackChunkName) return 'Webpack';
          if (document.querySelector('script[data-rollup]')) return 'Rollup';
          if ((window as unknown as Record<string, unknown>).parcelRequire) return 'Parcel';

          return undefined;
        },

        /**
         * Detect deployment platform
         */
        detectDeploymentPlatform() {
          const hostname = window.location.hostname;

          if (hostname.includes('vercel.app') || hostname.includes('vercel.com')) return 'Vercel';
          if (hostname.includes('netlify.app') || hostname.includes('netlify.com'))
            return 'Netlify';
          if (hostname.includes('surge.sh')) return 'Surge';
          if (hostname.includes('github.io')) return 'GitHub Pages';
          if (hostname.includes('firebase.app')) return 'Firebase Hosting';
          if (hostname.includes('cloudflare.com')) return 'Cloudflare Pages';

          return undefined;
        },

        /**
         * Analyze CMS accessibility
         */
        analyzeCMSAccessibility(cms: string) {
          let score = 50; // Base score
          const issues: string[] = [];
          const recommendations: string[] = [];

          // Check for structured content
          const hasStructuredContent = !!document.querySelector('article, section, main, aside');
          if (hasStructuredContent) score += 15;
          else issues.push('Missing semantic content structure');

          // Check for accessibility fields
          const hasAccessibilityFields = !!document.querySelector(
            '[alt], [aria-label], [aria-describedby]',
          );
          if (hasAccessibilityFields) score += 20;
          else issues.push('Missing accessibility attributes');

          // Check for image alt text
          const images = document.querySelectorAll('img');
          const imagesWithAlt = Array.from(images).filter((img) => img.getAttribute('alt'));
          const hasImageAltText = images.length === 0 || imagesWithAlt.length / images.length > 0.8;
          if (hasImageAltText) score += 15;
          else issues.push('Images missing alt text');

          // Check for semantic markup
          const hasSemanticMarkup = !!document.querySelector('h1, h2, h3, nav, main, article');
          if (hasSemanticMarkup) score += 10;
          else issues.push('Missing semantic HTML elements');

          // Determine content quality
          let contentQuality: 'high' | 'medium' | 'low' = 'medium';
          if (score >= 80) contentQuality = 'high';
          else if (score < 60) contentQuality = 'low';

          // Generate CMS-specific recommendations
          switch (cms) {
            case 'strapi':
              recommendations.push('Use Strapi accessibility plugins');
              recommendations.push('Configure required alt text fields');
              break;
            case 'contentful':
              recommendations.push('Use Contentful validation rules for accessibility');
              recommendations.push('Implement rich text accessibility guidelines');
              break;
            case 'sanity':
              recommendations.push('Use Sanity accessibility validation');
              recommendations.push('Configure portable text accessibility');
              break;
          }

          return {
            score,
            hasStructuredContent,
            hasAccessibilityFields,
            hasImageAltText,
            hasSemanticMarkup,
            contentQuality,
            issues,
            recommendations,
          };
        },
      };

      // Assign to window with validation
      try {
        (window as unknown as Record<string, unknown>).headlessCMSDetection = headlessCMSDetection;
        console.log('✅ CMS detection functions injected successfully');
      } catch (error) {
        console.error('❌ Failed to inject CMS detection functions:', error);
      }
    });

    // Wait for injection to complete with timeout and validation
    try {
      await page.waitForFunction(
        () => {
          const detection = (window as any).headlessCMSDetection;
          return detection && typeof detection.detectAllCMS === 'function';
        },
        { timeout: 5000 }
      );

      logger.debug('✅ CMS detection injection validated successfully');
    } catch (error) {
      logger.error('❌ CMS detection injection validation failed:', { error: error instanceof Error ? error.message : String(error) });
      throw new Error(`CMS detection injection failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  } catch (injectionError) {
    logger.error('❌ CMS detection injection process failed:', { error: injectionError instanceof Error ? injectionError.message : String(injectionError) });
    throw new Error(`CMS detection injection process failed: ${injectionError instanceof Error ? injectionError.message : String(injectionError)}`);
  }
}

  /**
   * Detect CMS platforms with enhanced error handling and fallback
   */
  private async detectCMSPlatforms(page: Page): Promise<HeadlessCMSDetection[]> {
    try {
      const result = await page.evaluate(() => {
        const detection = (window as any).headlessCMSDetection;

        // Validate detection object exists and has required method
        if (!detection) {
          throw new Error('CMS detection object not found on window');
        }

        if (typeof detection.detectAllCMS !== 'function') {
          throw new Error('detectAllCMS method not available');
        }

        return detection.detectAllCMS();
      });

      // Validate result
      if (!Array.isArray(result)) {
        logger.warn('CMS detection returned invalid result, using fallback');
        return [this.getDefaultCMS()];
      }

      logger.debug(`✅ CMS detection successful: ${result.length} platforms detected`);
      return result;
    } catch (error) {
      logger.warn('❌ CMS detection failed, using fallback:', { error: error instanceof Error ? error.message : String(error) });
      return [this.getDefaultCMS()];
    }
  }

  /**
   * Analyze JAMstack architecture
   */
  private async analyzeJAMStack(page: Page): Promise<JAMStackAnalysis> {
    const jamstackData = await page.evaluate(() => {
      const detection = (window as unknown as Record<string, unknown>).headlessCMSDetection;
      const architecture = (
        detection as { analyzeJAMStackArchitecture: () => unknown }
      ).analyzeJAMStackArchitecture();

      // Analyze features
      const features = {
        hasPrerendering: !!document.querySelector('[data-prerendered], [data-ssr]'),
        hasStaticGeneration: !!document.querySelector('[data-ssg], [data-static]'),
        hasIncrementalRegeneration: !!document.querySelector('[data-isr]'),
        hasEdgeFunctions: !!document.querySelector('[data-edge]'),
      };

      // Analyze performance features
      const performance = {
        hasOptimizedImages: !!document.querySelector('img[loading="lazy"], picture'),
        hasLazyLoading: !!document.querySelector('[loading="lazy"], [data-lazy]'),
        hasCDN:
          window.location.hostname.includes('cdn') ||
          !!document.querySelector('link[href*="cdn"], script[src*="cdn"]'),
        hasServiceWorker: 'serviceWorker' in navigator,
      };

      return { architecture, features, performance };
    });

    // Determine architecture type
    let architectureType: JAMStackAnalysis['architecture'] = 'traditional';
    const architecture = jamstackData.architecture as {
      isJAMStack?: boolean;
      staticSiteGenerator?: string;
      buildTool?: string;
      deploymentPlatform?: string;
    };
    if (architecture.isJAMStack) {
      architectureType = 'jamstack';
    } else if (architecture.staticSiteGenerator) {
      architectureType = 'hybrid';
    }

    // Calculate accessibility score
    let accessibilityScore = 50;
    if (jamstackData.features.hasPrerendering) accessibilityScore += 15;
    if (jamstackData.features.hasStaticGeneration) accessibilityScore += 15;
    if (jamstackData.performance.hasOptimizedImages) accessibilityScore += 10;
    if (jamstackData.performance.hasLazyLoading) accessibilityScore += 10;

    return {
      architecture: architectureType,
      staticSiteGenerator: architecture.staticSiteGenerator,
      buildTool: architecture.buildTool,
      deploymentPlatform: architecture.deploymentPlatform,
      features: jamstackData.features,
      performance: jamstackData.performance,
      accessibility: {
        hasStaticAccessibility: jamstackData.features.hasStaticGeneration,
        hasRuntimeAccessibility: !jamstackData.features.hasStaticGeneration,
        score: Math.min(accessibilityScore, 100),
      },
    };
  }

  /**
   * Analyze content accessibility
   */
  private async analyzeContentAccessibility(
    page: Page,
    _cmsPlatforms: HeadlessCMSDetection[],
  ): Promise<ContentAccessibilityAnalysis[]> {
    // First inject content analysis functions
    await page.evaluate(() => {
      const detection = (window as unknown as Record<string, unknown>)
        .headlessCMSDetection as Record<string, unknown>;
      detection.analyzeContentElement = function (element: Element, contentType: string) {
        const detectionMethods = detection as {
          checkProperHeadings: (element: Element) => boolean;
          checkImageAltText: (element: Element) => boolean;
          checkDescriptiveLinks: (element: Element) => boolean;
          checkStructuredData: (element: Element) => boolean;
          checkAccessibilityMetadata: (element: Element) => boolean;
        };

        const analysis = {
          contentType,
          hasProperHeadings: detectionMethods.checkProperHeadings(element),
          hasImageAltText: detectionMethods.checkImageAltText(element),
          hasDescriptiveLinks: detectionMethods.checkDescriptiveLinks(element),
          hasStructuredData: detectionMethods.checkStructuredData(element),
          hasAccessibilityMetadata: detectionMethods.checkAccessibilityMetadata(element),
          contentQualityScore: 0,
          issues: [] as string[],
        };

        // Calculate score
        let score = 0;
        if (analysis.hasProperHeadings) score += 20;
        if (analysis.hasImageAltText) score += 20;
        if (analysis.hasDescriptiveLinks) score += 20;
        if (analysis.hasStructuredData) score += 20;
        if (analysis.hasAccessibilityMetadata) score += 20;

        // Identify issues
        if (!analysis.hasProperHeadings) analysis.issues.push('Missing proper heading structure');
        if (!analysis.hasImageAltText) analysis.issues.push('Images missing alt text');
        if (!analysis.hasDescriptiveLinks) analysis.issues.push('Links lack descriptive text');
        if (!analysis.hasStructuredData) analysis.issues.push('Missing structured data');
        if (!analysis.hasAccessibilityMetadata)
          analysis.issues.push('Missing accessibility metadata');

        analysis.contentQualityScore = score;
        return analysis;
      };

      detection.checkProperHeadings = function (element: Element) {
        const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
        return headings.length > 0;
      };

      detection.checkImageAltText = function (element: Element) {
        const images = element.querySelectorAll('img');
        if (images.length === 0) return true;

        const imagesWithAlt = Array.from(images).filter((img) => img.getAttribute('alt'));
        return imagesWithAlt.length / images.length > 0.8;
      };

      detection.checkDescriptiveLinks = function (element: Element) {
        const links = element.querySelectorAll('a[href]');
        if (links.length === 0) return true;

        const descriptiveLinks = Array.from(links).filter((link) => {
          const text = link.textContent?.trim();
          return (
            text &&
            text.length > 3 &&
            !['click here', 'read more', 'more'].includes(text.toLowerCase())
          );
        });

        return descriptiveLinks.length / links.length > 0.7;
      };

      detection.checkStructuredData = function (element: Element) {
        return !!element.querySelector('[itemscope], script[type="application/ld+json"]');
      };

      detection.checkAccessibilityMetadata = function (element: Element) {
        return !!element.querySelector('[aria-label], [aria-describedby], [role]');
      };
    });

    return await page.evaluate(() => {
      const analyses: ContentAccessibilityAnalysis[] = [];

      // Analyze different content types
      const contentSelectors = {
        article: 'article, .post, .blog-post, [data-content-type="article"]',
        page: '.page, [data-content-type="page"]',
        product: '.product, [data-content-type="product"]',
        event: '.event, [data-content-type="event"]',
      };

      Object.entries(contentSelectors).forEach(([contentType, selector]) => {
        const elements = document.querySelectorAll(selector);

        elements.forEach((element, index) => {
          const detection = (window as unknown as Record<string, unknown>).headlessCMSDetection;
          const analysis = (
            detection as {
              analyzeContentElement: (
                element: Element,
                contentType: string,
              ) => ContentAccessibilityAnalysis;
            }
          ).analyzeContentElement(element, contentType);
          analyses.push({
            ...analysis,
            contentType: `${contentType}-${index}`,
          });
        });
      });

      return analyses;
    });
  }

  /**
   * Calculate overall accessibility score
   */
  private calculateOverallAccessibilityScore(
    cmsPlatforms: HeadlessCMSDetection[],
    jamstackAnalysis: JAMStackAnalysis,
    contentAnalysis: ContentAccessibilityAnalysis[],
  ): number {
    let totalScore = 0;
    let totalWeight = 0;

    // CMS accessibility scores
    cmsPlatforms.forEach((cms) => {
      totalScore += cms.accessibility.score * cms.confidence;
      totalWeight += cms.confidence;
    });

    // JAMstack accessibility score
    totalScore += jamstackAnalysis.accessibility.score * 0.3;
    totalWeight += 0.3;

    // Content accessibility scores
    if (contentAnalysis.length > 0) {
      const contentScore =
        contentAnalysis.reduce((sum, content) => sum + content.contentQualityScore, 0) /
        contentAnalysis.length;
      totalScore += contentScore * 0.4;
      totalWeight += 0.4;
    }

    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 50;
  }

  /**
   * Calculate content quality score
   */
  private calculateContentQualityScore(contentAnalysis: ContentAccessibilityAnalysis[]): number {
    if (contentAnalysis.length === 0) return 50;

    return Math.round(
      contentAnalysis.reduce((sum, content) => sum + content.contentQualityScore, 0) /
        contentAnalysis.length,
    );
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(
    cmsPlatforms: HeadlessCMSDetection[],
    jamstackAnalysis: JAMStackAnalysis,
    contentAnalysis: ContentAccessibilityAnalysis[],
  ): string[] {
    const recommendations = new Set<string>();

    // CMS-specific recommendations
    cmsPlatforms.forEach((cms) => {
      cms.accessibility.recommendations.forEach((rec) => recommendations.add(rec));

      if (cms.accessibility.score < 70) {
        recommendations.add(`Improve ${cms.cms} content accessibility configuration`);
      }
    });

    // JAMstack recommendations
    if (jamstackAnalysis.architecture === 'traditional') {
      recommendations.add(
        'Consider migrating to JAMstack architecture for better performance and accessibility',
      );
    }

    if (!jamstackAnalysis.features.hasPrerendering) {
      recommendations.add('Implement prerendering for better accessibility and SEO');
    }

    if (!jamstackAnalysis.performance.hasOptimizedImages) {
      recommendations.add('Optimize images with proper sizing and lazy loading');
    }

    // Content recommendations
    const contentIssues = contentAnalysis.flatMap((content) => content.issues);
    const uniqueIssues = Array.from(new Set(contentIssues));
    uniqueIssues.forEach((issue) => recommendations.add(`Address content issue: ${issue}`));

    return Array.from(recommendations).slice(0, 10);
  }

  /**
   * Generate best practices
   */
  private generateBestPractices(
    cmsPlatforms: HeadlessCMSDetection[],
    jamstackAnalysis: JAMStackAnalysis,
  ): string[] {
    const bestPractices = new Set<string>();

    // CMS-specific best practices
    cmsPlatforms.forEach((cms) => {
      switch (cms.cms) {
        case 'strapi':
          bestPractices.add('Configure Strapi content types with accessibility validation');
          bestPractices.add('Use Strapi plugins for SEO and accessibility');
          break;
        case 'contentful':
          bestPractices.add('Implement Contentful field validation for accessibility');
          bestPractices.add('Use Contentful rich text with accessibility guidelines');
          break;
        case 'sanity':
          bestPractices.add('Configure Sanity schemas with accessibility requirements');
          bestPractices.add('Use Sanity portable text accessibility features');
          break;
        case 'ghost':
          bestPractices.add('Use Ghost accessibility features and plugins');
          bestPractices.add('Configure Ghost themes with accessibility in mind');
          break;
      }
    });

    // JAMstack best practices
    if (jamstackAnalysis.architecture === 'jamstack') {
      bestPractices.add('Leverage static generation for consistent accessibility');
      bestPractices.add('Implement build-time accessibility checks');
      bestPractices.add('Use CDN for optimal performance and accessibility');
    }

    // General best practices
    bestPractices.add('Implement content accessibility guidelines in CMS');
    bestPractices.add('Use structured content with semantic markup');
    bestPractices.add('Validate accessibility during content creation');
    bestPractices.add('Test with assistive technologies');

    return Array.from(bestPractices).slice(0, 8);
  }

  /**
   * Identify modern patterns
   */
  private identifyModernPatterns(
    cmsPlatforms: HeadlessCMSDetection[],
    jamstackAnalysis: JAMStackAnalysis,
  ): string[] {
    const patterns = new Set<string>();

    // Architecture patterns
    if (jamstackAnalysis.architecture === 'jamstack') {
      patterns.add('JAMstack architecture implementation');
    }

    if (jamstackAnalysis.staticSiteGenerator) {
      patterns.add(`${jamstackAnalysis.staticSiteGenerator} static site generation`);
    }

    if (jamstackAnalysis.features.hasIncrementalRegeneration) {
      patterns.add('Incremental static regeneration');
    }

    if (jamstackAnalysis.features.hasEdgeFunctions) {
      patterns.add('Edge functions implementation');
    }

    // CMS patterns
    const hasHeadlessCMS = cmsPlatforms.some((cms) => cms.confidence > 0.5);
    if (hasHeadlessCMS) {
      patterns.add('Headless CMS architecture');
    }

    const hasMultipleCMS = cmsPlatforms.filter((cms) => cms.confidence > 0.3).length > 1;
    if (hasMultipleCMS) {
      patterns.add('Multi-CMS implementation');
    }

    // Performance patterns
    if (jamstackAnalysis.performance.hasOptimizedImages) {
      patterns.add('Optimized image delivery');
    }

    if (jamstackAnalysis.performance.hasCDN) {
      patterns.add('CDN-based content delivery');
    }

    return Array.from(patterns).slice(0, 6);
  }

  /**
   * Helper methods
   */
  private getDefaultCMS(): HeadlessCMSDetection {
    return {
      cms: 'none',
      confidence: 0,
      apiEndpoints: [],
      contentTypes: [],
      jamstackArchitecture: {
        isJAMStack: false,
      },
      accessibility: {
        score: 50,
        hasStructuredContent: false,
        hasAccessibilityFields: false,
        hasImageAltText: false,
        hasSemanticMarkup: false,
        contentQuality: 'medium',
        issues: [],
        recommendations: [],
      },
    };
  }

  private getDefaultJAMStackAnalysis(): JAMStackAnalysis {
    return {
      architecture: 'traditional',
      features: {
        hasPrerendering: false,
        hasStaticGeneration: false,
        hasIncrementalRegeneration: false,
        hasEdgeFunctions: false,
      },
      performance: {
        hasOptimizedImages: false,
        hasLazyLoading: false,
        hasCDN: false,
        hasServiceWorker: false,
      },
      accessibility: {
        hasStaticAccessibility: false,
        hasRuntimeAccessibility: true,
        score: 50,
      },
    };
  }
}

export default HeadlessCMSDetector;

{"data": {"ruleId": "WCAG-025", "ruleName": "Landmarks", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "info", "description": "Advanced layout analysis for landmark structure validation", "element": "landmark-elements", "value": "{\"overallScore\":80,\"criticalIssues\":[],\"recommendations\":[\"Increase target sizes to meet WCAG minimum requirements\"],\"performanceMetrics\":{\"analysisTime\":1428,\"elementsAnalyzed\":1000,\"breakpointsTested\":5}}", "severity": "info", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "recommendations", "Increase", "target", "sizes", "to", "meet", "WCAG", "minimum", "requirements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "breakpointsTested"], "metadata": {"scanDuration": 1537, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-025", "ruleName": "Landmarks", "timestamp": "2025-07-11T10:49:07.085Z"}}}, {"type": "warning", "description": "Content elements outside landmark regions", "value": "208 elements not in landmarks", "selector": "p, h1, h2, h3, h4, h5, h6, article, section, div", "elementCount": 10, "affectedSelectors": ["p", "h1", "h2", "h3", "h4", "h5", "h6", "article", "section", "div", "elements", "not", "in", "landmarks"], "severity": "warning", "metadata": {"scanDuration": 1537, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-025", "ruleName": "Landmarks", "timestamp": "2025-07-11T10:49:07.085Z"}}}], "recommendations": ["Move content into appropriate landmark regions", "Use semantic HTML5 landmark elements (main, nav, header, footer, aside)", "Ensure all content is contained within appropriate landmarks", "Provide accessible names for multiple landmarks of the same type"], "executionTime": 1453, "originalScore": 75}, "timestamp": 1752230947085, "hash": "0a1bada8c793a37c9add4e6aee9a1542", "accessCount": 1, "lastAccessed": 1752230947085, "size": 1931}
{"data": {"ruleId": "WCAG-062", "ruleName": "Reading Level", "category": "understandable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0305, "automated": true, "evidence": [{"type": "text", "description": "Reading level analysis", "value": "Overall reading level: College (13th-16th grade) (Grade 15.8)", "elementCount": 1, "affectedSelectors": ["Overall", "reading", "level", "College", "th-16th", "grade", "Grade"], "severity": "warning", "metadata": {"scanDuration": 59, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T11:39:09.310Z"}}}, {"type": "text", "description": "Complex text block (Grade 17.6)", "value": "\"HostedScanUse CasesScannersPricingResourcesBlog Log inSign upTrusted vulnerability scanswithout the hassleScan your websites, servers, networks, and APIs.View dashboards, get threat alerts, and genera...\"", "selector": "#__next", "severity": "error", "metadata": {"scanDuration": 59, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T11:39:09.310Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "HostedScanUse", "CasesScannersPricingResourcesBlog", "Log", "inSign", "upTrusted", "vulnerability", "scanswithout", "the", "hassleScan", "your", "websites", "servers", "networks", "and", "APIs.View", "dashboards", "get", "threat", "alerts", "genera"]}, {"type": "text", "description": "Complex text block (Grade 17.6)", "value": "\"HostedScanUse CasesScannersPricingResourcesBlog Log inSign upTrusted vulnerability scanswithout the hassleScan your websites, servers, networks, and APIs.View dashboards, get threat alerts, and genera...\"", "selector": ".relative", "severity": "error", "metadata": {"scanDuration": 59, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T11:39:09.310Z"}}, "elementCount": 1, "affectedSelectors": [".relative", "HostedScanUse", "CasesScannersPricingResourcesBlog", "Log", "inSign", "upTrusted", "vulnerability", "scanswithout", "the", "hassleScan", "your", "websites", "servers", "networks", "and", "APIs.View", "dashboards", "get", "threat", "alerts", "genera"]}, {"type": "text", "description": "Complex text block (Grade 13.5)", "value": "\"Trusted vulnerability scanswithout the hassleScan your websites, servers, networks, and APIs.View dashboards, get threat alerts, and generate audit-ready reports.Run a free scanScan nowOUR CUSTOMERSPr...\"", "selector": ".flex", "severity": "warning", "metadata": {"scanDuration": 59, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T11:39:09.310Z"}}, "elementCount": 1, "affectedSelectors": [".flex", "Trusted", "vulnerability", "scanswithout", "the", "hassleScan", "your", "websites", "servers", "networks", "and", "APIs.View", "dashboards", "get", "threat", "alerts", "generate", "audit-ready", "reports.Run", "a", "free", "scanScan", "nowOUR", "CUSTOMERSPr"]}, {"type": "text", "description": "Complex text block (Grade 15.5)", "value": "\"Trusted vulnerability scanswithout the hassleScan your websites, servers, networks, and APIs.View dashboards, get threat alerts, and generate audit-ready reports.Run a free scanScan nowOUR CUSTOMERSPr...\"", "selector": ".flex", "severity": "warning", "metadata": {"scanDuration": 59, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T11:39:09.310Z"}}, "elementCount": 1, "affectedSelectors": [".flex", "Trusted", "vulnerability", "scanswithout", "the", "hassleScan", "your", "websites", "servers", "networks", "and", "APIs.View", "dashboards", "get", "threat", "alerts", "generate", "audit-ready", "reports.Run", "a", "free", "scanScan", "nowOUR", "CUSTOMERSPr"]}, {"type": "text", "description": "Complex text block (Grade 12.5)", "value": "\"Trusted vulnerability scanswithout the hassleScan your websites, servers, networks, and APIs.View dashboards, get threat alerts, and generate audit-ready reports.Run a free scanScan now\"", "selector": ".w-full", "severity": "warning", "metadata": {"scanDuration": 59, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T11:39:09.310Z"}}, "elementCount": 1, "affectedSelectors": [".w-full", "Trusted", "vulnerability", "scanswithout", "the", "hassleScan", "your", "websites", "servers", "networks", "and", "APIs.View", "dashboards", "get", "threat", "alerts", "generate", "audit-ready", "reports.Run", "a", "free", "scanScan", "now"]}], "recommendations": ["Simplify sentence structure and use shorter sentences", "Replace complex words with simpler alternatives when possible", "Consider providing a summary or simplified version of complex content", "Use bullet points and lists to break up complex information", "Test content with actual users to ensure comprehension", "Use plain language principles for better accessibility", "Consider your target audience's reading level and education background"], "executionTime": 17, "originalScore": 60}, "timestamp": 1752233949310, "hash": "8c93f11bc216d3f0cbec6eb34e6ce0ae", "accessCount": 1, "lastAccessed": 1752233949310, "size": 5370}
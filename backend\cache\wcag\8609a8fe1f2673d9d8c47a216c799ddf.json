{"data": [{"type": "measurement", "description": "Interactive targets below enhanced size requirements", "value": "Found 57 targets that don't meet AAA enhanced size requirements (44x44px)", "elementCount": 1, "affectedSelectors": ["Found", "targets", "that", "don", "t", "meet", "AAA", "enhanced", "size", "requirements", "x44px"], "severity": "error", "metadata": {"scanDuration": 761, "elementsAnalyzed": 12, "checkSpecificData": {"automationRate": 0.9, "checkType": "enhanced-target-size-analysis", "enhancedSizeRequirements": true, "aaaLevelValidation": true, "evidenceIndex": 1, "ruleId": "WCAG-055", "ruleName": "Target Size Enhanced", "timestamp": "2025-07-11T11:39:05.397Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Small target: 135x28px (below 44x44px enhanced)", "value": "<a>HostedScan</a>", "selector": ".flex", "severity": "error", "metadata": {"scanDuration": 761, "elementsAnalyzed": 12, "checkSpecificData": {"automationRate": 0.9, "checkType": "enhanced-target-size-analysis", "enhancedSizeRequirements": true, "aaaLevelValidation": true, "evidenceIndex": 2, "ruleId": "WCAG-055", "ruleName": "Target Size Enhanced", "timestamp": "2025-07-11T11:39:05.397Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".flex", "a", "HostedScan"]}, {"type": "measurement", "description": "Small target: 91x41px (below 44x44px enhanced)", "value": "<a>Log in</a>", "selector": ".rounded-md", "severity": "error", "metadata": {"scanDuration": 761, "elementsAnalyzed": 12, "checkSpecificData": {"automationRate": 0.9, "checkType": "enhanced-target-size-analysis", "enhancedSizeRequirements": true, "aaaLevelValidation": true, "evidenceIndex": 3, "ruleId": "WCAG-055", "ruleName": "Target Size Enhanced", "timestamp": "2025-07-11T11:39:05.397Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".rounded-md", "a", "Log", "in"]}, {"type": "measurement", "description": "Small target: 103x41px (below 44x44px enhanced)", "value": "<a>Sign up</a>", "selector": ".rounded-md", "severity": "error", "metadata": {"scanDuration": 761, "elementsAnalyzed": 12, "checkSpecificData": {"automationRate": 0.9, "checkType": "enhanced-target-size-analysis", "enhancedSizeRequirements": true, "aaaLevelValidation": true, "evidenceIndex": 4, "ruleId": "WCAG-055", "ruleName": "Target Size Enhanced", "timestamp": "2025-07-11T11:39:05.397Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".rounded-md", "a", "Sign", "up"]}, {"type": "measurement", "description": "Small target: 364x28px (below 44x44px enhanced)", "value": "<a>Learn more</a>", "selector": ".flex", "severity": "error", "metadata": {"scanDuration": 761, "elementsAnalyzed": 12, "checkSpecificData": {"automationRate": 0.9, "checkType": "enhanced-target-size-analysis", "enhancedSizeRequirements": true, "aaaLevelValidation": true, "evidenceIndex": 5, "ruleId": "WCAG-055", "ruleName": "Target Size Enhanced", "timestamp": "2025-07-11T11:39:05.397Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".flex", "a", "Learn", "more"]}, {"type": "measurement", "description": "Small target: 488x28px (below 44x44px enhanced)", "value": "<a>Get started</a>", "selector": ".flex", "severity": "error", "metadata": {"scanDuration": 761, "elementsAnalyzed": 12, "checkSpecificData": {"automationRate": 0.9, "checkType": "enhanced-target-size-analysis", "enhancedSizeRequirements": true, "aaaLevelValidation": true, "evidenceIndex": 6, "ruleId": "WCAG-055", "ruleName": "Target Size Enhanced", "timestamp": "2025-07-11T11:39:05.397Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".flex", "a", "Get", "started"]}, {"type": "measurement", "description": "Small target: 364x28px (below 44x44px enhanced)", "value": "<a>Learn more</a>", "selector": ".flex", "severity": "error", "metadata": {"scanDuration": 761, "elementsAnalyzed": 12, "checkSpecificData": {"automationRate": 0.9, "checkType": "enhanced-target-size-analysis", "enhancedSizeRequirements": true, "aaaLevelValidation": true, "evidenceIndex": 7, "ruleId": "WCAG-055", "ruleName": "Target Size Enhanced", "timestamp": "2025-07-11T11:39:05.397Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".flex", "a", "Learn", "more"]}, {"type": "measurement", "description": "Small target: 488x28px (below 44x44px enhanced)", "value": "<a>Get started</a>", "selector": ".flex", "severity": "error", "metadata": {"scanDuration": 761, "elementsAnalyzed": 12, "checkSpecificData": {"automationRate": 0.9, "checkType": "enhanced-target-size-analysis", "enhancedSizeRequirements": true, "aaaLevelValidation": true, "evidenceIndex": 8, "ruleId": "WCAG-055", "ruleName": "Target Size Enhanced", "timestamp": "2025-07-11T11:39:05.397Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".flex", "a", "Get", "started"]}, {"type": "measurement", "description": "Small target: 118x28px (below 44x44px enhanced)", "value": "<a>Get started</a>", "selector": ".mt-10", "severity": "error", "metadata": {"scanDuration": 761, "elementsAnalyzed": 12, "checkSpecificData": {"automationRate": 0.9, "checkType": "enhanced-target-size-analysis", "enhancedSizeRequirements": true, "aaaLevelValidation": true, "evidenceIndex": 9, "ruleId": "WCAG-055", "ruleName": "Target Size Enhanced", "timestamp": "2025-07-11T11:39:05.397Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".mt-10", "a", "Get", "started"]}, {"type": "measurement", "description": "Small target: 179x28px (below 44x44px enhanced)", "value": "<a>Read our API Docs</a>", "selector": ".mt-10", "severity": "error", "metadata": {"scanDuration": 761, "elementsAnalyzed": 12, "checkSpecificData": {"automationRate": 0.9, "checkType": "enhanced-target-size-analysis", "enhancedSizeRequirements": true, "aaaLevelValidation": true, "evidenceIndex": 10, "ruleId": "WCAG-055", "ruleName": "Target Size Enhanced", "timestamp": "2025-07-11T11:39:05.397Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".mt-10", "a", "Read", "our", "API", "Docs"]}, {"type": "measurement", "description": "Small target: 273x24px (below 44x44px enhanced)", "value": "<a>Learn about OpenVAS</a>", "selector": ".mt-4", "severity": "error", "metadata": {"scanDuration": 761, "elementsAnalyzed": 12, "checkSpecificData": {"automationRate": 0.9, "checkType": "enhanced-target-size-analysis", "enhancedSizeRequirements": true, "aaaLevelValidation": true, "evidenceIndex": 11, "ruleId": "WCAG-055", "ruleName": "Target Size Enhanced", "timestamp": "2025-07-11T11:39:05.397Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".mt-4", "a", "Learn", "about", "OpenVAS"]}], "timestamp": 1752233945398, "hash": "1e057dab365ec4d7eecd57a5394755f8", "accessCount": 1, "lastAccessed": 1752233945398, "size": 7963}
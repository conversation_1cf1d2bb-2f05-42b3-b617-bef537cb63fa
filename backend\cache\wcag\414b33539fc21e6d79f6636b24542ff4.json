{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "metadata": {"scanDuration": 305, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.8, "checkType": "language-detection-analysis", "languagePatternAnalysis": true, "langAttributeValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-038", "ruleName": "Language of Parts", "timestamp": "2025-07-11T18:04:05.454Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752257045454, "hash": "450a7e28f93a6adc3c246df0d05695d1", "accessCount": 1, "lastAccessed": 1752257045454, "size": 831, "metadata": {"originalKey": "WCAG-038:WCAG-038:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-038_wcag-038_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752257045455, "version": "1.1", "keyHash": "996594f22f194febe55bbfd4c555a549"}}
{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame 'A362B1B11B79F8E7E1503ABC8B8AFA25'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A362B1B11B79F8E7E1503ABC8B8AFA25"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.8, "checkType": "language-detection-analysis", "languagePatternAnalysis": true, "langAttributeValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-038", "ruleName": "Language of Parts", "timestamp": "2025-07-11T18:50:03.497Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259803497, "hash": "5000e4eb93928b6069736b667e798ab0", "accessCount": 1, "lastAccessed": 1752259803497, "size": 830, "metadata": {"originalKey": "WCAG-038:WCAG-038:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-038_wcag-038_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752259803498, "version": "1.1", "keyHash": "996594f22f194febe55bbfd4c555a549"}}
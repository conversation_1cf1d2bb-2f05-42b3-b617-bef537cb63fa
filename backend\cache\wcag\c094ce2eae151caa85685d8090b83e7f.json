{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Cannot read properties of undefined (reading 'getElementSelector')\npptr:evaluate;FormAccessibilityAnalyzer.getForms%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cutils%5Cform-accessibility-analyzer.ts%3A434%3A27):7:44", "severity": "error", "elementCount": 1, "affectedSelectors": ["Cannot", "read", "properties", "of", "undefined", "reading", "getElementSelector", "pptr", "evaluate", "FormAccessibilityAnalyzer.getForms", "D", "A", "CWeb", "projects", "CCom<PERSON>ly", "Checker", "Cbackend", "Csrc", "Ccompliance", "Cwcag", "Cutils", "Cform-accessibility-analyzer.ts", "A434", "A27"], "metadata": {"scanDuration": 9, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.85, "checkType": "content-structure-analysis", "headingAnalysis": true, "labelAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-036", "ruleName": "Headings and Labels", "timestamp": "2025-07-11T11:39:49.111Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752233989111, "hash": "3af7bce1fc85f39fc8325406301a06c5", "accessCount": 1, "lastAccessed": 1752233989111, "size": 1199}
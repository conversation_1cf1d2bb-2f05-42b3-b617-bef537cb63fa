{"data": [{"type": "code", "description": "Moving content without user controls: JavaScript-based animation detected", "value": "Moving content detected", "selector": "script", "severity": "error", "elementCount": 1, "affectedSelectors": ["script", "Moving", "content", "detected"], "metadata": {"scanDuration": 42, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-analysis", "mediaElementsAnalyzed": true, "animationDetection": true, "accessibilityPatterns": true, "multimediaAccessibilityTesting": true, "evidenceIndex": 0, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T18:46:03.814Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259563814, "hash": "1b1ec350a9a34ce6c9a7b0e088b48e0d", "accessCount": 1, "lastAccessed": 1752259563814, "size": 794, "metadata": {"originalKey": "WCAG-045:WCAG-045:Y29kZTpNb3ZpbmcgY29udGVudCB3aXRo", "normalizedKey": "wcag-045_wcag-045_y29kztpnb3zpbmcgy29udgvudcb3axro", "savedAt": 1752259563815, "version": "1.1", "keyHash": "fa7e48035e360ff3e1669d42cefa942e"}}
{"data": {"ruleId": "WCAG-054", "ruleName": "Pointer Cancellation", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "passed", "score": 100, "maxScore": 100, "weight": 0.0458, "automated": true, "evidence": [], "recommendations": ["Use up events (click, mouseup, pointerup) for triggering actions", "Avoid triggering actions on down events unless essential", "Provide abort or undo mechanisms for essential down events", "Allow users to cancel actions by moving pointer away", "Test pointer cancellation with various input methods"], "executionTime": 4, "originalScore": 100}, "timestamp": 1752233940543, "hash": "daa0d6f154ae6df283fe2a441247df3e", "accessCount": 1, "lastAccessed": 1752233940543, "size": 585}
/**
 * WCAG Compliance Type Definitions
 * Strict TypeScript interfaces - NO any[] types allowed
 * Following established HIPAA/GDPR patterns with Zod validation
 */

// import { z } from 'zod';

// Core WCAG Rule Identifiers - Following GDPR pattern
export type WcagRuleId =
  | 'WCAG-001'
  | 'WCAG-002'
  | 'WCAG-003'
  | 'WCAG-004'
  | 'WCAG-005'
  | 'WCAG-006'
  | 'WCAG-007'
  | 'WCAG-008'
  | 'WCAG-009'
  | 'WCAG-010'
  | 'WCAG-011'
  | 'WCAG-012'
  | 'WCAG-013'
  | 'WCAG-014'
  | 'WCAG-015'
  | 'WCAG-016'
  | 'WCAG-017'
  | 'WCAG-018'
  | 'WCAG-019'
  | 'WCAG-020'
  | 'WCAG-021';

// Core WCAG Types
export type WcagVersion = '2.1' | '2.2' | '3.0';
export type WcagLevel = 'A' | 'AA' | 'AAA';
export type WcagCategory = 'perceivable' | 'operable' | 'understandable' | 'robust';
export type ScanStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
// AUTOMATED ONLY - No manual_review status (separate tracking)
export type AutomatedCheckStatus = 'passed' | 'failed' | 'not_applicable';
export type ManualReviewStatus = 'pending' | 'in_progress' | 'completed' | 'skipped';
export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';

// Scan Configuration
export interface WcagScanConfig {
  targetUrl: string;
  scanOptions?: WcagScanOptions;
  userId: string;
  requestId: string;
}

export interface WcagScanOptions {
  enableContrastAnalysis?: boolean;
  enableKeyboardTesting?: boolean;
  enableFocusAnalysis?: boolean;
  enableSemanticValidation?: boolean;
  enableManualReview?: boolean;
  wcagVersion?: WcagVersion | 'all';
  level?: WcagLevel;
  maxPages?: number;
  timeout?: number;

  // ✅ ENHANCED FEATURES: Backward-compatible extensions
  includeElementCounts?: boolean;
  generateFixExamples?: boolean;
  enablePerformanceMetrics?: boolean;
  enhancedReporting?: boolean;
  cacheStrategy?: 'memory' | 'redis' | 'none';
}

// Scan Results
export interface WcagScanResult {
  scanId: string;
  targetUrl: string;
  status: ScanStatus;
  overallScore: number;
  levelAchieved: WcagLevel | 'FAIL';
  riskLevel: RiskLevel;
  summary: WcagScanSummary;
  checks: WcagCheckResult[];
  recommendations: WcagRecommendation[];
  metadata: WcagScanMetadata;
  ruleResults?: WcagRuleResult[]; // Additional property for database compatibility
}

// SEPARATED: Automated vs Manual tracking
export interface WcagScanSummary {
  totalAutomatedChecks: number;
  passedAutomatedChecks: number;
  failedAutomatedChecks: number;
  automatedScore: number; // SINGLE automated score only
  categoryScores: {
    perceivable: number;
    operable: number;
    understandable: number;
    robust: number;
  };
  versionScores: {
    wcag21: number;
    wcag22: number;
    wcag30: number;
  };
  automationRate: number;
  manualReviewItems: number; // Separate manual tracking
  // Additional properties for database compatibility
  overallScore?: number;
  totalRules?: number;
  passedRules?: number;
  failedRules?: number;
  riskLevel?: RiskLevel;
  scanDuration?: number;
}

// Manual Review Item Structure
export interface WcagManualReviewItem {
  selector: string;
  description: string;
  automatedFindings: string;
  reviewRequired: string;
  priority: 'high' | 'medium' | 'low';
  estimatedTime: number; // minutes
  type?: string; // Additional type field for categorization
  element?: string; // Element information
  context?: string; // Additional context field for compatibility
}

// Alias for compatibility with manual review template
export type ManualReviewItem = WcagManualReviewItem;

// Browser Evaluation Types - For page.evaluate() contexts
export interface BrowserEvaluationResult {
  totalChecks: number;
  passedChecks: number;
  evidence: WcagEvidence[];
  issues: string[];
  recommendations: string[];
  manualReviewItems: ManualReviewItem[];
}

// Element Analysis Types for browser context
export interface ElementAnalysisData {
  type: string;
  selector: string;
  tagName: string;
  id?: string;
  className?: string;
  textContent?: string;
  ariaLabel?: string;
  ariaLabelledby?: string;
  ariaDescribedby?: string;
  role?: string;
  tabIndex?: number;
  isVisible: boolean;
  hasTextContent: boolean;
  context?: string;
  [key: string]: unknown; // For additional properties specific to element types
}

// Individual Check Results
export interface WcagCheckResult {
  ruleId: string;
  ruleName: string;
  category: WcagCategory;
  wcagVersion: WcagVersion;
  successCriterion: string;
  level: WcagLevel;
  status: AutomatedCheckStatus;
  score: number;
  maxScore: number;
  weight: number;
  automated: boolean;
  evidence: WcagEvidence[];
  recommendations: string[];
  executionTime: number;
  errorMessage?: string;
  originalScore?: number; // Store original partial score for analysis (before binary conversion)
  manualReviewItems?: WcagManualReviewItem[]; // Optional manual review items for tracking
  thresholdApplied?: number; // ✅ Threshold percentage used for scoring (e.g., 75)
  scoringDetails?: string; // ✅ Full scoring explanation and details
}

// Alias for compatibility with database operations
export type WcagRuleResult = WcagCheckResult;

export interface WcagEvidence {
  type: 'text' | 'image' | 'code' | 'measurement' | 'interaction' | 'info' | 'warning' | 'error';
  description: string;
  value: string;
  selector?: string;
  screenshot?: string;
  severity?: 'info' | 'warning' | 'error' | 'critical'; // Make severity optional
  message?: string; // Additional message field for evidence
  element?: string; // Additional element field for compatibility
  details?: string; // Additional details field for compatibility
}

export interface WcagRecommendation {
  ruleId: string;
  priority: 'high' | 'medium' | 'low';
  category: WcagCategory;
  title: string;
  description: string;
  implementation: string;
  resources: string[];
}

// Specialized Analysis Types
export interface ContrastAnalysisResult {
  elementSelector: string;
  elementType: string;
  foregroundColor: string;
  backgroundColor: string;
  contrastRatio: number;
  isLargeText: boolean;
  levelAAPass: boolean;
  levelAAAPass: boolean;
  contextDescription: string;
  recommendation: string;
}

export interface FocusAnalysisResult {
  elementSelector: string;
  elementType: string;
  isFocusable: boolean;
  tabIndex: number;
  hasVisibleFocus: boolean;
  focusIndicatorWidth: number;
  focusIndicatorColor: string;
  focusContrastRatio: number;
  isObscured: boolean;
  keyboardAccessible: boolean;
  issues: string[];
}

export interface KeyboardAnalysisResult {
  elementSelector: string;
  elementType: string;
  isReachable: boolean;
  isOperable: boolean;
  supportedKeys: string[];
  hasKeyboardTrap: boolean;
  focusOrderPosition: number;
  isLogicalOrder: boolean;
  issues: string[];
  recommendations: string[];
}

// Manual Review Types
export interface WcagManualReview {
  id: string;
  scanId: string;
  ruleId: string;
  elementSelector?: string;
  description: string;
  automatedFindings?: string;
  reviewRequired: string;
  priority: 'high' | 'medium' | 'low';
  estimatedTime: number; // minutes
  reviewStatus: ManualReviewStatus;
  reviewerNotes?: string;
  createdAt: Date;
  reviewedAt?: Date;
}

// Database Models
export interface WcagScanModel {
  id: string;
  user_id: string;
  target_url: string;
  scan_status: ScanStatus;
  scan_timestamp: Date;
  completion_timestamp?: Date;
  scan_duration?: number;
  overall_score?: number;
  level_achieved?: WcagLevel | 'FAIL';
  risk_level?: RiskLevel;
  perceivable_score?: number;
  operable_score?: number;
  understandable_score?: number;
  robust_score?: number;
  wcag21_score?: number;
  wcag22_score?: number;
  wcag30_score?: number;
  total_automated_checks: number;
  passed_automated_checks: number;
  failed_automated_checks: number;
  manual_review_items: number;
  scan_options?: WcagScanOptions;
  error_message?: string;
  created_at: Date;
  updated_at: Date;
}

export interface WcagAutomatedResultModel {
  id: string;
  scan_id: string;
  rule_id: string;
  rule_name: string;
  category: WcagCategory;
  wcag_version: WcagVersion;
  success_criterion: string;
  level: WcagLevel;
  status: AutomatedCheckStatus;
  score: number;
  max_score: number;
  weight: number;
  evidence: WcagEvidence[];
  recommendations: string[];
  execution_time: number;
  error_message?: string;
  created_at: Date;
}

// Metadata and Configuration
export interface WcagScanMetadata {
  scanId: string;
  userId: string;
  requestId: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  userAgent: string;
  viewport: {
    width: number;
    height: number;
  };
  environment: string;
  version: string;
}

// API Request/Response Types
export interface WcagScanRequest {
  targetUrl: string;
  scanOptions?: WcagScanOptions;
}

export interface WcagScanResponse {
  success: boolean;
  data: WcagScanResult;
  requestId: string;
  processingTime: number;
}

export interface WcagExportRequest {
  scanId: string;
  format: 'pdf' | 'json' | 'csv';
  includeEvidence?: boolean;
  includeRecommendations?: boolean;
  includeManualReviewItems?: boolean;
}

// Error Types
export interface WcagError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  ruleId?: string;
  context?: string;
}

// Configuration Constants
export interface WcagRuleConfig {
  ruleId: string;
  ruleName: string;
  category: WcagCategory;
  wcagVersion: WcagVersion;
  successCriterion: string;
  level: WcagLevel;
  weight: number;
  automated: boolean;
  description: string;
  checkFunction: string;
}

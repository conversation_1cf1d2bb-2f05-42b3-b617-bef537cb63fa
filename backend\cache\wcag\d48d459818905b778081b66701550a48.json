{"data": {"ruleId": "WCAG-066", "ruleName": "Error Prevention Enhanced", "category": "robust", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0305, "automated": true, "evidence": [{"type": "interaction", "description": "Error prevention analysis", "value": "Found 3 forms/inputs with inadequate error prevention mechanisms", "elementCount": 1, "affectedSelectors": ["Found", "forms", "inputs", "with", "inadequate", "error", "prevention", "mechanisms"], "severity": "error", "metadata": {"scanDuration": 306, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-066", "ruleName": "Error Prevention Enhanced", "timestamp": "2025-07-11T19:26:48.694Z"}}}, {"type": "interaction", "description": "Form lacks error prevention (low risk)", "value": "2 fields, missing: validation", "selector": "#[object HTMLInputElement]", "severity": "info", "metadata": {"scanDuration": 306, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-066", "ruleName": "Error Prevention Enhanced", "timestamp": "2025-07-11T19:26:48.694Z"}}, "elementCount": 1, "affectedSelectors": ["#[object HTMLInputElement]", "fields", "missing", "validation"]}], "recommendations": ["Implement validation for all user inputs", "Add confirmation dialogs for high-risk operations", "Provide review/preview functionality for critical forms", "Include undo mechanisms where possible", "CRITICAL: Add multiple error prevention mechanisms for critical operations", "Require confirmation fields for password changes and deletions"], "executionTime": 24, "originalScore": 55, "thresholdApplied": 75, "scoringDetails": "55.0% (threshold: 75%) - FAILED"}, "timestamp": 1752262008694, "hash": "d3553be2174bf34cf0edc1d97cc07b4f", "accessCount": 1, "lastAccessed": 1752262008694, "size": 1742, "metadata": {"originalKey": "rule:WCAG-066:053b13d2:add92319", "normalizedKey": "rule_wcag-066_053b13d2_add92319", "savedAt": 1752262008695, "version": "1.1", "keyHash": "7b3f0d2f6b08681d293efff40af776cf"}}
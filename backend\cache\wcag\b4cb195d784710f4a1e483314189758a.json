{"data": [{"type": "code", "description": "Moving content without user controls: JavaScript-based animation detected", "value": "Moving content detected", "selector": "script", "severity": "error", "elementCount": 1, "affectedSelectors": ["script", "Moving", "content", "detected"], "metadata": {"scanDuration": 42, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-analysis", "mediaElementsAnalyzed": true, "animationDetection": true, "accessibilityPatterns": true, "multimediaAccessibilityTesting": true, "evidenceIndex": 0, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T19:24:10.019Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752261850019, "hash": "d6dd2f660039c9a9d81c57679324e000", "accessCount": 1, "lastAccessed": 1752261850019, "size": 794, "metadata": {"originalKey": "rule:WCAG-045:WCAG-045", "normalizedKey": "rule_wcag-045_wcag-045", "savedAt": 1752261850019, "version": "1.1", "keyHash": "e65574ee35119efda51fba780a5bdeda"}}
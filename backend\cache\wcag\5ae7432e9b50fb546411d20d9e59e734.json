{"data": {"ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "code", "description": "Advanced trap detection: custom trap detected", "value": "Severity: critical, Affected: a:nth-of-type(1), a:nth-of-type(2), a:nth-of-type(3), a:nth-of-type(4), a:nth-of-type(5), a:nth-of-type(6), a:nth-of-type(7), a:nth-of-type(8), a:nth-of-type(12), a:nth-of-type(13), a:nth-of-type(14), a:nth-of-type(15), a:nth-of-type(16), a:nth-of-type(19), a:nth-of-type(20), a:nth-of-type(25), a:nth-of-type(26), a:nth-of-type(34), a:nth-of-type(35), a:nth-of-type(36), a:nth-of-type(37), a:nth-of-type(38), a:nth-of-type(46), a:nth-of-type(47), a:nth-of-type(53), a:nth-of-type(54), a:nth-of-type(55), a:nth-of-type(56), a:nth-of-type(59), a:nth-of-type(60), a:nth-of-type(68), a:nth-of-type(69), a:nth-of-type(78), a:nth-of-type(79), a:nth-of-type(80), a:nth-of-type(81), a:nth-of-type(82), a:nth-of-type(85), a:nth-of-type(86), a:nth-of-type(93), a:nth-of-type(94), a:nth-of-type(97), a:nth-of-type(98), a:nth-of-type(99), a:nth-of-type(100), a:nth-of-type(101), a:nth-of-type(102), a:nth-of-type(103), a:nth-of-type(104), a:nth-of-type(105), a:nth-of-type(106), a:nth-of-type(107), a:nth-of-type(108), a:nth-of-type(109), a:nth-of-type(110), a:nth-of-type(111), a:nth-of-type(112), a:nth-of-type(113), a:nth-of-type(114), a:nth-of-type(115), a:nth-of-type(116), a:nth-of-type(117), a:nth-of-type(118), a:nth-of-type(119), a:nth-of-type(120), a:nth-of-type(121), a:nth-of-type(122), a:nth-of-type(123), a:nth-of-type(124), a:nth-of-type(125), a:nth-of-type(126), a:nth-of-type(127), a:nth-of-type(128), a:nth-of-type(129), a:nth-of-type(130), a:nth-of-type(131), a:nth-of-type(132), a:nth-of-type(133), a:nth-of-type(134), a:nth-of-type(135), a:nth-of-type(136), a:nth-of-type(137), a:nth-of-type(138), a:nth-of-type(139), a:nth-of-type(140), a:nth-of-type(141), a:nth-of-type(142), a:nth-of-type(143), a:nth-of-type(144), a:nth-of-type(145), a:nth-of-type(146), a:nth-of-type(147), a:nth-of-type(148), a:nth-of-type(149), a:nth-of-type(150), a:nth-of-type(151), a:nth-of-type(152), a:nth-of-type(153), a:nth-of-type(154), a:nth-of-type(155), a:nth-of-type(156), a:nth-of-type(157), a:nth-of-type(158), a:nth-of-type(159), a:nth-of-type(160), a:nth-of-type(161), a:nth-of-type(162), a:nth-of-type(163), a:nth-of-type(164), a:nth-of-type(165), a:nth-of-type(166), a:nth-of-type(167), a:nth-of-type(168), a:nth-of-type(169), a:nth-of-type(170), a:nth-of-type(171), a:nth-of-type(172), a:nth-of-type(173), a:nth-of-type(174), a:nth-of-type(175), a:nth-of-type(176), a:nth-of-type(177), a:nth-of-type(178), a:nth-of-type(179), a:nth-of-type(180), a:nth-of-type(181), a:nth-of-type(182), a:nth-of-type(183), a:nth-of-type(184), a:nth-of-type(185), a:nth-of-type(186), a:nth-of-type(187), Escape routes: 0", "severity": "error", "elementCount": 1, "affectedSelectors": ["Severity", "critical", "Affected", "a", "nth-of-type", "Escape", "routes"], "metadata": {"scanDuration": 689, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-11T19:26:54.259Z"}}}, {"type": "code", "description": "Escape mechanisms validation: Insufficient escape routes", "value": "Only 1/5 escape mechanisms found", "severity": "warning", "elementCount": 1, "affectedSelectors": ["Only", "escape", "mechanisms", "found"], "metadata": {"scanDuration": 689, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-11T19:26:54.259Z"}}}], "recommendations": ["Add escape mechanisms (Esc key, close button)", "Ensure focus can move freely between elements", "Provide clear instructions for keyboard navigation", "Add multiple escape mechanisms (Esc key, close button, click outside)"], "executionTime": 484, "originalScore": 0, "thresholdApplied": 75, "scoringDetails": "0.0% (threshold: 75%) - FAILED"}, "timestamp": 1752262014259, "hash": "3c455593d52f30a6f11c1779111f7be1", "accessCount": 1, "lastAccessed": 1752262014259, "size": 4125, "metadata": {"originalKey": "rule:WCAG-027:053b13d2:add92319", "normalizedKey": "rule_wcag-027_053b13d2_add92319", "savedAt": 1752262014259, "version": "1.1", "keyHash": "d0d975299b49012a74e1b863f9356b49"}}
{"data": {"ruleId": "WCAG-051", "ruleName": "Keyboard Accessible", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0687, "automated": true, "evidence": [{"type": "info", "description": "Advanced keyboard accessibility analysis with comprehensive interaction testing", "element": "interactive-elements", "value": "{\"overallScore\":17,\"criticalIssues\":[\"Focus indicator obscured for element: a.w-full\",\"Focus indicator obscured for element: #demo-account-login-button\",\"Focus indicator obscured for element: a.mt-10\",\"Focus indicator obscured for element: a.mt-10\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.w-full\",\"Focus indicator obscured for element: #demo-account-login-button\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Failed to analyze focus obstruction for #headlessui-menu-button-:Rspmm:: DOMException: SyntaxError: Failed to execute 'querySelector' on 'Document': '#headlessui-menu-button-:Rspmm:' is not a valid selector.\",\"Failed to analyze focus obstruction for #headlessui-menu-button-:Ru9mm:: DOMException: SyntaxError: Failed to execute 'querySelector' on 'Document': '#headlessui-menu-button-:Ru9mm:' is not a valid selector.\",\"Focus indicator obscured for element: button.right-1\",\"Focus indicator obscured for element: button.rounded-full\",\"Focus indicator obscured for element: button.rounded-full\",\"Focus indicator obscured for element: input.grow\"],\"recommendations\":[\"Ensure focus indicator for a.w-full is not obscured by other elements\",\"Ensure focus indicator for #demo-account-login-button is not obscured by other elements\",\"Ensure focus indicator for a.mt-10 is not obscured by other elements\",\"Ensure focus indicator for a.mt-10 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.w-full is not obscured by other elements\",\"Ensure focus indicator for #demo-account-login-button is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for button.right-1 is not obscured by other elements\",\"Ensure focus indicator for button.rounded-full is not obscured by other elements\",\"Ensure focus indicator for button.rounded-full is not obscured by other elements\",\"Ensure focus indicator for input.grow is not obscured by other elements\"],\"performanceMetrics\":{\"analysisTime\":1292,\"elementsAnalyzed\":75,\"obstructionsFound\":60}}", "severity": "error", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "Focus", "indicator", "obscured", "for", "element", "a.w-full", "demo-account-login-button", "a.mt-10", "a.mt-4", "a.relative", "a.text-sm", "Failed", "to", "analyze", "focus", "obstruction", "headlessui-menu-button-", "Rspmm", "DOMException", "SyntaxError", "execute", "querySelector", "on", "Document", "is", "not", "a", "valid", "selector", "Ru9mm", "button.right-1", "button.rounded-full", "input.grow", "recommendations", "Ensure", "by", "other", "elements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "obstructionsFound"], "metadata": {"scanDuration": 3610, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-051", "ruleName": "Keyboard Accessible", "timestamp": "2025-07-11T11:38:52.497Z"}}}, {"type": "code", "description": "Keyboard accessibility issue: Disabled element still in tab order", "value": "button | Focusable: true | TabIndex: 0", "selector": "button:nth-of-type(22)", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(22)", "button", "Focusable", "true", "TabIndex"], "severity": "warning", "metadata": {"scanDuration": 3610, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-051", "ruleName": "Keyboard Accessible", "timestamp": "2025-07-11T11:38:52.498Z"}}}, {"type": "code", "description": "Keyboard accessibility issue: Disabled element still in tab order", "value": "button | Focusable: true | TabIndex: 0", "selector": "button:nth-of-type(40)", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(40)", "button", "Focusable", "true", "TabIndex"], "severity": "warning", "metadata": {"scanDuration": 3610, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-051", "ruleName": "Keyboard Accessible", "timestamp": "2025-07-11T11:38:52.498Z"}}}], "recommendations": ["Ensure focus indicator for a.w-full is not obscured by other elements", "Ensure focus indicator for #demo-account-login-button is not obscured by other elements", "Ensure focus indicator for a.mt-10 is not obscured by other elements", "Ensure focus indicator for a.mt-10 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.w-full is not obscured by other elements", "Ensure focus indicator for #demo-account-login-button is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for button.right-1 is not obscured by other elements", "Ensure focus indicator for button.rounded-full is not obscured by other elements", "Ensure focus indicator for button.rounded-full is not obscured by other elements", "Ensure focus indicator for input.grow is not obscured by other elements", "Ensure all interactive elements are keyboard accessible", "Provide keyboard equivalents for mouse-only interactions", "Use proper semantic HTML elements for interactive content", "Add appropriate ARIA roles and properties for custom controls", "Test all functionality using only the keyboard"], "executionTime": 1309, "originalScore": 84}, "timestamp": 1752233932498, "hash": "07944a526d8c4b18f5fe409883cf49ae", "accessCount": 1, "lastAccessed": 1752233932498, "size": 15346}
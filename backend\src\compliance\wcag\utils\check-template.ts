/**
 * WCAG Check Template System
 * Provides consistent structure for all WCAG checks
 */

import { Page } from 'puppeteer';
import {
  WcagCheckResult,
  WcagEvidence,
  WcagCategory,
  WcagVersion,
  WcagLevel,
  // AutomatedCheckStatus,
  // WcagManualReviewItem,
} from '../types';
import { SCORING_CONFIG, ScoringConfig } from '../constants';
import { UnifiedDOMExtractor, PageStructure } from './unified-dom-extractor';
import logger from '../../../utils/logger';

export interface CheckConfig {
  targetUrl: string;
  timeout: number;
  scanId: string;
  page?: Page;
  pageStructure?: PageStructure; // ✅ Unified DOM structure for all checks
}

export interface EnhancedCheckConfig extends CheckConfig {
  retryAttempts: number;
  enableJavaScript: boolean;
  enableImages: boolean;
  followRedirects: boolean;
}

export interface CheckResult {
  score: number;
  maxScore: number;
  evidence: WcagEvidence[];
  issues: string[];
  recommendations: string[];
}

export type CheckFunction<T extends CheckConfig> = (
  page: Page,
  config: T,
) => Promise<CheckResult>;

export class CheckTemplate {
  private domExtractor: UnifiedDOMExtractor;

  constructor() {
    this.domExtractor = UnifiedDOMExtractor.getInstance();
  }

  /**
   * Calculate WCAG compliance with configurable threshold scoring
   */
  private calculateWcagCompliance(
    result: CheckResult,
    config: ScoringConfig = {
      passThreshold: SCORING_CONFIG.DEFAULT_PASS_THRESHOLD,
      strictMode: SCORING_CONFIG.STRICT_MODE,
      enableGradualScoring: SCORING_CONFIG.ENABLE_GRADUAL_SCORING,
      enableThresholdLogging: SCORING_CONFIG.ENABLE_THRESHOLD_LOGGING,
    }
  ): { status: 'passed' | 'failed'; score: number; details: string } {
    const scorePercentage = (result.score / result.maxScore) * 100;

    // Apply threshold-based scoring
    const isPassed = scorePercentage >= config.passThreshold;

    let finalScore: number;
    let status: 'passed' | 'failed';

    if (config.strictMode) {
      // Strict mode: binary scoring (legacy behavior)
      finalScore = isPassed ? result.maxScore : 0;
      status = isPassed ? 'passed' : 'failed';
    } else if (config.enableGradualScoring && isPassed) {
      // Gradual scoring: show actual score if passed
      finalScore = result.score;
      status = 'passed';
    } else {
      // Standard mode: 100% if passed, 0% if failed
      finalScore = isPassed ? result.maxScore : 0;
      status = isPassed ? 'passed' : 'failed';
    }

    const details = `${scorePercentage.toFixed(1)}% (threshold: ${config.passThreshold}%) - ${status.toUpperCase()}`;

    return { status, score: finalScore, details };
  }

  /**
   * Execute a WCAG check with consistent error handling and logging
   */
  async executeCheck<T extends CheckConfig>(
    ruleId: string,
    ruleName: string,
    category: string,
    weight: number,
    level: string,
    config: T,
    checkFunction: CheckFunction<T>,
    requiresBrowser: boolean = true,
    requiresManualReview: boolean = false,
  ): Promise<WcagCheckResult> {
    const startTime = Date.now();

    try {
      logger.info(`🔍 [${config.scanId}] Starting ${ruleId}: ${ruleName}`);

      if (requiresManualReview) {
        throw new Error('Manual review checks should not use fully automated template');
      }

      try {
        if (requiresBrowser && !config.page) {
          throw new Error('Browser instance required - page not provided in config');
        }

        // ✅ UNIFIED DOM EXTRACTION: Extract page structure once for all checks
        if (requiresBrowser && config.page && !config.pageStructure) {
          logger.debug(`📋 [${config.scanId}] Extracting unified page structure for ${ruleId}`);
          try {
            config.pageStructure = await this.domExtractor.extractPageStructure(config.page, config.targetUrl);
            logger.debug(`📋 [${config.scanId}] Page structure extracted: ${Object.keys(config.pageStructure.performance.elementCounts).reduce((sum, key) => sum + config.pageStructure!.performance.elementCounts[key], 0)} elements`);
          } catch (extractionError) {
            logger.warn(`⚠️ [${config.scanId}] DOM extraction failed for ${ruleId}, proceeding without structure:`, { error: extractionError instanceof Error ? extractionError.message : String(extractionError) });
            // Continue without page structure - checks should handle gracefully
          }
        }

        // Execute the specific check function with enhanced config
        const result = await checkFunction(config.page!, config);

        const executionTime = Date.now() - startTime;

        // ✅ ENHANCED WCAG Compliance: 75% Threshold Scoring System
        // Apply configurable threshold-based scoring instead of strict binary
        const scoringResult = this.calculateWcagCompliance(result);
        const { status, score: finalScore, details } = scoringResult;

        // Enhanced logging with threshold information
        if (status === 'failed') {
          logger.warn(
            `⚠️ [${config.scanId}] ${ruleId} failed: ${details}`,
          );
        } else {
          logger.info(
            `✅ [${config.scanId}] ${ruleId} passed: ${details}`,
          );
        }

        logger.info(
          `🎯 [${config.scanId}] Completed ${ruleId} in ${executionTime}ms - Status: ${status} (${finalScore}/${result.maxScore})`,
        );

        return {
          ruleId,
          ruleName,
          category: category as WcagCategory,
          wcagVersion: this.getVersionFromRuleId(ruleId),
          successCriterion: this.getSuccessCriterionFromRuleId(ruleId),
          level: level as WcagLevel,
          status,
          score: finalScore, // ✅ Threshold-based score: 75% threshold applied
          maxScore: result.maxScore,
          weight,
          automated: true,
          evidence: result.evidence,
          recommendations: result.recommendations,
          executionTime,
          // ✅ Enhanced scoring metadata for analysis and debugging
          originalScore: result.score, // Original score before threshold application
          thresholdApplied: SCORING_CONFIG.DEFAULT_PASS_THRESHOLD, // Threshold used
          scoringDetails: details, // Full scoring explanation
        };
      } finally {
        // Browser cleanup will be handled by orchestrator
      }
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      logger.error(`❌ [${config.scanId}] Error in ${ruleId}`, {
        error: {
          message: errorMessage,
          stack: errorStack,
          name: error instanceof Error ? error.name : 'UnknownError',
        },
        ruleId,
        ruleName,
        executionTime,
      });

      return {
        ruleId,
        ruleName,
        category: category as WcagCategory,
        wcagVersion: this.getVersionFromRuleId(ruleId),
        successCriterion: this.getSuccessCriterionFromRuleId(ruleId),
        level: level as WcagLevel,
        status: 'failed',
        score: 0,
        maxScore: 100,
        weight,
        automated: true,
        evidence: [
          {
            type: 'text',
            description: 'Technical error during check execution',
            value: errorMessage,
            severity: 'error',
          },
        ],
        recommendations: [
          'Check failed due to technical error - manual review recommended',
          `Error details: ${errorMessage}`,
          'Check browser console and server logs for more information',
        ],
        executionTime,
        errorMessage,
      };
    }
  }

  /**
   * Get WCAG version from rule ID
   */
  protected getVersionFromRuleId(ruleId: string): WcagVersion {
    const ruleNumber = parseInt(ruleId.split('-')[1]);

    if (ruleNumber <= 9) return '2.1';
    if (ruleNumber <= 16) return '2.2';
    return '3.0';
  }

  /**
   * Get success criterion from rule ID
   */
  protected getSuccessCriterionFromRuleId(ruleId: string): string {
    const criterionMap: Record<string, string> = {
      'WCAG-001': '1.1.1',
      'WCAG-002': '1.2.2',
      'WCAG-003': '1.3.1',
      'WCAG-004': '1.4.3',
      'WCAG-005': '2.1.1',
      'WCAG-006': '2.4.3',
      'WCAG-007': '2.4.7',
      'WCAG-008': '3.3.1',
      'WCAG-009': '4.1.2',
      'WCAG-010': '2.4.11',
      'WCAG-011': '2.4.12',
      'WCAG-012': '2.4.13',
      'WCAG-013': '2.5.7',
      'WCAG-014': '2.5.8',
      'WCAG-015': '3.2.6',
      'WCAG-016': '3.3.7',
      'WCAG-017': '2.1',
      'WCAG-018': '2.2',
      'WCAG-019': '2.4',
      'WCAG-020': '2.5',
      'WCAG-021': '3.1',
    };

    return criterionMap[ruleId] || 'Unknown';
  }
}

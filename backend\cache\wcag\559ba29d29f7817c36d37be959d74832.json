{"data": {"ruleId": "WCAG-016", "ruleName": "Redundant Entry", "category": "understandable", "wcagVersion": "2.2", "successCriterion": "0.0.0", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.07, "automated": false, "evidence": [{"type": "text", "description": "Redundant entry analysis summary", "value": "2/3 checks pass automated tests, 0 require manual review", "severity": "error", "elementCount": 1, "affectedSelectors": ["checks", "pass", "automated", "tests", "require", "manual", "review"], "metadata": {"scanDuration": 1333, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-016", "ruleName": "Redundant Entry", "timestamp": "2025-07-11T11:37:59.610Z"}}}, {"type": "text", "description": "No duplicate fields detected", "value": "Single address field found", "severity": "info", "elementCount": 1, "affectedSelectors": ["Single", "address", "field", "found"], "metadata": {"scanDuration": 1333, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-016", "ruleName": "Redundant Entry", "timestamp": "2025-07-11T11:37:59.610Z"}}}, {"type": "text", "description": "No multi-step form indicators found", "value": "Single-page form or no complex form flow detected", "severity": "info", "elementCount": 1, "affectedSelectors": ["Single-page", "form", "or", "no", "complex", "flow", "detected"], "metadata": {"scanDuration": 1333, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-016", "ruleName": "Redundant Entry", "timestamp": "2025-07-11T11:37:59.610Z"}}}], "recommendations": ["Add autocomplete=\"name\" to input.grow.rounded-lg.border-[0.5px].border-black-v2-50.bg-white.px-6.py-3.5.font-formadjrtext.text-lg.leading-7.text-dark-blue.placeholder:text-[#bcbec4].sm:pr-44"], "executionTime": 0, "errorMessage": "Field missing autocomplete attribute: input.grow.rounded-lg.border-[0.5px].border-black-v2-50.bg-white.px-6.py-3.5.font-formadjrtext.text-lg.leading-7.text-dark-blue.placeholder:text-[#bcbec4].sm:pr-44", "manualReviewItems": []}, "timestamp": 1752233879610, "hash": "1b81bebda448d2cd20c1494282c6d241", "accessCount": 1, "lastAccessed": 1752233879610, "size": 2097}
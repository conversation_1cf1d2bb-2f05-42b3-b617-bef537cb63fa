{"data": {"ruleId": "WCAG-029", "ruleName": "Page Titled", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "info", "description": "Page has descriptive title", "value": "<title>#1 Healthcare Collaboration Platform | TigerConnect</title>", "selector": "title", "elementCount": 1, "affectedSelectors": ["title", "Healthcare", "Collaboration", "Platform", "TigerConnect"], "severity": "info", "metadata": {"scanDuration": 1115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-029", "ruleName": "Page Titled", "timestamp": "2025-07-11T10:49:10.080Z"}}}, {"type": "warning", "description": "Multiple title elements found", "value": "14 title elements detected", "selector": "title", "elementCount": 1, "affectedSelectors": ["title", "elements", "detected"], "severity": "warning", "metadata": {"scanDuration": 1115, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-029", "ruleName": "Page Titled", "timestamp": "2025-07-11T10:49:10.080Z"}}}], "recommendations": ["Use exactly one title element per page", "Use descriptive titles that identify the page topic and purpose", "Include both page-specific and site context in titles", "Keep titles between 10-60 characters for optimal display", "Test titles with screen readers and browser tabs"], "executionTime": 8, "originalScore": 90}, "timestamp": 1752230950081, "hash": "f120db6b93ec27c54f198261f1c6c03f", "accessCount": 1, "lastAccessed": 1752230950081, "size": 1498}
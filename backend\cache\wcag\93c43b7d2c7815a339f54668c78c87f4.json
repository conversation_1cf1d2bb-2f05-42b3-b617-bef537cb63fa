{"data": {"ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0611, "automated": true, "evidence": [{"type": "text", "description": "auto-updating 1 has pause/stop controls", "value": "auto-update-0 - type: auto-update, hasControls: true", "severity": "info", "elementCount": 1, "affectedSelectors": ["auto-update-0", "type", "auto-update", "hasControls", "true"], "metadata": {"scanDuration": 1282, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T11:38:19.178Z"}}}, {"type": "code", "description": "auto-updating 2 requires pause/stop controls", "value": "auto-update-1 - type: auto-update, duration: 0s", "severity": "error", "elementCount": 1, "affectedSelectors": ["auto-update-1", "type", "auto-update", "duration", "s"], "metadata": {"scanDuration": 1282, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T11:38:19.178Z"}}}, {"type": "text", "description": "auto-updating 3 has pause/stop controls", "value": "auto-update-2 - type: auto-update, hasControls: true", "severity": "info", "elementCount": 1, "affectedSelectors": ["auto-update-2", "type", "auto-update", "hasControls", "true"], "metadata": {"scanDuration": 1282, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T11:38:19.178Z"}}}, {"type": "code", "description": "auto-updating 4 requires pause/stop controls", "value": "auto-update-3 - type: auto-update, duration: 0s", "severity": "error", "elementCount": 1, "affectedSelectors": ["auto-update-3", "type", "auto-update", "duration", "s"], "metadata": {"scanDuration": 1282, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T11:38:19.178Z"}}}, {"type": "code", "description": "Moving content without user controls: Carousel or slider element", "value": "Moving content detected", "selector": "ul:nth-of-type(2)", "severity": "error", "elementCount": 1, "affectedSelectors": ["ul:nth-of-type(2)", "Moving", "content", "detected"], "metadata": {"scanDuration": 1282, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T11:38:19.178Z"}}}, {"type": "code", "description": "Moving content without user controls: Carousel or slider element", "value": "Moving content detected", "selector": "ul:nth-of-type(4)", "severity": "error", "elementCount": 1, "affectedSelectors": ["ul:nth-of-type(4)", "Moving", "content", "detected"], "metadata": {"scanDuration": 1282, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T11:38:19.178Z"}}}], "recommendations": ["Add pause/stop controls to auto-updating 2", "Add pause/stop controls to auto-updating 4", "Provide pause, stop, or hide controls for moving content", "Allow users to control auto-updating content", "Ensure moving content can be paused on user request", "Consider reducing or eliminating unnecessary animations"], "executionTime": 29, "originalScore": 50}, "timestamp": 1752233899178, "hash": "c15b6f9b9db973aebf38e778f526cfe2", "accessCount": 1, "lastAccessed": 1752233899178, "size": 3548}
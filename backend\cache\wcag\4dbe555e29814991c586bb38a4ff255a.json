{"data": [{"type": "info", "description": "Link with descriptive text", "value": "<a href=\"#main\">Skip to content</a>", "selector": "a:nth-of-type(1)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(1)", "a", "href", "#main", "<PERSON><PERSON>", "to", "content"], "severity": "info", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "info", "description": "Link with descriptive text", "value": "<a href=\"tel:************\">Contact Sales: (*************</a>", "selector": "a:nth-of-type(2)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(2)", "a", "href", "tel", "Contact", "Sales"], "severity": "info", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "info", "description": "Link with descriptive text", "value": "<a href=\"https://tigerconnect.com/about/contact-us/#tab-contactsupport\">Contact Support</a>", "selector": "a:nth-of-type(3)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(3)", "a", "href", "https", "tigerconnect.com", "about", "contact-us", "#tab-contactsupport", "Contact", "Support", "//tigerconnect.com/about/contact-us/#tab-contactsupport\">Contact"], "severity": "info", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/\"></a>", "selector": "a:nth-of-type(9)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(9)", "a", "href", "https", "tigerconnect.com", "//tigerconnect.com/\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/healthcare-professionals/\"></a>", "selector": "a:nth-of-type(12)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(12)", "a", "href", "https", "tigerconnect.com", "healthcare-professionals", "//tigerconnect.com/healthcare-professionals/\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/products/\"></a>", "selector": "a:nth-of-type(33)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(33)", "a", "href", "https", "tigerconnect.com", "products", "//tigerconnect.com/products/\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/resource-center/\"></a>", "selector": "a:nth-of-type(52)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(52)", "a", "href", "https", "tigerconnect.com", "resource-center", "//tigerconnect.com/resource-center/\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 6, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/about/\"></a>", "selector": "a:nth-of-type(77)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(77)", "a", "href", "https", "tigerconnect.com", "about", "//tigerconnect.com/about/\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 7, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/\"></a>", "selector": "a:nth-of-type(92)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(92)", "a", "href", "https", "tigerconnect.com", "//tigerconnect.com/\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 8, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://newtc.kinsta.cloud/products/clinical-collaboration/\"></a>", "selector": "a:nth-of-type(97)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(97)", "a", "href", "https", "newtc.kinsta.cloud", "products", "clinical-collaboration", "//newtc.kinsta.cloud/products/clinical-collaboration/\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 9, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://newtc.kinsta.cloud/products/clinical-collaboration/\"></a>", "selector": "a:nth-of-type(99)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(99)", "a", "href", "https", "newtc.kinsta.cloud", "products", "clinical-collaboration", "//newtc.kinsta.cloud/products/clinical-collaboration/\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 10, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/products/alarm-management-event-notification-software/\"></a>", "selector": "a:nth-of-type(101)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(101)", "a", "href", "https", "tigerconnect.com", "products", "alarm-management-event-notification-software", "//tigerconnect.com/products/alarm-management-event-notification-software/\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 11, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/products/patient-engagement-software/\"></a>", "selector": "a:nth-of-type(103)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(103)", "a", "href", "https", "tigerconnect.com", "products", "patient-engagement-software", "//tigerconnect.com/products/patient-engagement-software/\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 12, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"https://tigerconnect.com/workflows/critical-response-workflows/\">Learn More</a>", "selector": "a:nth-of-type(110)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(110)", "a", "href", "https", "tigerconnect.com", "workflows", "critical-response-workflows", "Learn", "More", "//tigerconnect.com/workflows/critical-response-workflows/\">Learn"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 13, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"/healthcare-professionals/physicians/\">Read More</a>", "selector": "a:nth-of-type(125)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(125)", "a", "href", "healthcare-professionals", "physicians", "Read", "More"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 14, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"https://tigerconnect.com/healthcare-professionals/it/\">Read More</a>", "selector": "a:nth-of-type(126)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(126)", "a", "href", "https", "tigerconnect.com", "healthcare-professionals", "it", "Read", "More", "//tigerconnect.com/healthcare-professionals/it/\">Read"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 15, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"https://tigerconnect.com/healthcare-professionals/nurses/\">Read More</a>", "selector": "a:nth-of-type(127)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(127)", "a", "href", "https", "tigerconnect.com", "healthcare-professionals", "nurses", "Read", "More", "//tigerconnect.com/healthcare-professionals/nurses/\">Read"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 16, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"https://tigerconnect.com/healthcare-professionals/execs/\">Read More</a>", "selector": "a:nth-of-type(128)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(128)", "a", "href", "https", "tigerconnect.com", "healthcare-professionals", "execs", "Read", "More", "//tigerconnect.com/healthcare-professionals/execs/\">Read"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 17, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/resources/reports/2024-state-of-healthcare-collaboration/\"></a>", "selector": "a:nth-of-type(139)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(139)", "a", "href", "https", "tigerconnect.com", "resources", "reports", "state-of-healthcare-collaboration", "//tigerconnect.com/resources/reports/2024-state-of-healthcare-collaboration/\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 18, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.516Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://tigerconnect.com/resources/reports/gartner-report-2024-gartner-magic-quadrant-for-clinical-communication-and-collaboration\"></a>", "selector": "a:nth-of-type(141)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(141)", "a", "href", "https", "tigerconnect.com", "resources", "reports", "gartner-report-2024-gartner-magic-quadrant-for-clinical-communication-and-collaboration", "//tigerconnect.com/resources/reports/gartner-report-2024-gartner-magic-quadrant-for-clinical-communication-and-collaboration\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 19, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.517Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://apps.apple.com/rs/app/tigerconnect/id355832697\"></a>", "selector": "a:nth-of-type(177)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(177)", "a", "href", "https", "apps.apple.com", "rs", "app", "tigerconnect", "id355832697", "//apps.apple.com/rs/app/tigerconnect/id355832697\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 20, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.517Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://play.google.com/store/apps/details?id=com.tigertext&hl=en_US&gl=US&pli=1\"></a>", "selector": "a:nth-of-type(178)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(178)", "a", "href", "https", "play.google.com", "store", "apps", "details", "id", "com.tigertext", "hl", "en_US", "gl", "US", "pli", "//play.google.com/store/apps/details?id=com.tigertext&hl=en_US&gl=US&pli=1\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 21, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.517Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://www.facebook.com/TigerConnected\"></a>", "selector": "a:nth-of-type(179)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(179)", "a", "href", "https", "www.facebook.com", "TigerConnected", "//www.facebook.com/TigerConnected\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 22, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.517Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://www.instagram.com/lifeattigerconnect/\"></a>", "selector": "a:nth-of-type(180)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(180)", "a", "href", "https", "www.instagram.com", "lifeattigerconnect", "//www.instagram.com/lifeattigerconnect/\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 23, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.517Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://www.linkedin.com/company/tigerconnect\"></a>", "selector": "a:nth-of-type(181)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(181)", "a", "href", "https", "www.linkedin.com", "company", "tigerconnect", "//www.linkedin.com/company/tigerconnect\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 24, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.517Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://twitter.com/TigerConnect\"></a>", "selector": "a:nth-of-type(182)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(182)", "a", "href", "https", "twitter.com", "TigerConnect", "//twitter.com/TigerConnect\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 25, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.517Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with empty accessible name", "value": "<a href=\"https://www.youtube.com/c/tigerconnect\"></a>", "selector": "a:nth-of-type(183)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(183)", "a", "href", "https", "www.youtube.com", "c", "tigerconnect", "//www.youtube.com/c/tigerconnect\"></a>"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 26, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.517Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"https://tigerconnect.com/products/careconduit/\">Learn More</a>", "selector": "a:nth-of-type(184)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(184)", "a", "href", "https", "tigerconnect.com", "products", "careconduit", "Learn", "More", "//tigerconnect.com/products/careconduit/\">Learn"], "severity": "error", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 27, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.517Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true}}}, {"type": "warning", "description": "Link purpose analysis summary", "value": "232 total links, 25 problematic", "selector": "a[href]", "elementCount": 1, "affectedSelectors": ["a[href]", "total", "links", "problematic"], "severity": "warning", "metadata": {"scanDuration": 19, "elementsAnalyzed": 29, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 28, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T19:26:53.517Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true}}}], "timestamp": 1752262013517, "hash": "1ecb5a9d670db6e074ceb6183be3cd04", "accessCount": 1, "lastAccessed": 1752262013517, "size": 25262, "metadata": {"originalKey": "rule:WCAG-026:WCAG-026", "normalizedKey": "rule_wcag-026_wcag-026", "savedAt": 1752262013517, "version": "1.1", "keyHash": "b112f40959409251a19e970ebef7a70a"}}
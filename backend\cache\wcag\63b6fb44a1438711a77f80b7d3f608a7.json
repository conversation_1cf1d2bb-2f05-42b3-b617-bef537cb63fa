{"data": {"ruleId": "WCAG-029", "ruleName": "Page Titled", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "passed", "score": 90, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "info", "description": "Page has descriptive title", "value": "<title>#1 Healthcare Collaboration Platform | TigerConnect</title>", "selector": "title", "elementCount": 1, "affectedSelectors": ["title", "Healthcare", "Collaboration", "Platform", "TigerConnect"], "severity": "info", "metadata": {"scanDuration": 5555, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-029", "ruleName": "Page Titled", "timestamp": "2025-07-11T19:27:00.318Z"}}}, {"type": "warning", "description": "Multiple title elements found", "value": "14 title elements detected", "selector": "title", "elementCount": 1, "affectedSelectors": ["title", "elements", "detected"], "severity": "warning", "metadata": {"scanDuration": 5555, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-029", "ruleName": "Page Titled", "timestamp": "2025-07-11T19:27:00.318Z"}}}], "recommendations": ["Use exactly one title element per page", "Use descriptive titles that identify the page topic and purpose", "Include both page-specific and site context in titles", "Keep titles between 10-60 characters for optimal display", "Test titles with screen readers and browser tabs"], "executionTime": 87, "originalScore": 90, "thresholdApplied": 75, "scoringDetails": "90.0% (threshold: 75%) - PASSED"}, "timestamp": 1752262020318, "hash": "8ed66864ea2b50a336f1cd0b1022196c", "accessCount": 1, "lastAccessed": 1752262020318, "size": 1573, "metadata": {"originalKey": "rule:WCAG-029:053b13d2:add92319", "normalizedKey": "rule_wcag-029_053b13d2_add92319", "savedAt": 1752262020319, "version": "1.1", "keyHash": "57c15ed92d722527879e9e084ee3c2e9"}}
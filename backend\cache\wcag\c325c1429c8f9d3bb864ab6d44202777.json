{"data": {"ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "info", "description": "Link with descriptive text", "value": "<a href=\"/\">HostedScan</a>", "selector": "a:nth-of-type(1)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(1)", "a", "href", "HostedScan"], "severity": "info", "metadata": {"scanDuration": 53, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T11:39:28.818Z"}}}, {"type": "info", "description": "Link with descriptive text", "value": "<a href=\"/scan-types\">Scanners</a>", "selector": "a:nth-of-type(2)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(2)", "a", "href", "scan-types", "Scanners"], "severity": "info", "metadata": {"scanDuration": 53, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T11:39:28.818Z"}}}, {"type": "info", "description": "Link with descriptive text", "value": "<a href=\"/pricing\">Pricing</a>", "selector": "a:nth-of-type(3)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(3)", "a", "href", "pricing", "Pricing"], "severity": "info", "metadata": {"scanDuration": 53, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T11:39:28.819Z"}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"/compliance\">Learn more</a>", "selector": "a:nth-of-type(7)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(7)", "a", "href", "compliance", "Learn", "more"], "severity": "error", "metadata": {"scanDuration": 53, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T11:39:28.819Z"}}}, {"type": "error", "description": "Link with generic/non-descriptive text", "value": "<a href=\"/scan-types\">Learn more</a>", "selector": "a:nth-of-type(9)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(9)", "a", "href", "scan-types", "Learn", "more"], "severity": "error", "metadata": {"scanDuration": 53, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T11:39:28.819Z"}}}, {"type": "warning", "description": "Link purpose analysis summary", "value": "201 total links, 2 problematic", "selector": "a[href]", "elementCount": 1, "affectedSelectors": ["a[href]", "total", "links", "problematic"], "severity": "warning", "metadata": {"scanDuration": 53, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T11:39:28.819Z"}}}], "recommendations": ["Use descriptive link text that explains the purpose or destination", "Avoid generic phrases like \"click here\", \"more\", \"read more\"", "Ensure link text makes sense when read out of context", "Consider using aria-label for additional context when needed"], "executionTime": 14, "originalScore": 99}, "timestamp": 1752233968819, "hash": "057d8a0502350a746599698d3e757cf8", "accessCount": 1, "lastAccessed": 1752233968819, "size": 3395}
{"data": {"ruleId": "WCAG-028", "ruleName": "Bypass Blocks", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "info", "description": "Valid skip link found", "value": "Skip link: \"Skip to content\" targeting main", "selector": "a[href^=\"#\"]:first-child:nth-of-type(1)", "elementCount": 1, "affectedSelectors": ["a[href^=\"#\"]:first-child:nth-of-type(1)", "<PERSON><PERSON>", "link", "to", "content", "targeting", "main"], "severity": "info", "metadata": {"scanDuration": 81, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-028", "ruleName": "Bypass Blocks", "timestamp": "2025-07-11T10:49:08.883Z"}}}, {"type": "warning", "description": "Skip links with invalid targets", "value": "Skip links targeting non-existent elements: login", "selector": "a[href^=\"#\"]:first-child:nth-of-type(2)", "elementCount": 1, "affectedSelectors": ["a[href^=\"#\"]:first-child:nth-of-type(2)", "<PERSON><PERSON>", "links", "targeting", "non-existent", "elements", "login"], "severity": "warning", "metadata": {"scanDuration": 81, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-028", "ruleName": "Bypass Blocks", "timestamp": "2025-07-11T10:49:08.883Z"}}}], "recommendations": ["Add skip links to bypass repetitive navigation content", "Ensure skip links are among the first focusable elements", "Use proper landmarks (main, nav, header) to aid navigation", "Test skip links with keyboard navigation", "Consider consolidating multiple navigation blocks"], "executionTime": 13, "originalScore": 80}, "timestamp": 1752230948883, "hash": "563da8c8d2f8d5f922bc7bd388ba9e66", "accessCount": 1, "lastAccessed": 1752230948883, "size": 1675}
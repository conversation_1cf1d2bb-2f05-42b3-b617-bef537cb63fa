{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "metadata": {"scanDuration": 25, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.7, "checkType": "context-change-analysis", "behaviorAnalysis": true, "userInitiatedChanges": true, "accessibilityPatterns": true, "componentLibraryDetection": true, "evidenceIndex": 0, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-11T18:03:39.596Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752257019596, "hash": "88c9bd02391bdbcc2510ab2fba1b55a4", "accessCount": 1, "lastAccessed": 1752257019596, "size": 821, "metadata": {"originalKey": "WCAG-064:WCAG-064:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-064_wcag-064_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752257019597, "version": "1.1", "keyHash": "2c9e62c78c82dfeab62ac701290c788b"}}
{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame 'A362B1B11B79F8E7E1503ABC8B8AFA25'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A362B1B11B79F8E7E1503ABC8B8AFA25"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.7, "checkType": "context-change-analysis", "behaviorAnalysis": true, "userInitiatedChanges": true, "accessibilityPatterns": true, "componentLibraryDetection": true, "evidenceIndex": 0, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-11T18:49:41.617Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259781617, "hash": "972524f8e4edbfb00f9bb8ba3bc5db0e", "accessCount": 1, "lastAccessed": 1752259781617, "size": 821, "metadata": {"originalKey": "WCAG-064:WCAG-064:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-064_wcag-064_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752259781617, "version": "1.1", "keyHash": "2c9e62c78c82dfeab62ac701290c788b"}}
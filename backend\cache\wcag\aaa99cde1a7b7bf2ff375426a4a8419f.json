{"data": [{"type": "info", "description": "Advanced hover/focus triggered content analysis with layout validation", "element": "hover-focus-content", "value": "{\"overallScore\":80,\"criticalIssues\":[],\"recommendations\":[\"Increase target sizes to meet WCAG minimum requirements\"],\"performanceMetrics\":{\"analysisTime\":9116,\"elementsAnalyzed\":1000,\"breakpointsTested\":5}}", "severity": "info", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "recommendations", "Increase", "target", "sizes", "to", "meet", "WCAG", "minimum", "requirements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "breakpointsTested"], "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "link:nth-of-type(1)", "elementCount": 1, "affectedSelectors": ["link:nth-of-type(1)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 1, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "link:nth-of-type(2)", "elementCount": 1, "affectedSelectors": ["link:nth-of-type(2)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 2, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "link:nth-of-type(3)", "elementCount": 1, "affectedSelectors": ["link:nth-of-type(3)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 3, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "link:nth-of-type(4)", "elementCount": 1, "affectedSelectors": ["link:nth-of-type(4)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 4, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "link:nth-of-type(5)", "elementCount": 1, "affectedSelectors": ["link:nth-of-type(5)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 5, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "link:nth-of-type(6)", "elementCount": 1, "affectedSelectors": ["link:nth-of-type(6)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 6, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(8)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(8)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 7, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(9)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(9)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 8, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(10)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(10)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 9, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(11)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(11)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 10, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(12)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(12)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 11, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(13)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(13)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 12, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(14)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(14)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 13, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(15)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(15)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 14, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(16)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(16)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 15, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(17)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(17)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 16, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(18)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(18)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 17, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(19)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(19)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 18, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(20)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(20)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 19, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(21)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(21)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 20, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(22)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(22)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 21, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(23)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(23)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 22, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(24)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(24)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 23, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Hover/focus content issue: Title attribute tooltip not dismissible, Title attribute tooltip not hoverable, Title attribute tooltip not persistent", "value": "title-tooltip (hover)", "selector": "img:nth-of-type(25)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(25)", "title-tooltip", "hover"], "severity": "error", "metadata": {"scanDuration": 9389, "elementsAnalyzed": 87, "checkSpecificData": {"automationRate": 0.85, "checkType": "hover-focus-content-analysis", "interactionTriggerAnalysis": true, "contentPersistenceValidation": true, "dismissibilityTesting": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "evidenceIndex": 24, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T19:24:11.751Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752261851751, "hash": "87a9746f8a12654bc7cb2254e9a4048d", "accessCount": 1, "lastAccessed": 1752261851751, "size": 23839, "metadata": {"originalKey": "rule:WCAG-043:WCAG-043", "normalizedKey": "rule_wcag-043_wcag-043", "savedAt": 1752261851752, "version": "1.1", "keyHash": "965e4c6d91020cc79f4351a814b7e228"}}
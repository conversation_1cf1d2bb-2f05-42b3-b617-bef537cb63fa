{"data": [{"type": "text", "description": "No drag and drop elements found", "value": "Drag and drop elements found: false", "element": "page", "elementCount": 1, "affectedSelectors": ["Drag", "and", "drop", "elements", "found", "false"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "dragging-movement-analysis", "manualReviewRequired": false, "dragInteractionDetection": true, "alternativeMethodValidation": true, "interactionAnalysis": true, "touchGestureAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-013", "ruleName": "Dragging Movements", "timestamp": "2025-07-11T18:00:42.437Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "No sliders found", "value": "page - slidersFound: false", "severity": "info", "elementCount": 1, "affectedSelectors": ["page", "slidersFound", "false"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "dragging-movement-analysis", "manualReviewRequired": false, "dragInteractionDetection": true, "alternativeMethodValidation": true, "interactionAnalysis": true, "touchGestureAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-013", "ruleName": "Dragging Movements", "timestamp": "2025-07-11T18:00:42.437Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "info", "description": "No sortable lists found", "value": "Sortable lists found: false", "element": "page", "elementCount": 1, "affectedSelectors": ["Sortable", "lists", "found", "false"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "dragging-movement-analysis", "manualReviewRequired": false, "dragInteractionDetection": true, "alternativeMethodValidation": true, "interactionAnalysis": true, "touchGestureAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-013", "ruleName": "Dragging Movements", "timestamp": "2025-07-11T18:00:42.437Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "info", "description": "No resizable elements found", "value": "Resizable elements found: false", "element": "page", "elementCount": 1, "affectedSelectors": ["Resizable", "elements", "found", "false"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "dragging-movement-analysis", "manualReviewRequired": false, "dragInteractionDetection": true, "alternativeMethodValidation": true, "interactionAnalysis": true, "touchGestureAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-013", "ruleName": "Dragging Movements", "timestamp": "2025-07-11T18:00:42.437Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "error", "description": "Error analyzing gesture interactions", "value": "Error: element.className.includes is not a function\npptr:evaluate;DraggingMovementsCheck.analyzeGestureInteractions%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cchecks%5Cdragging-movements.ts%3A494%3A48):8:56", "element": "gesture elements", "elementCount": 1, "affectedSelectors": ["Error", "element.className.includes", "is", "not", "a", "function", "pptr", "evaluate", "DraggingMovementsCheck.analyzeGestureInteractions", "D", "A", "CWeb", "projects", "CCom<PERSON>ly", "Checker", "Cbackend", "Csrc", "Ccompliance", "Cwcag", "Cchecks", "Cdragging-movements.ts", "A494", "A48"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.7, "checkType": "dragging-movement-analysis", "manualReviewRequired": false, "dragInteractionDetection": true, "alternativeMethodValidation": true, "interactionAnalysis": true, "touchGestureAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-013", "ruleName": "Dragging Movements", "timestamp": "2025-07-11T18:00:42.437Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752256842437, "hash": "117f763c8a534db639e0f32bfc24dd70", "accessCount": 1, "lastAccessed": 1752256842437, "size": 4316, "metadata": {"originalKey": "WCAG-013:WCAG-013:dGV4dDpObyBkcmFnIGFuZCBkcm9wIGVs", "normalizedKey": "wcag-013_wcag-013_dgv4ddpobybkcmfnigfuzcbkcm9wigvs", "savedAt": 1752256842438, "version": "1.1", "keyHash": "98b1dab9983339502bb824572d306f25"}}
{"data": [{"type": "code", "description": "Input modality detection: high risk", "value": "Touch: 0, <PERSON>: 0, Keyboard: 0, Pointer: 13", "severity": "error", "elementCount": 1, "affectedSelectors": ["Touch", "Mouse", "Keyboard", "Pointer"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Detected input restriction types", "value": "pointer-disabled, css-pointer-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["pointer-disabled", "css-pointer-disabled"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 1, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Restriction analysis: high severity restrictions", "value": "Methods: pointer-events-none, Affected elements: 11, Bypass mechanisms: 0", "severity": "error", "elementCount": 1, "affectedSelectors": ["Methods", "pointer-events-none", "Affected", "elements", "Bypass", "mechanisms"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 2, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Multi-modal interaction testing: poor rating", "value": "Touch: false, Mouse: true, Keyboard: true, Pointer: true, Concurrent: false", "severity": "error", "elementCount": 1, "affectedSelectors": ["Touch", "false", "Mouse", "true", "Keyboard", "Pointer", "Concurrent"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 3, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 1 needs improvement", "value": "button:nth-of-type(1) - supported methods: 1, accessible: Use Cases", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "Use", "Cases"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 4, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 2 needs improvement", "value": "button:nth-of-type(2) - supported methods: 1, accessible: Resources", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "Resources"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 5, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 3 needs improvement", "value": "button:nth-of-type(3) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 6, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 4 needs improvement", "value": "input:nth-of-type(4) - supported methods: 1, accessible: false", "severity": "warning", "elementCount": 1, "affectedSelectors": ["input", "nth-of-type", "supported", "methods", "accessible", "false"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 7, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 5 needs improvement", "value": "button:nth-of-type(5) - supported methods: 1, accessible: Scan now", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "<PERSON><PERSON>", "now"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 8, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 6 needs improvement", "value": "button:nth-of-type(6) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 9, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 7 needs improvement", "value": "button:nth-of-type(7) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 10, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 8 needs improvement", "value": "button:nth-of-type(8) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 11, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 9 needs improvement", "value": "button:nth-of-type(9) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 12, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 10 needs improvement", "value": "button:nth-of-type(10) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 13, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 11 needs improvement", "value": "button:nth-of-type(11) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 14, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 12 needs improvement", "value": "button:nth-of-type(12) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 15, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 13 needs improvement", "value": "button:nth-of-type(13) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 67, "elementsAnalyzed": 17, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 16, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T11:39:08.098Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752233948098, "hash": "48f5a5a0267c4b14851ccb329df7996a", "accessCount": 1, "lastAccessed": 1752233948098, "size": 13880}
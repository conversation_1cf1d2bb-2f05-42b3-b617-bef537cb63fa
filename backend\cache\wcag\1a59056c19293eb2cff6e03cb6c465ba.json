{"data": [{"type": "info", "description": "No motion-triggered functionality detected", "value": "<PERSON> does not appear to use device motion for triggering functions", "severity": "info", "metadata": {"scanDuration": 10, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-actuation-analysis", "motionEventDetection": true, "alternativeControlValidation": true, "advancedMotionDetection": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-056", "ruleName": "Motion Actuation", "timestamp": "2025-07-11T19:24:13.172Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["Page", "does", "not", "appear", "to", "use", "device", "motion", "for", "triggering", "functions"]}], "timestamp": 1752261853172, "hash": "631eead3fe3baecbe11d948284d81678", "accessCount": 1, "lastAccessed": 1752261853172, "size": 848, "metadata": {"originalKey": "rule:WCAG-056:WCAG-056", "normalizedKey": "rule_wcag-056_wcag-056", "savedAt": 1752261853172, "version": "1.1", "keyHash": "a9eb7b592b6635e31f79cef11bcde9ef"}}
{"data": [{"type": "text", "description": "auto-updating 1 has pause/stop controls", "value": "auto-update-0 - type: auto-update, hasControls: true", "severity": "info", "elementCount": 1, "affectedSelectors": ["auto-update-0", "type", "auto-update", "hasControls", "true"], "metadata": {"scanDuration": 29, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-analysis", "mediaElementsAnalyzed": true, "animationDetection": true, "accessibilityPatterns": true, "multimediaAccessibilityTesting": true, "evidenceIndex": 0, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T11:38:19.182Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "auto-updating 2 requires pause/stop controls", "value": "auto-update-1 - type: auto-update, duration: 0s", "severity": "error", "elementCount": 1, "affectedSelectors": ["auto-update-1", "type", "auto-update", "duration", "s"], "metadata": {"scanDuration": 29, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-analysis", "mediaElementsAnalyzed": true, "animationDetection": true, "accessibilityPatterns": true, "multimediaAccessibilityTesting": true, "evidenceIndex": 1, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T11:38:19.182Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "auto-updating 3 has pause/stop controls", "value": "auto-update-2 - type: auto-update, hasControls: true", "severity": "info", "elementCount": 1, "affectedSelectors": ["auto-update-2", "type", "auto-update", "hasControls", "true"], "metadata": {"scanDuration": 29, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-analysis", "mediaElementsAnalyzed": true, "animationDetection": true, "accessibilityPatterns": true, "multimediaAccessibilityTesting": true, "evidenceIndex": 2, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T11:38:19.182Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "auto-updating 4 requires pause/stop controls", "value": "auto-update-3 - type: auto-update, duration: 0s", "severity": "error", "elementCount": 1, "affectedSelectors": ["auto-update-3", "type", "auto-update", "duration", "s"], "metadata": {"scanDuration": 29, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-analysis", "mediaElementsAnalyzed": true, "animationDetection": true, "accessibilityPatterns": true, "multimediaAccessibilityTesting": true, "evidenceIndex": 3, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T11:38:19.182Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Moving content without user controls: Carousel or slider element", "value": "Moving content detected", "selector": "ul:nth-of-type(2)", "severity": "error", "elementCount": 1, "affectedSelectors": ["ul:nth-of-type(2)", "Moving", "content", "detected"], "metadata": {"scanDuration": 29, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-analysis", "mediaElementsAnalyzed": true, "animationDetection": true, "accessibilityPatterns": true, "multimediaAccessibilityTesting": true, "evidenceIndex": 4, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T11:38:19.182Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Moving content without user controls: Carousel or slider element", "value": "Moving content detected", "selector": "ul:nth-of-type(4)", "severity": "error", "elementCount": 1, "affectedSelectors": ["ul:nth-of-type(4)", "Moving", "content", "detected"], "metadata": {"scanDuration": 29, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-analysis", "mediaElementsAnalyzed": true, "animationDetection": true, "accessibilityPatterns": true, "multimediaAccessibilityTesting": true, "evidenceIndex": 5, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T11:38:19.183Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752233899184, "hash": "78a34f4ef9647e1aa94c32ac2f94e393", "accessCount": 1, "lastAccessed": 1752233899184, "size": 4755}
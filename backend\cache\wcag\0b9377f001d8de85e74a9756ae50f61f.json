{"data": {"ruleId": "WCAG-039", "ruleName": "Images of Text", "category": "perceivable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0535, "automated": true, "evidence": [{"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding), CSS class suggests text content", "value": "Alt: \"TigerConnect-Clinical-Healthcare-Communications-Logo\" | Src: https://tigerconnect.com/wp-content/uploads/2024/01/TigerConnect-Clinical-Healthcare-Communications-", "selector": "img:nth-of-type(22)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(22)", "Alt", "TigerConnect-Clinical-Healthcare-Communications-Logo", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "TigerConnect-Clinical-Healthcare-Communications-"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 93\" | Src: https://tigerconnect.com/wp-content/uploads/2024/05/group-93.webp", "selector": "img:nth-of-type(103)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(103)", "Alt", "group", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "group-93.webp"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"CareConduit logo\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/05/CareConduit-logo.svg", "selector": "img:nth-of-type(104)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(104)", "Alt", "CareConduit", "logo", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "CareConduit-logo.svg"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Ambulance white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/02/Ambulance-white.png", "selector": "img:nth-of-type(105)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(105)", "Alt", "Ambulance", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Ambulance-white.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group message white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-message-white.png", "selector": "img:nth-of-type(106)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(106)", "Alt", "group", "message", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-message-white.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 94\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-94.webp", "selector": "img:nth-of-type(107)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(107)", "Alt", "group", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-94.webp"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Alarm Mgt 1w\" | Src: https://tigerconnect.com/wp-content/uploads/2024/07/Alarm-Mgt_1w.png", "selector": "img:nth-of-type(108)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(108)", "Alt", "Alarm", "Mgt", "w", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Alarm-Mgt_1w.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 93\" | Src: https://tigerconnect.com/wp-content/uploads/2024/05/group-93.webp", "selector": "img:nth-of-type(109)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(109)", "Alt", "group", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "group-93.webp"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"CareConduit logo\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/05/CareConduit-logo.svg", "selector": "img:nth-of-type(110)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(110)", "Alt", "CareConduit", "logo", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "CareConduit-logo.svg"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Ambulance white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/02/Ambulance-white.png", "selector": "img:nth-of-type(111)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(111)", "Alt", "Ambulance", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Ambulance-white.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group message white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-message-white.png", "selector": "img:nth-of-type(112)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(112)", "Alt", "group", "message", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-message-white.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 94\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-94.webp", "selector": "img:nth-of-type(113)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(113)", "Alt", "group", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-94.webp"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Alarm Mgt 1w\" | Src: https://tigerconnect.com/wp-content/uploads/2024/07/Alarm-Mgt_1w.png", "selector": "img:nth-of-type(114)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(114)", "Alt", "Alarm", "Mgt", "w", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Alarm-Mgt_1w.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 93\" | Src: https://tigerconnect.com/wp-content/uploads/2024/05/group-93.webp", "selector": "img:nth-of-type(115)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(115)", "Alt", "group", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "group-93.webp"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"CareConduit logo\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/05/CareConduit-logo.svg", "selector": "img:nth-of-type(116)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(116)", "Alt", "CareConduit", "logo", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "CareConduit-logo.svg"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Ambulance white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/02/Ambulance-white.png", "selector": "img:nth-of-type(117)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(117)", "Alt", "Ambulance", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Ambulance-white.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group message white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-message-white.png", "selector": "img:nth-of-type(118)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(118)", "Alt", "group", "message", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-message-white.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 94\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-94.webp", "selector": "img:nth-of-type(119)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(119)", "Alt", "group", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-94.webp"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Alarm Mgt 1w\" | Src: https://tigerconnect.com/wp-content/uploads/2024/07/Alarm-Mgt_1w.png", "selector": "img:nth-of-type(120)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(120)", "Alt", "Alarm", "Mgt", "w", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Alarm-Mgt_1w.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 93\" | Src: https://tigerconnect.com/wp-content/uploads/2024/05/group-93.webp", "selector": "img:nth-of-type(121)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(121)", "Alt", "group", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "group-93.webp"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"CareConduit logo\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/05/CareConduit-logo.svg", "selector": "img:nth-of-type(122)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(122)", "Alt", "CareConduit", "logo", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "CareConduit-logo.svg"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Ambulance white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/02/Ambulance-white.png", "selector": "img:nth-of-type(123)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(123)", "Alt", "Ambulance", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Ambulance-white.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group message white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-message-white.png", "selector": "img:nth-of-type(124)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(124)", "Alt", "group", "message", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-message-white.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Workflow Critical Response\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Critical-Response.webp", "selector": "img:nth-of-type(125)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(125)", "Alt", "Workflow", "Critical", "Response", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Critical-Response.webp"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Workflow Emergency Department\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Emergency-Department.webp", "selector": "img:nth-of-type(126)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(126)", "Alt", "Workflow", "Emergency", "Department", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Emergency-Department.webp"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Workflow Inpatient Care\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Inpatient-Care.webp", "selector": "img:nth-of-type(127)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(127)", "Alt", "Workflow", "Inpatient", "Care", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Inpatient-Care.webp"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Workflow Operating Room\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Operating-Room.webp", "selector": "img:nth-of-type(128)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(128)", "Alt", "Workflow", "Operating", "Room", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Operating-Room.webp"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Workflow Post Acute Ambulatory\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Post-Acute-Ambulatory.webp", "selector": "img:nth-of-type(129)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(129)", "Alt", "Workflow", "Post", "Acute", "Ambulatory", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Post-Acute-Ambulatory.webp"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content", "value": "Alt: \"blog header why healthcare needs purpose built collaboration solutions\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/04/blog-header-why-healthcare-needs-purpose-built", "selector": "img:nth-of-type(131)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(131)", "Alt", "blog", "header", "why", "healthcare", "needs", "purpose", "built", "collaboration", "solutions", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "blog-header-why-healthcare-needs-purpose-built"], "severity": "warning", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo Geisinger G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-Geisinger-G.png", "selector": "img:nth-of-type(135)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(135)", "Alt", "Logo", "<PERSON><PERSON><PERSON><PERSON>", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-Geisinger-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo Innovation G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-Innovation-G.png", "selector": "img:nth-of-type(136)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(136)", "Alt", "Logo", "Innovation", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-Innovation-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.888Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo UMMC G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-UMMC-G.png", "selector": "img:nth-of-type(137)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(137)", "Alt", "Logo", "UMMC", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-UMMC-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"whclgoo\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/whclgoo.png", "selector": "img:nth-of-type(138)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(138)", "Alt", "whclgoo", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "whclgoo.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo CapHealth G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-CapHealth-G.png", "selector": "img:nth-of-type(139)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(139)", "Alt", "Logo", "CapHealth", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-CapHealth-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo CommonSpirit G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-CommonSpirit-G.png", "selector": "img:nth-of-type(140)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(140)", "Alt", "Logo", "CommonSpirit", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-CommonSpirit-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo Geisinger G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-Geisinger-G.png", "selector": "img:nth-of-type(141)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(141)", "Alt", "Logo", "<PERSON><PERSON><PERSON><PERSON>", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-Geisinger-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo Innovation G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-Innovation-G.png", "selector": "img:nth-of-type(142)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(142)", "Alt", "Logo", "Innovation", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-Innovation-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo UMMC G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-UMMC-G.png", "selector": "img:nth-of-type(143)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(143)", "Alt", "Logo", "UMMC", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-UMMC-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"whclgoo\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/whclgoo.png", "selector": "img:nth-of-type(144)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(144)", "Alt", "whclgoo", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "whclgoo.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo CapHealth G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-CapHealth-G.png", "selector": "img:nth-of-type(145)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(145)", "Alt", "Logo", "CapHealth", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-CapHealth-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo CommonSpirit G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-CommonSpirit-G.png", "selector": "img:nth-of-type(146)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(146)", "Alt", "Logo", "CommonSpirit", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-CommonSpirit-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo Geisinger G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-Geisinger-G.png", "selector": "img:nth-of-type(147)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(147)", "Alt", "Logo", "<PERSON><PERSON><PERSON><PERSON>", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-Geisinger-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo Innovation G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-Innovation-G.png", "selector": "img:nth-of-type(148)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(148)", "Alt", "Logo", "Innovation", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-Innovation-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo UMMC G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-UMMC-G.png", "selector": "img:nth-of-type(149)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(149)", "Alt", "Logo", "UMMC", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-UMMC-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"whclgoo\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/whclgoo.png", "selector": "img:nth-of-type(150)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(150)", "Alt", "whclgoo", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "whclgoo.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo CapHealth G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-CapHealth-G.png", "selector": "img:nth-of-type(151)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(151)", "Alt", "Logo", "CapHealth", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-CapHealth-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo CommonSpirit G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-CommonSpirit-G.png", "selector": "img:nth-of-type(152)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(152)", "Alt", "Logo", "CommonSpirit", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-CommonSpirit-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo Geisinger G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-Geisinger-G.png", "selector": "img:nth-of-type(153)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(153)", "Alt", "Logo", "<PERSON><PERSON><PERSON><PERSON>", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-Geisinger-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo Innovation G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-Innovation-G.png", "selector": "img:nth-of-type(154)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(154)", "Alt", "Logo", "Innovation", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-Innovation-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo UMMC G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-UMMC-G.png", "selector": "img:nth-of-type(155)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(155)", "Alt", "Logo", "UMMC", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-UMMC-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"whclgoo\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/whclgoo.png", "selector": "img:nth-of-type(156)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(156)", "Alt", "whclgoo", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "whclgoo.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo CapHealth G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-CapHealth-G.png", "selector": "img:nth-of-type(157)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(157)", "Alt", "Logo", "CapHealth", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-CapHealth-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo CommonSpirit G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-CommonSpirit-G.png", "selector": "img:nth-of-type(158)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(158)", "Alt", "Logo", "CommonSpirit", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-CommonSpirit-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo Geisinger G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-Geisinger-G.png", "selector": "img:nth-of-type(159)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(159)", "Alt", "Logo", "<PERSON><PERSON><PERSON><PERSON>", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-Geisinger-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo Innovation G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-Innovation-G.png", "selector": "img:nth-of-type(160)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(160)", "Alt", "Logo", "Innovation", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-Innovation-G.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Doctors with tablet\" | Src: https://tigerconnect.com/wp-content/uploads/2025/02/Doctors-with-tablet.jpg", "selector": "img:nth-of-type(165)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(165)", "Alt", "Doctors", "with", "tablet", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Doctors-with-tablet.jpg"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"PE Product SS\" | Src: https://tigerconnect.com/wp-content/uploads/2025/02/PE-Product-SS-590x1024.png", "selector": "img:nth-of-type(174)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(174)", "Alt", "PE", "Product", "SS", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "PE-Product-SS-590x1024.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Users Physicians\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Users-Physicians.webp", "selector": "img:nth-of-type(175)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(175)", "Alt", "Users", "Physicians", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Users-Physicians.webp"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Users IT\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Users-IT.webp", "selector": "img:nth-of-type(177)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(177)", "Alt", "Users", "IT", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Users-IT.webp"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Users Nurse1\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Users-Nurse1.webp", "selector": "img:nth-of-type(179)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(179)", "Alt", "Users", "Nurse1", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Users-Nurse1.webp"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"ICO\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/ICO-itex2.png", "selector": "img:nth-of-type(181)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(181)", "Alt", "ICO", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "ICO-itex2.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"2024 best in klas recognition\" | Src: https://tigerconnect.com/wp-content/uploads/2025/01/2024-best-in-klas-recognition.png", "selector": "img:nth-of-type(185)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(185)", "Alt", "best", "in", "klas", "recognition", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "best-in-klas-recognition.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"g2 logo png\" | Src: https://tigerconnect.com/wp-content/uploads/2025/01/g2-logo-png.png", "selector": "img:nth-of-type(186)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(186)", "Alt", "g2", "logo", "png", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "g2-logo-png.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Gartner logo blue small digital\" | Src: https://tigerconnect.com/wp-content/uploads/2025/02/Gartner_logo_blue_small_digital.png", "selector": "img:nth-of-type(187)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(187)", "Alt", "<PERSON><PERSON><PERSON>", "logo", "blue", "small", "digital", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Gartner_logo_blue_small_digital.png"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"RSC 04\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/RSC-04.png", "selector": "img:nth-of-type(188)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(188)", "Alt", "RSC", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "RSC-04.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 64, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Webinar Preview Transform Pre Hospital and Transfer Communication\" | Src: https://tigerconnect.com/wp-content/uploads/2025/02/Webinar-Preview-Transform-Pre-Hospital-and-Trans", "selector": "img:nth-of-type(189)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(189)", "Alt", "Webinar", "Preview", "Transform", "Pre", "Hospital", "and", "Transfer", "Communication", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Webinar-Preview-Transform-Pre-Hospital-and-Trans"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 65, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"resources inforgraphic\" | Src: https://tigerconnect.com/wp-content/uploads/2024/03/resources-inforgraphic.png", "selector": "img:nth-of-type(190)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(190)", "Alt", "resources", "inforgraphic", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "resources-inforgraphic.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 66, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Case Study Tufts Medical Center Preview\" | Src: https://tigerconnect.com/wp-content/uploads/2024/12/Case-Study-Tufts-Medical-Center-Preview.png", "selector": "img:nth-of-type(191)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(191)", "Alt", "Case", "Study", "Tufts", "Medical", "Center", "Preview", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Case-Study-Tufts-Medical-Center-Preview.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 67, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"RSC 02\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/RSC-02.png", "selector": "img:nth-of-type(192)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(192)", "Alt", "RSC", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "RSC-02.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 68, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Blog Expanding Care Beyong Hospital Walls Preview\" | Src: https://tigerconnect.com/wp-content/uploads/2025/02/Blog-Expanding-Care-Beyong-Hospital-Walls-Previe", "selector": "img:nth-of-type(193)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(193)", "Alt", "Blog", "Expanding", "Care", "<PERSON><PERSON>", "Hospital", "Walls", "Preview", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Blog-Expanding-Care-Beyong-Hospital-Walls-Previe"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 69, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.889Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Beckers 2024 report state of healthcare collaboration how communication inefficiencies impact clinic\" | Src: https://tigerconnect.com/wp-content/uploads/2024/08/Beckers-2024-report-state-of-healthcare-collabor", "selector": "img:nth-of-type(196)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(196)", "Alt", "<PERSON><PERSON>", "report", "state", "of", "healthcare", "collaboration", "how", "communication", "inefficiencies", "impact", "clinic", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Beckers-2024-report-state-of-healthcare-collabor"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 70, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.890Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"rg\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/rg.png", "selector": "img:nth-of-type(198)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(198)", "Alt", "rg", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "rg.png"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 71, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.890Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"TigerConnect-Clinical-Healthcare-Communications-Logo-R\" | Src: https://tigerconnect.com/wp-content/uploads/2024/01/TigerConnect-Clinical-Healthcare-Communications-", "selector": "img:nth-of-type(199)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(199)", "Alt", "TigerConnect-Clinical-Healthcare-Communications-Logo-R", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "TigerConnect-Clinical-Healthcare-Communications-"], "severity": "info", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 72, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.890Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Google-Play-App-Clinical-Collaboration-Apple-App-TigerConnect\" | Src: https://tigerconnect.com/wp-content/uploads/2024/01/Clinical-Collaboration-Apple-App-TigerConnect.we", "selector": "img:nth-of-type(227)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(227)", "Alt", "Google-Play-App-Clinical-Collaboration-Apple-App-TigerConnect", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Clinical-Collaboration-Apple-App-TigerConnect.we"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 73, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.890Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Apple-App-Clinical-Collaboration-Google-App-TigerConnect\" | Src: https://tigerconnect.com/wp-content/uploads/2024/01/Clinical-Collaboration-Google-App-TigerConnect.w", "selector": "img:nth-of-type(228)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(228)", "Alt", "Apple-App-Clinical-Collaboration-Google-App-TigerConnect", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Clinical-Collaboration-Google-App-TigerConnect.w"], "severity": "error", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 74, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.890Z"}}}, {"type": "code", "description": "Background image may contain text", "value": "Background: url(\"https://tigerconnect.com/wp-content/plugins/add-search-to-menu-premium/public/images/spinner.gi", "selector": "span:nth-of-type(225)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(225)", "Background", "url", "https", "tigerconnect.com", "wp-content", "plugins", "add-search-to-menu-premium", "public", "images", "spinner.gi"], "severity": "warning", "metadata": {"scanDuration": 359, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 75, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T20:07:52.890Z"}}}], "recommendations": ["Use actual HTML text instead of images of text when possible", "Reserve images of text for logos, branding, or essential visual presentation", "Provide text alternatives that include the same text as the image", "Use CSS for visual text styling instead of images", "Ensure images of text can be resized without loss of quality"], "executionTime": 18, "originalScore": 0, "thresholdApplied": 75, "scoringDetails": "0.0% (threshold: 75%) - FAILED"}, "timestamp": 1752264472890, "hash": "5f544ea4126dbdecb3ec77f143cc92f2", "accessCount": 1, "lastAccessed": 1752264472890, "size": 55064, "metadata": {"originalKey": "WCAG-039:053b13d2:add92319", "normalizedKey": "wcag-039_053b13d2_add92319", "savedAt": 1752264472891, "version": "1.1", "keyHash": "81573dd5700f2f6139531a65a0234736"}}
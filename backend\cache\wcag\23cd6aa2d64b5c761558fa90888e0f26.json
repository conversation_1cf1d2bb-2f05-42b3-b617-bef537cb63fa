{"data": [{"type": "text", "description": "Reading level analysis", "value": "Overall reading level: College (13th-16th grade) (Grade 15.8)", "elementCount": 1, "affectedSelectors": ["Overall", "reading", "level", "College", "th-16th", "grade", "Grade"], "severity": "warning", "metadata": {"scanDuration": 17, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.55, "checkType": "reading-level-analysis", "textComplexityAnalysis": true, "readabilityScoring": true, "aiContentAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T11:39:09.315Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Complex text block (Grade 17.6)", "value": "\"HostedScanUse CasesScannersPricingResourcesBlog Log inSign upTrusted vulnerability scanswithout the hassleScan your websites, servers, networks, and APIs.View dashboards, get threat alerts, and genera...\"", "selector": "#__next", "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.55, "checkType": "reading-level-analysis", "textComplexityAnalysis": true, "readabilityScoring": true, "aiContentAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T11:39:09.315Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "HostedScanUse", "CasesScannersPricingResourcesBlog", "Log", "inSign", "upTrusted", "vulnerability", "scanswithout", "the", "hassleScan", "your", "websites", "servers", "networks", "and", "APIs.View", "dashboards", "get", "threat", "alerts", "genera"]}, {"type": "text", "description": "Complex text block (Grade 17.6)", "value": "\"HostedScanUse CasesScannersPricingResourcesBlog Log inSign upTrusted vulnerability scanswithout the hassleScan your websites, servers, networks, and APIs.View dashboards, get threat alerts, and genera...\"", "selector": ".relative", "severity": "error", "metadata": {"scanDuration": 17, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.55, "checkType": "reading-level-analysis", "textComplexityAnalysis": true, "readabilityScoring": true, "aiContentAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T11:39:09.315Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".relative", "HostedScanUse", "CasesScannersPricingResourcesBlog", "Log", "inSign", "upTrusted", "vulnerability", "scanswithout", "the", "hassleScan", "your", "websites", "servers", "networks", "and", "APIs.View", "dashboards", "get", "threat", "alerts", "genera"]}, {"type": "text", "description": "Complex text block (Grade 13.5)", "value": "\"Trusted vulnerability scanswithout the hassleScan your websites, servers, networks, and APIs.View dashboards, get threat alerts, and generate audit-ready reports.Run a free scanScan nowOUR CUSTOMERSPr...\"", "selector": ".flex", "severity": "warning", "metadata": {"scanDuration": 17, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.55, "checkType": "reading-level-analysis", "textComplexityAnalysis": true, "readabilityScoring": true, "aiContentAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T11:39:09.315Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".flex", "Trusted", "vulnerability", "scanswithout", "the", "hassleScan", "your", "websites", "servers", "networks", "and", "APIs.View", "dashboards", "get", "threat", "alerts", "generate", "audit-ready", "reports.Run", "a", "free", "scanScan", "nowOUR", "CUSTOMERSPr"]}, {"type": "text", "description": "Complex text block (Grade 15.5)", "value": "\"Trusted vulnerability scanswithout the hassleScan your websites, servers, networks, and APIs.View dashboards, get threat alerts, and generate audit-ready reports.Run a free scanScan nowOUR CUSTOMERSPr...\"", "selector": ".flex", "severity": "warning", "metadata": {"scanDuration": 17, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.55, "checkType": "reading-level-analysis", "textComplexityAnalysis": true, "readabilityScoring": true, "aiContentAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T11:39:09.315Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".flex", "Trusted", "vulnerability", "scanswithout", "the", "hassleScan", "your", "websites", "servers", "networks", "and", "APIs.View", "dashboards", "get", "threat", "alerts", "generate", "audit-ready", "reports.Run", "a", "free", "scanScan", "nowOUR", "CUSTOMERSPr"]}, {"type": "text", "description": "Complex text block (Grade 12.5)", "value": "\"Trusted vulnerability scanswithout the hassleScan your websites, servers, networks, and APIs.View dashboards, get threat alerts, and generate audit-ready reports.Run a free scanScan now\"", "selector": ".w-full", "severity": "warning", "metadata": {"scanDuration": 17, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.55, "checkType": "reading-level-analysis", "textComplexityAnalysis": true, "readabilityScoring": true, "aiContentAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-062", "ruleName": "Reading Level", "timestamp": "2025-07-11T11:39:09.315Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": [".w-full", "Trusted", "vulnerability", "scanswithout", "the", "hassleScan", "your", "websites", "servers", "networks", "and", "APIs.View", "dashboards", "get", "threat", "alerts", "generate", "audit-ready", "reports.Run", "a", "free", "scanScan", "now"]}], "timestamp": 1752233949315, "hash": "993ca41abc40b36e7ee7f2268bdb205f", "accessCount": 1, "lastAccessed": 1752233949315, "size": 6422}
{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "metadata": {"scanDuration": 231, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.7, "checkType": "pointer-interaction-analysis", "pointerEventTesting": true, "cancellationMechanisms": true, "advancedPointerTracking": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-054", "ruleName": "Pointer Cancellation", "timestamp": "2025-07-11T18:03:24.786Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752257004786, "hash": "886704f321436e7197717ad1fb09cd7e", "accessCount": 1, "lastAccessed": 1752257004786, "size": 833, "metadata": {"originalKey": "WCAG-054:WCAG-054:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-054_wcag-054_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752257004787, "version": "1.1", "keyHash": "00371d4b30fdf374654a2093c5ea6681"}}
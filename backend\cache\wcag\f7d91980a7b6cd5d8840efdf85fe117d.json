{"data": [{"type": "info", "description": "Advanced navigation structure analysis for multiple ways validation", "element": "navigation-elements", "value": "{\"overallScore\":80,\"criticalIssues\":[],\"recommendations\":[\"Increase target sizes to meet WCAG minimum requirements\"],\"performanceMetrics\":{\"analysisTime\":693,\"elementsAnalyzed\":1000,\"breakpointsTested\":5}}", "severity": "info", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "recommendations", "Increase", "target", "sizes", "to", "meet", "WCAG", "minimum", "requirements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "breakpointsTested"], "metadata": {"scanDuration": 711, "elementsAnalyzed": 4, "checkSpecificData": {"automationRate": 0.75, "checkType": "navigation-method-analysis", "navigationAnalysis": true, "methodDetection": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-035", "ruleName": "Multiple Ways", "timestamp": "2025-07-11T11:39:47.683Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "error", "description": "Insufficient navigation methods available", "value": "Found 1 method(s), need at least 2 ways to locate pages", "selector": "body", "elementCount": 1, "affectedSelectors": ["body", "Found", "method", "s", "need", "at", "least", "ways", "to", "locate", "pages"], "severity": "error", "metadata": {"scanDuration": 711, "elementsAnalyzed": 4, "checkSpecificData": {"automationRate": 0.75, "checkType": "navigation-method-analysis", "navigationAnalysis": true, "methodDetection": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 1, "ruleId": "WCAG-035", "ruleName": "Multiple Ways", "timestamp": "2025-07-11T11:39:47.683Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "info", "description": "Navigation method found: navigation", "value": "Navigation menu or links - Quality: good", "selector": "nav, [role=\"navigation\"]", "elementCount": 2, "affectedSelectors": ["nav", "[role=\"navigation\"]", "Navigation", "menu", "or", "links", "Quality", "good"], "severity": "info", "metadata": {"scanDuration": 711, "elementsAnalyzed": 4, "checkSpecificData": {"automationRate": 0.75, "checkType": "navigation-method-analysis", "navigationAnalysis": true, "methodDetection": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 2, "ruleId": "WCAG-035", "ruleName": "Multiple Ways", "timestamp": "2025-07-11T11:39:47.683Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "warning", "description": "Multiple ways navigation analysis summary", "value": "1 navigation methods found: navigation", "selector": "nav, form, a", "elementCount": 3, "affectedSelectors": ["nav", "form", "a", "navigation", "methods", "found"], "severity": "warning", "metadata": {"scanDuration": 711, "elementsAnalyzed": 4, "checkSpecificData": {"automationRate": 0.75, "checkType": "navigation-method-analysis", "navigationAnalysis": true, "methodDetection": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 3, "ruleId": "WCAG-035", "ruleName": "Multiple Ways", "timestamp": "2025-07-11T11:39:47.683Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752233987683, "hash": "b94278ea58b96b0ae2d5171c63c524db", "accessCount": 1, "lastAccessed": 1752233987683, "size": 3585}
{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame 'F50C781F86D8B2916ACE7B01A295A631'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "F50C781F86D8B2916ACE7B01A295A631"], "metadata": {"scanDuration": 2, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.8, "checkType": "language-detection-analysis", "languagePatternAnalysis": true, "langAttributeValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-038", "ruleName": "Language of Parts", "timestamp": "2025-07-11T20:12:02.916Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752264722917, "hash": "08b200da79f7744ce217e3aad68c0042", "accessCount": 1, "lastAccessed": 1752264722917, "size": 830, "metadata": {"originalKey": "WCAG-038:WCAG-038", "normalizedKey": "wcag-038_wcag-038", "savedAt": 1752264722917, "version": "1.1", "keyHash": "6c47ba9a25463f9fa47406d8d40b8da7"}}
{"data": {"ruleId": "WCAG-001", "ruleName": "Non-text Content", "category": "perceivable", "wcagVersion": "2.2", "successCriterion": "0.0.0", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.08, "automated": false, "evidence": [{"type": "text", "description": "Non-text content analysis summary", "value": "0/19 elements pass automated checks, 44 require manual review", "severity": "error", "elementCount": 0, "affectedSelectors": ["elements", "pass", "automated", "checks", "require", "manual", "review"], "fixExample": {"before": "<!-- Inaccessible element -->", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.199Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TigerConnect-Clinical-Healthcare-Communications-Logo\" - accuracy verification needed", "selector": "img:nth-child(1)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(1)", "Alt", "text", "present", "TigerConnect-Clinical-Healthcare-Communications-Logo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.199Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(2)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(2)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.199Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(3)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(3)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.199Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(4)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(4)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.199Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(5)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(5)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.199Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(6)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(6)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.199Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(7)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(7)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.199Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(8)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(8)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.199Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(9)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(9)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.199Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(10)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(10)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(11)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(11)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(12)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(12)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(13)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(13)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(14)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(14)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(15)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(15)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(16)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(16)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "img:nth-child(17)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(17)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TigerConnect-Clinical-Healthcare-Communications-Logo\" - accuracy verification needed", "selector": "img:nth-child(18)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(18)", "Alt", "text", "present", "TigerConnect-Clinical-Healthcare-Communications-Logo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Ambulance white\" - accuracy verification needed", "selector": "img:nth-child(19)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(19)", "Alt", "text", "present", "Ambulance", "white", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"group message white\" - accuracy verification needed", "selector": "img:nth-child(20)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(20)", "Alt", "text", "present", "group", "message", "white", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"group 94\" - accuracy verification needed", "selector": "img:nth-child(21)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(21)", "Alt", "text", "present", "group", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Alarm Mgt 1w\" - accuracy verification needed", "selector": "img:nth-child(22)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(22)", "Alt", "text", "present", "Alarm", "Mgt", "w", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"group 93\" - accuracy verification needed", "selector": "img:nth-child(23)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(23)", "Alt", "text", "present", "group", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"CareConduit logo\" - accuracy verification needed", "selector": "img:nth-child(24)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(24)", "Alt", "text", "present", "CareConduit", "logo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Critical Response\" - accuracy verification needed", "selector": "img:nth-child(25)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(25)", "Alt", "text", "present", "Workflow", "Critical", "Response", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Emergency Department\" - accuracy verification needed", "selector": "img:nth-child(26)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(26)", "Alt", "text", "present", "Workflow", "Emergency", "Department", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Inpatient Care\" - accuracy verification needed", "selector": "img:nth-child(27)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(27)", "Alt", "text", "present", "Workflow", "Inpatient", "Care", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Operating Room\" - accuracy verification needed", "selector": "img:nth-child(28)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(28)", "Alt", "text", "present", "Workflow", "Operating", "Room", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Workflow Post Acute Ambulatory\" - accuracy verification needed", "selector": "img:nth-child(29)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(29)", "Alt", "text", "present", "Workflow", "Post", "Acute", "Ambulatory", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"blog header why healthcare needs purpose built collaboration solutions\" - accuracy verification needed", "selector": "img:nth-child(30)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(30)", "Alt", "text", "present", "blog", "header", "why", "healthcare", "needs", "purpose", "built", "collaboration", "solutions", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo CapHealth G\" - accuracy verification needed", "selector": "img:nth-child(31)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(31)", "Alt", "text", "present", "Logo", "CapHealth", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo CommonSpirit G\" - accuracy verification needed", "selector": "img:nth-child(32)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(32)", "Alt", "text", "present", "Logo", "CommonSpirit", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo Geisinger G\" - accuracy verification needed", "selector": "img:nth-child(33)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(33)", "Alt", "text", "present", "Logo", "<PERSON><PERSON><PERSON><PERSON>", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.200Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo Innovation G\" - accuracy verification needed", "selector": "img:nth-child(34)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(34)", "Alt", "text", "present", "Logo", "Innovation", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Logo UMMC G\" - accuracy verification needed", "selector": "img:nth-child(35)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(35)", "Alt", "text", "present", "Logo", "UMMC", "G", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"whclgoo\" - accuracy verification needed", "selector": "img:nth-child(36)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(36)", "Alt", "text", "present", "whclgoo", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Doctors with tablet\" - accuracy verification needed", "selector": "img:nth-child(37)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(37)", "Alt", "text", "present", "Doctors", "with", "tablet", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Homepage SS 2\" - accuracy verification needed", "selector": "img:nth-child(38)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(38)", "Alt", "text", "present", "Homepage", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Physician Scheduling Product SS\" - accuracy verification needed", "selector": "img:nth-child(39)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(39)", "Alt", "text", "present", "Physician", "Scheduling", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Clinical Collaboration Product SS\" - accuracy verification needed", "selector": "img:nth-child(40)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(40)", "Alt", "text", "present", "Clinical", "Collaboration", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"AMEN Product SS\" - accuracy verification needed", "selector": "img:nth-child(41)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(41)", "Alt", "text", "present", "AMEN", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"PE Product SS\" - accuracy verification needed", "selector": "img:nth-child(42)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(42)", "Alt", "text", "present", "PE", "Product", "SS", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Users Physicians\" - accuracy verification needed", "selector": "img:nth-child(43)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(43)", "Alt", "text", "present", "Users", "Physicians", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Users IT\" - accuracy verification needed", "selector": "img:nth-child(44)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(44)", "Alt", "text", "present", "Users", "IT", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Users Nurse1\" - accuracy verification needed", "selector": "img:nth-child(45)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(45)", "Alt", "text", "present", "Users", "Nurse1", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"ICO\" - accuracy verification needed", "selector": "img:nth-child(46)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(46)", "Alt", "text", "present", "ICO", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"2024 best in klas recognition\" - accuracy verification needed", "selector": "img:nth-child(47)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(47)", "Alt", "text", "present", "best", "in", "klas", "recognition", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"g2 logo png\" - accuracy verification needed", "selector": "img:nth-child(48)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(48)", "Alt", "text", "present", "g2", "logo", "png", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"<PERSON><PERSON>ner logo blue small digital\" - accuracy verification needed", "selector": "img:nth-child(49)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(49)", "Alt", "text", "present", "<PERSON><PERSON><PERSON>", "logo", "blue", "small", "digital", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"RSC 04\" - accuracy verification needed", "selector": "img:nth-child(50)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(50)", "Alt", "text", "present", "RSC", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Webinar Preview Transform Pre Hospital and Transfer Communication\" - accuracy verification needed", "selector": "img:nth-child(51)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(51)", "Alt", "text", "present", "Webinar", "Preview", "Transform", "Pre", "Hospital", "and", "Transfer", "Communication", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"resources inforgraphic\" - accuracy verification needed", "selector": "img:nth-child(52)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(52)", "Alt", "text", "present", "resources", "inforgraphic", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Case Study Tufts Medical Center Preview\" - accuracy verification needed", "selector": "img:nth-child(53)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(53)", "Alt", "text", "present", "Case", "Study", "Tufts", "Medical", "Center", "Preview", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"RSC 02\" - accuracy verification needed", "selector": "img:nth-child(54)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(54)", "Alt", "text", "present", "RSC", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Blog Expanding Care Beyong Hospital Walls Preview\" - accuracy verification needed", "selector": "img:nth-child(55)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(55)", "Alt", "text", "present", "Blog", "Expanding", "Care", "<PERSON><PERSON>", "Hospital", "Walls", "Preview", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.201Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Beckers 2024 report state of healthcare collaboration how communication inefficiencies impact clinician productivity 11.08.57 AM 1\" - accuracy verification needed", "selector": "img:nth-child(56)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(56)", "Alt", "text", "present", "<PERSON><PERSON>", "report", "state", "of", "healthcare", "collaboration", "how", "communication", "inefficiencies", "impact", "clinician", "productivity", "AM", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.202Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Alt text too short to be descriptive", "selector": "img:nth-child(57)", "severity": "error", "elementCount": 1, "affectedSelectors": ["img:nth-child(57)", "Alt", "text", "too", "short", "to", "be", "descriptive"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.202Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TigerConnect-Clinical-Healthcare-Communications-Logo-R\" - accuracy verification needed", "selector": "img:nth-child(58)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(58)", "Alt", "text", "present", "TigerConnect-Clinical-Healthcare-Communications-Logo-R", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.202Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Google-Play-App-Clinical-Collaboration-Apple-App-TigerConnect\" - accuracy verification needed", "selector": "img:nth-child(59)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(59)", "Alt", "text", "present", "Google-Play-App-Clinical-Collaboration-Apple-App-TigerConnect", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.202Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Apple-App-Clinical-Collaboration-Google-App-TigerConnect\" - accuracy verification needed", "selector": "img:nth-child(60)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-child(60)", "Alt", "text", "present", "Apple-App-Clinical-Collaboration-Google-App-TigerConnect", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.202Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Cookiebot session tracker icon loaded\" - accuracy verification needed", "selector": "#CookiebotSessionPixel", "severity": "warning", "elementCount": 1, "affectedSelectors": ["#CookiebotSessionPixel", "Alt", "text", "present", "<PERSON><PERSON><PERSON>", "session", "tracker", "icon", "loaded", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.202Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "iframe:nth-child(1)", "severity": "error", "elementCount": 1, "affectedSelectors": ["iframe:nth-child(1)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<iframe><!-- Missing accessibility attributes --></iframe>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.202Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "iframe:nth-child(2)", "severity": "error", "elementCount": 1, "affectedSelectors": ["iframe:nth-child(2)", "No", "alternative", "text", "provided"], "fixExample": {"before": "<iframe><!-- Missing accessibility attributes --></iframe>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 421, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T18:44:39.202Z"}}}], "recommendations": ["Add appropriate alternative text for all non-text content", "img:nth-child(2): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(3): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(4): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(5): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(6): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(7): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(8): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(9): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(10): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(11): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(12): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(13): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(14): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(15): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(16): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(17): Add descriptive alt text, aria-label, or other text alternative", "img:nth-child(57): Provide more descriptive alternative text", "iframe:nth-child(1): Add descriptive alt text, aria-label, or other text alternative", "iframe:nth-child(2): Add descriptive alt text, aria-label, or other text alternative", "Use empty alt=\"\" for decorative images", "Provide descriptive alternatives that convey the same information"], "executionTime": 0, "errorMessage": "Missing or inadequate alternative text for img:nth-child(2); Missing or inadequate alternative text for img:nth-child(3); Missing or inadequate alternative text for img:nth-child(4); Missing or inadequate alternative text for img:nth-child(5); Missing or inadequate alternative text for img:nth-child(6); Missing or inadequate alternative text for img:nth-child(7); Missing or inadequate alternative text for img:nth-child(8); Missing or inadequate alternative text for img:nth-child(9); Missing or inadequate alternative text for img:nth-child(10); Missing or inadequate alternative text for img:nth-child(11); Missing or inadequate alternative text for img:nth-child(12); Missing or inadequate alternative text for img:nth-child(13); Missing or inadequate alternative text for img:nth-child(14); Missing or inadequate alternative text for img:nth-child(15); Missing or inadequate alternative text for img:nth-child(16); Missing or inadequate alternative text for img:nth-child(17); Missing or inadequate alternative text for img:nth-child(57); Missing or inadequate alternative text for iframe:nth-child(1); Missing or inadequate alternative text for iframe:nth-child(2)", "manualReviewItems": []}, "timestamp": 1752259479202, "hash": "99d41e1b4f650319fe6d3a98d48d0190", "accessCount": 1, "lastAccessed": 1752259479202, "size": 48766, "metadata": {"originalKey": "rule:WCAG-001:053b13d2:add92319", "normalizedKey": "rule_wcag-001_053b13d2_add92319", "savedAt": 1752259479204, "version": "1.1", "keyHash": "5059d2ef34f02f1834bdefaf33e9738f"}}
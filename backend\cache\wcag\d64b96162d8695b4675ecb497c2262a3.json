{"data": {"ruleId": "WCAG-053", "ruleName": "Pointer Gestures", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "passed", "score": 100, "maxScore": 100, "weight": 0.0458, "automated": true, "evidence": [{"type": "text", "description": "Gesture pattern detection: Low or no accessibility risk", "value": "Risk level: none, Confidence: 90.0%", "severity": "info", "elementCount": 1, "affectedSelectors": ["Risk", "level", "none", "Confidence"], "metadata": {"scanDuration": 1401, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-053", "ruleName": "Pointer Gestures", "timestamp": "2025-07-11T11:38:57.817Z"}}}, {"type": "text", "description": "Alternative input validation: Sufficient alternatives available", "value": "Keyboard: true, Single-point: true, But<PERSON>: true", "severity": "info", "elementCount": 1, "affectedSelectors": ["Keyboard", "true", "Single-point", "<PERSON><PERSON>"], "metadata": {"scanDuration": 1401, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-053", "ruleName": "Pointer Gestures", "timestamp": "2025-07-11T11:38:57.818Z"}}}, {"type": "text", "description": "Gesture complexity analysis: Good accessibility rating", "value": "Rating: excellent, Complexity score: 0.0%", "severity": "info", "elementCount": 1, "affectedSelectors": ["Rating", "excellent", "Complexity", "score"], "metadata": {"scanDuration": 1401, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-053", "ruleName": "Pointer Gestures", "timestamp": "2025-07-11T11:38:57.818Z"}}}], "recommendations": [], "executionTime": 24, "originalScore": 100}, "timestamp": 1752233937818, "hash": "59992aff45f6c44ca744a8dbf1e19ea8", "accessCount": 1, "lastAccessed": 1752233937818, "size": 1714}
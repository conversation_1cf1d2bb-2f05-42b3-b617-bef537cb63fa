{"data": {"ruleId": "WCAG-029", "ruleName": "Page Titled", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "passed", "score": 90, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "info", "description": "Page has descriptive title", "value": "<title>#1 Healthcare Collaboration Platform | TigerConnect</title>", "selector": "title", "elementCount": 1, "affectedSelectors": ["title", "Healthcare", "Collaboration", "Platform", "TigerConnect"], "severity": "info", "metadata": {"scanDuration": 411, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-029", "ruleName": "Page Titled", "timestamp": "2025-07-11T20:11:40.341Z"}}}, {"type": "warning", "description": "Multiple title elements found", "value": "14 title elements detected", "selector": "title", "elementCount": 1, "affectedSelectors": ["title", "elements", "detected"], "severity": "warning", "metadata": {"scanDuration": 411, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-029", "ruleName": "Page Titled", "timestamp": "2025-07-11T20:11:40.341Z"}}}], "recommendations": ["Use exactly one title element per page", "Use descriptive titles that identify the page topic and purpose", "Include both page-specific and site context in titles", "Keep titles between 10-60 characters for optimal display", "Test titles with screen readers and browser tabs"], "executionTime": 5, "originalScore": 90, "thresholdApplied": 75, "scoringDetails": "90.0% (threshold: 75%) - PASSED"}, "timestamp": 1752264700341, "hash": "ed2f34e0c4e9728119d676a0e8f483d6", "accessCount": 1, "lastAccessed": 1752264700341, "size": 1570, "metadata": {"originalKey": "WCAG-029:053b13d2:add92319", "normalizedKey": "wcag-029_053b13d2_add92319", "savedAt": 1752264700342, "version": "1.1", "keyHash": "a2e558d259f158ebff8bfc0e8ff98394"}}
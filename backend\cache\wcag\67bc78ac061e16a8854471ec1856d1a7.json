{"data": {"ruleId": "WCAG-014", "ruleName": "Target Size", "category": "operable", "wcagVersion": "2.2", "successCriterion": "2.5.8", "level": "AA", "status": "passed", "score": 100, "maxScore": 100, "weight": 0.08, "automated": true, "evidence": [{"type": "text", "description": "Target size analysis summary", "value": "74/74 interactive targets meet size requirements", "severity": "info", "elementCount": 1, "affectedSelectors": ["interactive", "targets", "meet", "size", "requirements"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.873Z"}}}, {"type": "measurement", "description": "Advanced target size analysis with optimization recommendations", "value": "{\"analysis\":[{\"element\":\"button\",\"width\":152.09375,\"height\":44,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":152.09375,\"height\":48},\"touchFriendly\":false},{\"element\":\"button\",\"width\":150.09375,\"height\":44,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":150.09375,\"height\":48},\"touchFriendly\":false},{\"element\":\"button\",\"width\":145.890625,\"height\":50,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":145.890625,\"height\":50},\"touchFriendly\":true},{\"element\":\"button\",\"width\":48,\"height\":48,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":true},{\"element\":\"button\",\"width\":48,\"height\":48,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":true},{\"element\":\"button\",\"width\":48,\"height\":48,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":true},{\"element\":\"button\",\"width\":48,\"height\":48,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":135.015625,\"height\":28,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":135.015625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":111.65625,\"height\":44,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":111.65625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":90.171875,\"height\":44,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":90.171875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":69.828125,\"height\":44,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":69.828125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":90.921875,\"height\":41,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":90.921875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":103.46875,\"height\":41,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":103.46875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":363.796875,\"height\":28,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":363.796875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":488.40625,\"height\":28,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":488.40625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":363.796875,\"height\":28,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":363.796875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":488.390625,\"height\":28,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":488.390625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":192,\"height\":50,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":192,\"height\":50},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":192,\"height\":50,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":192,\"height\":50},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":117.859375,\"height\":28,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":117.859375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":179.125,\"height\":28,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":179.125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":273,\"height\":24,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":273,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":273,\"height\":24,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":273,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":273,\"height\":24,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":273,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":273,\"height\":24,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":273,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":273,\"height\":24,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":273,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":273,\"height\":24,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":273,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":273,\"height\":24,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":273,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":273,\"height\":24,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":273,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":87.1875,\"height\":24,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":87.1875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":320,\"height\":350,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":320,\"height\":350},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":320,\"height\":350,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":320,\"height\":350},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":320,\"height\":350,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":320,\"height\":350},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":320,\"height\":350,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":320,\"height\":350},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":320,\"height\":350,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":320,\"height\":350},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":192,\"height\":50,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":192,\"height\":50},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":192,\"height\":50,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":192,\"height\":50},\"touchFriendly\":true},{\"element\":\"a[href]\",\"width\":223.015625,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":223.015625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":191.484375,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":191.484375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":208.421875,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":208.421875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":162.328125,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":162.328125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":160.46875,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":160.46875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":179.59375,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":179.59375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":89.203125,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":89.203125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":163.015625,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":163.015625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":86.1875,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":86.1875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":122.375,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":122.375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":174.0625,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":174.0625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":196.90625,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":196.90625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":130.0625,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":130.0625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":128.078125,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":128.078125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":93.90625,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":93.90625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":63.296875,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":63.296875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":38.046875,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":35.640625,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":25.65625,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":135.375,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":135.375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":188.3125,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":188.3125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":161.515625,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":161.515625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":148.625,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":148.625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":157.125,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":157.125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":109.078125,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":109.078125,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":197,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":197,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":195.15625,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":195.15625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":116.1875,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":116.1875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":194.640625,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":194.640625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":163.46875,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":163.46875,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":155.265625,\"height\":19,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":155.265625,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":38,\"height\":38,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":38,\"height\":38,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":48,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":221.4375,\"height\":20,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":221.4375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":221.4375,\"height\":20,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":221.4375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":221.4375,\"height\":20,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":221.4375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":221.4375,\"height\":20,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":221.4375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":221.4375,\"height\":20,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":221.4375,\"height\":48},\"touchFriendly\":false},{\"element\":\"a[href]\",\"width\":221.4375,\"height\":40,\"meetsMinimum\":false,\"recommendedSize\":{\"width\":221.4375,\"height\":48},\"touchFriendly\":false},{\"element\":\"input:not([type=\\\"hidden\\\"])\",\"width\":490,\"height\":58,\"meetsMinimum\":true,\"recommendedSize\":{\"width\":490,\"height\":58},\"touchFriendly\":true}],\"optimization\":{\"totalTargets\":77,\"compliantTargets\":20,\"complianceRate\":25.97402597402597,\"optimizationSuggestions\":[{\"selector\":\"a[href]\",\"currentSize\":{\"width\":135.015625,\"height\":28},\"suggestedSize\":{\"width\":135.015625,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 135.015625x28px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":90.921875,\"height\":41},\"suggestedSize\":{\"width\":90.921875,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 90.921875x41px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":103.46875,\"height\":41},\"suggestedSize\":{\"width\":103.46875,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 103.46875x41px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":363.796875,\"height\":28},\"suggestedSize\":{\"width\":363.796875,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 363.796875x28px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":488.40625,\"height\":28},\"suggestedSize\":{\"width\":488.40625,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 488.40625x28px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":363.796875,\"height\":28},\"suggestedSize\":{\"width\":363.796875,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 363.796875x28px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":488.390625,\"height\":28},\"suggestedSize\":{\"width\":488.390625,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 488.390625x28px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":117.859375,\"height\":28},\"suggestedSize\":{\"width\":117.859375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 117.859375x28px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":179.125,\"height\":28},\"suggestedSize\":{\"width\":179.125,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 179.125x28px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":273,\"height\":24},\"suggestedSize\":{\"width\":273,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 273x24px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":273,\"height\":24},\"suggestedSize\":{\"width\":273,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 273x24px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":273,\"height\":24},\"suggestedSize\":{\"width\":273,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 273x24px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":273,\"height\":24},\"suggestedSize\":{\"width\":273,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 273x24px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":273,\"height\":24},\"suggestedSize\":{\"width\":273,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 273x24px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":273,\"height\":24},\"suggestedSize\":{\"width\":273,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 273x24px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":273,\"height\":24},\"suggestedSize\":{\"width\":273,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 273x24px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":273,\"height\":24},\"suggestedSize\":{\"width\":273,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 273x24px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":87.1875,\"height\":24},\"suggestedSize\":{\"width\":87.1875,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 87.1875x24px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":223.015625,\"height\":19},\"suggestedSize\":{\"width\":223.015625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 223.015625x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":191.484375,\"height\":19},\"suggestedSize\":{\"width\":191.484375,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 191.484375x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":208.421875,\"height\":19},\"suggestedSize\":{\"width\":208.421875,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 208.421875x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":162.328125,\"height\":19},\"suggestedSize\":{\"width\":162.328125,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 162.328125x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":160.46875,\"height\":19},\"suggestedSize\":{\"width\":160.46875,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 160.46875x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":179.59375,\"height\":19},\"suggestedSize\":{\"width\":179.59375,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 179.59375x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":89.203125,\"height\":19},\"suggestedSize\":{\"width\":89.203125,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 89.203125x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":163.015625,\"height\":19},\"suggestedSize\":{\"width\":163.015625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 163.015625x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":86.1875,\"height\":19},\"suggestedSize\":{\"width\":86.1875,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 86.1875x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":122.375,\"height\":19},\"suggestedSize\":{\"width\":122.375,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 122.375x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":174.0625,\"height\":19},\"suggestedSize\":{\"width\":174.0625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 174.0625x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":196.90625,\"height\":19},\"suggestedSize\":{\"width\":196.90625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 196.90625x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":130.0625,\"height\":19},\"suggestedSize\":{\"width\":130.0625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 130.0625x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":128.078125,\"height\":19},\"suggestedSize\":{\"width\":128.078125,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 128.078125x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":93.90625,\"height\":19},\"suggestedSize\":{\"width\":93.90625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 93.90625x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":63.296875,\"height\":19},\"suggestedSize\":{\"width\":63.296875,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 63.296875x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":38.046875,\"height\":19},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 38.046875x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":35.640625,\"height\":19},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 35.640625x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":25.65625,\"height\":19},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 25.65625x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":135.375,\"height\":19},\"suggestedSize\":{\"width\":135.375,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 135.375x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":188.3125,\"height\":19},\"suggestedSize\":{\"width\":188.3125,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 188.3125x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":161.515625,\"height\":19},\"suggestedSize\":{\"width\":161.515625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 161.515625x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":148.625,\"height\":19},\"suggestedSize\":{\"width\":148.625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 148.625x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":157.125,\"height\":19},\"suggestedSize\":{\"width\":157.125,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 157.125x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":109.078125,\"height\":19},\"suggestedSize\":{\"width\":109.078125,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 109.078125x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":197,\"height\":19},\"suggestedSize\":{\"width\":197,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 197x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":195.15625,\"height\":19},\"suggestedSize\":{\"width\":195.15625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 195.15625x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":116.1875,\"height\":19},\"suggestedSize\":{\"width\":116.1875,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 116.1875x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":194.640625,\"height\":19},\"suggestedSize\":{\"width\":194.640625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 194.640625x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":163.46875,\"height\":19},\"suggestedSize\":{\"width\":163.46875,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 163.46875x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":155.265625,\"height\":19},\"suggestedSize\":{\"width\":155.265625,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 155.265625x19px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":38,\"height\":38},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 38x38px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":38,\"height\":38},\"suggestedSize\":{\"width\":48,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 38x38px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":221.4375,\"height\":20},\"suggestedSize\":{\"width\":221.4375,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 221.4375x20px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":221.4375,\"height\":20},\"suggestedSize\":{\"width\":221.4375,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 221.4375x20px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":221.4375,\"height\":20},\"suggestedSize\":{\"width\":221.4375,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 221.4375x20px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":221.4375,\"height\":20},\"suggestedSize\":{\"width\":221.4375,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 221.4375x20px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":221.4375,\"height\":20},\"suggestedSize\":{\"width\":221.4375,\"height\":48},\"priority\":\"high\",\"reason\":\"Current size 221.4375x20px is below WCAG minimum of 44x44px\"},{\"selector\":\"a[href]\",\"currentSize\":{\"width\":221.4375,\"height\":40},\"suggestedSize\":{\"width\":221.4375,\"height\":48},\"priority\":\"medium\",\"reason\":\"Current size 221.4375x40px is below WCAG minimum of 44x44px\"}]},\"thirdPartyEnhanced\":false}", "severity": "error", "elementCount": 1, "affectedSelectors": ["analysis", "element", "button", "width", "height", "meetsMinimum", "true", "recommendedSize", "touchFriendly", "false", "a[href]", "input", "not", "type", "hidden", "optimization", "totalTargets", "compliantTargets", "complianceRate", "optimizationSuggestions", "selector", "currentSize", "suggestedSize", "priority", "medium", "reason", "Current", "size", "x28px", "is", "below", "WCAG", "minimum", "of", "x44px", "x41px", "x24px", "high", "x19px", "x38px", "x20px", "x40px", "thirdPartyEnhanced"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 152x44px ", "selector": "#headlessui-menu-button-:Rspmm:", "severity": "info", "elementCount": 1, "affectedSelectors": ["#headlessui-menu-button-:Rspmm:", "Size", "x44px"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 150x44px ", "selector": "#headlessui-menu-button-:Ru9mm:", "severity": "info", "elementCount": 1, "affectedSelectors": ["#headlessui-menu-button-:Ru9mm:", "Size", "x44px"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 146x50px (adequate spacing)", "selector": "button.right-1.flex.w-full.flex-row.items-center.justify-center.whitespace-nowrap.rounded-lg.border-[0.5px].border-green-highlight-dark.bg-green-highlight.px-4.py-3.font-formadjrtext.text-base.font-bold.leading-5.tracking-[0.4px].text-dark-blue.sm:absolute.sm:w-auto.sm:rounded", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.right-1.flex.w-full.flex-row.items-center.justify-center.whitespace-nowrap.rounded-lg.border-[0.5px].border-green-highlight-dark.bg-green-highlight.px-4.py-3.font-formadjrtext.text-base.font-bold.leading-5.tracking-[0.4px].text-dark-blue.sm:absolute.sm:w-auto.sm:rounded", "Size", "x50px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 48x48px (adequate spacing)", "selector": "button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "Size", "x48px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 48x48px (adequate spacing)", "selector": "button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "Size", "x48px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 135x28px (adequate spacing)", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Size", "x28px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 112x44px ", "selector": "a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "Size", "x44px"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 90x44px ", "selector": "a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "Size", "x44px"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 70x44px ", "selector": "a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "Size", "x44px"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 91x41px (adequate spacing)", "selector": "a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.order-[0.5px].hidden.flex-none.rounded-md.border-white/50.bg-white.!text-dark-blue.sm:block.text-base.font-bold", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.order-[0.5px].hidden.flex-none.rounded-md.border-white/50.bg-white.!text-dark-blue.sm:block.text-base.font-bold", "Size", "x41px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 103x41px (adequate spacing)", "selector": "a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.hidden.flex-none.whitespace-nowrap.rounded-md.border-[0.5px].border-dark-blue/75.bg-dark-blue.font-formadjrtext.tracking-[0.45px].text-white.shadow-[0_4px_16px_0_rgba(0,0,0,0.25)].sm:block.text-base.font-bold", "severity": "info", "elementCount": 4, "affectedSelectors": ["a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.hidden.flex-none.whitespace-nowrap.rounded-md.border-[0.5px].border-dark-blue/75.bg-dark-blue.font-formadjrtext.tracking-[0.45px].text-white.shadow-[0_4px_16px_0_rgba(0", "0", "0.25)].sm:block.text-base.font-bold", "Size", "x41px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 364x28px (adequate spacing)", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Size", "x28px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 488x28px (adequate spacing)", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Size", "x28px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 364x28px (adequate spacing)", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Size", "x28px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 488x28px (adequate spacing)", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Size", "x28px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 192x50px (adequate spacing)", "selector": "a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px].!bg-dark-blue.!text-white", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px].!bg-dark-blue.!text-white", "Size", "x50px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 192x50px (adequate spacing)", "selector": "#demo-account-login-button", "severity": "info", "elementCount": 1, "affectedSelectors": ["#demo-account-login-button", "Size", "x50px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 118x28px (adequate spacing)", "selector": "a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "Size", "x28px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 179x28px (adequate spacing)", "selector": "a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "Size", "x28px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 273x24px (adequate spacing)", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Size", "x24px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 273x24px (adequate spacing)", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Size", "x24px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 273x24px (adequate spacing)", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Size", "x24px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 273x24px (adequate spacing)", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Size", "x24px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 273x24px (adequate spacing)", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Size", "x24px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 273x24px (adequate spacing)", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Size", "x24px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 273x24px (adequate spacing)", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Size", "x24px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 273x24px (adequate spacing)", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Size", "x24px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 87x24px (adequate spacing)", "selector": "a.flex.flex-row.items-center.font-medium.leading-6.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.leading-6.hover:underline", "Size", "x24px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 320x350px (adequate spacing)", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Size", "x350px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 320x350px (adequate spacing)", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Size", "x350px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 320x350px (adequate spacing)", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Size", "x350px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 320x350px (adequate spacing)", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Size", "x350px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 320x350px (adequate spacing)", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Size", "x350px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 192x50px (adequate spacing)", "selector": "a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px]", "Size", "x50px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 192x50px (adequate spacing)", "selector": "#demo-account-login-button", "severity": "info", "elementCount": 1, "affectedSelectors": ["#demo-account-login-button", "Size", "x50px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 223x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 191x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 208x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 162x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 160x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 180x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 89x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 163x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 86x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 122x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.874Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 174x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 197x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 130x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 128x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 94x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 63x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 38x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 36x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 26x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 135x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 188x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 162x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 149x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 157x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 109x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 197x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 195x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 116x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 64, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 195x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 65, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 163x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 66, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 155x19px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x19px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 67, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 38x38px (adequate spacing)", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Size", "x38px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 68, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 38x38px (adequate spacing)", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Size", "x38px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 69, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 221x20px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x20px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 70, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 221x20px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x20px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 71, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 221x20px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x20px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 72, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 221x20px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x20px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 73, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 221x20px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x20px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 74, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}, {"type": "measurement", "description": "Target meets size requirements", "value": "Size: 221x40px (adequate spacing)", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Size", "x40px", "adequate", "spacing"], "metadata": {"scanDuration": 1508, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 75, "ruleId": "WCAG-014", "ruleName": "Target Size", "timestamp": "2025-07-11T11:37:53.875Z"}}}], "recommendations": ["Current size 135.015625x28px is below WCAG minimum of 44x44px", "Current size 90.921875x41px is below WCAG minimum of 44x44px", "Current size 103.46875x41px is below WCAG minimum of 44x44px", "Current size 363.796875x28px is below WCAG minimum of 44x44px", "Current size 488.40625x28px is below WCAG minimum of 44x44px", "Current size 363.796875x28px is below WCAG minimum of 44x44px", "Current size 488.390625x28px is below WCAG minimum of 44x44px", "Current size 117.859375x28px is below WCAG minimum of 44x44px", "Current size 179.125x28px is below WCAG minimum of 44x44px", "Current size 273x24px is below WCAG minimum of 44x44px", "Current size 273x24px is below WCAG minimum of 44x44px", "Current size 273x24px is below WCAG minimum of 44x44px", "Current size 273x24px is below WCAG minimum of 44x44px", "Current size 273x24px is below WCAG minimum of 44x44px", "Current size 273x24px is below WCAG minimum of 44x44px", "Current size 273x24px is below WCAG minimum of 44x44px", "Current size 273x24px is below WCAG minimum of 44x44px", "Current size 87.1875x24px is below WCAG minimum of 44x44px", "Current size 223.015625x19px is below WCAG minimum of 44x44px", "Current size 191.484375x19px is below WCAG minimum of 44x44px", "Current size 208.421875x19px is below WCAG minimum of 44x44px", "Current size 162.328125x19px is below WCAG minimum of 44x44px", "Current size 160.46875x19px is below WCAG minimum of 44x44px", "Current size 179.59375x19px is below WCAG minimum of 44x44px", "Current size 89.203125x19px is below WCAG minimum of 44x44px", "Current size 163.015625x19px is below WCAG minimum of 44x44px", "Current size 86.1875x19px is below WCAG minimum of 44x44px", "Current size 122.375x19px is below WCAG minimum of 44x44px", "Current size 174.0625x19px is below WCAG minimum of 44x44px", "Current size 196.90625x19px is below WCAG minimum of 44x44px", "Current size 130.0625x19px is below WCAG minimum of 44x44px", "Current size 128.078125x19px is below WCAG minimum of 44x44px", "Current size 93.90625x19px is below WCAG minimum of 44x44px", "Current size 63.296875x19px is below WCAG minimum of 44x44px", "Current size 38.046875x19px is below WCAG minimum of 44x44px", "Current size 35.640625x19px is below WCAG minimum of 44x44px", "Current size 25.65625x19px is below WCAG minimum of 44x44px", "Current size 135.375x19px is below WCAG minimum of 44x44px", "Current size 188.3125x19px is below WCAG minimum of 44x44px", "Current size 161.515625x19px is below WCAG minimum of 44x44px", "Current size 148.625x19px is below WCAG minimum of 44x44px", "Current size 157.125x19px is below WCAG minimum of 44x44px", "Current size 109.078125x19px is below WCAG minimum of 44x44px", "Current size 197x19px is below WCAG minimum of 44x44px", "Current size 195.15625x19px is below WCAG minimum of 44x44px", "Current size 116.1875x19px is below WCAG minimum of 44x44px", "Current size 194.640625x19px is below WCAG minimum of 44x44px", "Current size 163.46875x19px is below WCAG minimum of 44x44px", "Current size 155.265625x19px is below WCAG minimum of 44x44px", "Current size 38x38px is below WCAG minimum of 44x44px", "Current size 38x38px is below WCAG minimum of 44x44px", "Current size 221.4375x20px is below WCAG minimum of 44x44px", "Current size 221.4375x20px is below WCAG minimum of 44x44px", "Current size 221.4375x20px is below WCAG minimum of 44x44px", "Current size 221.4375x20px is below WCAG minimum of 44x44px", "Current size 221.4375x20px is below WCAG minimum of 44x44px", "Current size 221.4375x40px is below WCAG minimum of 44x44px"], "executionTime": 213, "originalScore": 100}, "timestamp": 1752233873875, "hash": "a107d3bfea58dc2c2fc16f0408602ef8", "accessCount": 1, "lastAccessed": 1752233873875, "size": 74857}
{"data": [{"type": "text", "description": "Gesture pattern detection: Low or no accessibility risk", "value": "Risk level: none, Confidence: 90.0%", "severity": "info", "elementCount": 1, "affectedSelectors": ["Risk", "level", "none", "Confidence"], "metadata": {"scanDuration": 22, "elementsAnalyzed": 3, "checkSpecificData": {"automationRate": 0.75, "checkType": "pointer-gesture-analysis", "gestureDetection": true, "alternativeInputValidation": true, "advancedGestureDetection": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-049", "ruleName": "Pointer Gestures", "timestamp": "2025-07-11T18:46:51.568Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Alternative input validation: Sufficient alternatives available", "value": "Keyboard: true, Single-point: true, But<PERSON>: true", "severity": "info", "elementCount": 1, "affectedSelectors": ["Keyboard", "true", "Single-point", "<PERSON><PERSON>"], "metadata": {"scanDuration": 22, "elementsAnalyzed": 3, "checkSpecificData": {"automationRate": 0.75, "checkType": "pointer-gesture-analysis", "gestureDetection": true, "alternativeInputValidation": true, "advancedGestureDetection": true, "accessibilityPatterns": true, "evidenceIndex": 1, "ruleId": "WCAG-049", "ruleName": "Pointer Gestures", "timestamp": "2025-07-11T18:46:51.568Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Gesture complexity analysis: Good accessibility rating", "value": "Rating: excellent, Complexity score: 0.0%", "severity": "info", "elementCount": 1, "affectedSelectors": ["Rating", "excellent", "Complexity", "score"], "metadata": {"scanDuration": 22, "elementsAnalyzed": 3, "checkSpecificData": {"automationRate": 0.75, "checkType": "pointer-gesture-analysis", "gestureDetection": true, "alternativeInputValidation": true, "advancedGestureDetection": true, "accessibilityPatterns": true, "evidenceIndex": 2, "ruleId": "WCAG-049", "ruleName": "Pointer Gestures", "timestamp": "2025-07-11T18:46:51.568Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259611569, "hash": "b3c70b721b647726cd2c16907b3ab9bf", "accessCount": 1, "lastAccessed": 1752259611569, "size": 2348, "metadata": {"originalKey": "WCAG-049:WCAG-049:dGV4dDpHZXN0dXJlIHBhdHRlcm4gZGV0", "normalizedKey": "wcag-049_wcag-049_dgv4ddphzxn0dxjlihbhdhrlcm4gzgv0", "savedAt": 1752259611569, "version": "1.1", "keyHash": "a7d28bd9a345a8a9ae3dcb8a2d747b11"}}
{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "fixExample": {"before": "Current implementation", "after": "<button tabindex=\"0\" onkeydown=\"handleKeyDown(event)\">Action</button>", "description": "Ensure keyboard accessibility", "codeExample": "<button tabindex=\"0\" onkeydown=\"handleKeyDown(event)\">Action</button>", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/keyboard.html", "https://webaim.org/techniques/keyboard/"]}, "metadata": {"scanDuration": 33, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.85, "checkType": "keyboard-interaction-analysis", "keyboardTesting": true, "focusManagement": true, "advancedKeyboardTracking": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-051", "ruleName": "Keyboard Accessible", "timestamp": "2025-07-11T18:03:19.417Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752256999418, "hash": "1bed8b99d291e17ebe4cfc75c3b86f16", "accessCount": 1, "lastAccessed": 1752256999418, "size": 1208, "metadata": {"originalKey": "WCAG-051:WCAG-051:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-051_wcag-051_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752256999418, "version": "1.1", "keyHash": "b38e4ec1222f1c2136bf377e7c01fc61"}}
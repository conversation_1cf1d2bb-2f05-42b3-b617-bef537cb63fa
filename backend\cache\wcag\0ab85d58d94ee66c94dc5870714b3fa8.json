{"data": {"ruleId": "WCAG-063", "ruleName": "Pronunciation", "category": "understandable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0305, "automated": true, "evidence": [{"type": "text", "description": "Words that may need pronunciation guidance", "value": "Found 118 words that could benefit from pronunciation guidance for AAA compliance", "elementCount": 1, "affectedSelectors": ["Found", "words", "that", "could", "benefit", "from", "pronunciation", "guidance", "for", "AAA", "compliance"], "severity": "warning", "metadata": {"scanDuration": 713, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T11:39:10.062Z"}}}, {"type": "text", "description": "Word may need pronunciation guidance: \"hosted<PERSON><PERSON>e\"", "value": "Context: \"HostedScanUse CasesScannersPricingResources\" (Category: complex-word)", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 713, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T11:39:10.062Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "HostedScanUse", "CasesScannersPricingResources", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"casesscannerspricingresourcesblog\"", "value": "Context: \"HostedScanUse CasesScannersPricingResourcesBlog Log inSign upTrusted vulnerab\" (Category: complex-word)", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 713, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T11:39:10.062Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "HostedScanUse", "CasesScannersPricingResourcesBlog", "Log", "inSign", "upTrusted", "vulnerab", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"insign\"", "value": "Context: \"nnersPricingResourcesBlog Log inSign upTrusted vulnerability scans\" (Category: complex-word)", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 713, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T11:39:10.062Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "nnersPricingResourcesBlog", "Log", "inSign", "upTrusted", "vulnerability", "scans", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"uptrusted\"", "value": "Context: \"icingResourcesBlog Log inSign upTrusted vulnerability scanswithout th\" (Category: complex-word)", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 713, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T11:39:10.062Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "icingResourcesBlog", "Log", "inSign", "upTrusted", "vulnerability", "scanswithout", "th", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"vulnerability\"", "value": "Context: \"rcesBlog Log inSign upTrusted vulnerability scanswithout the hassleScan y\" (Category: complex-word)", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 713, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T11:39:10.062Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "rcesBlog", "Log", "inSign", "upTrusted", "vulnerability", "scanswithout", "the", "hassleScan", "y", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"customerspreferred\"", "value": "Context: \"ts.Run a free scanScan nowOUR CUSTOMERSPreferred by teams who take cybersecuri\" (Category: complex-word)", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 713, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T11:39:10.062Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "ts.<PERSON>", "a", "free", "scanScan", "nowOUR", "CUSTOMERSPreferred", "by", "teams", "who", "take", "cybersecuri", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"cybersecurity\"", "value": "Context: \"RSPreferred by teams who take cybersecurity seriouslyWhat we doHostedScan\" (Category: complex-word)", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 713, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T11:39:10.062Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "RSPreferred", "by", "teams", "who", "take", "cybersecurity", "seriouslyWhat", "we", "doHostedScan", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"seriouslywhat\"", "value": "Context: \"teams who take cybersecurity seriouslyWhat we doHostedScan is simple and\" (Category: complex-word)", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 713, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T11:39:10.062Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "teams", "who", "take", "cybersecurity", "seriouslyWhat", "we", "doHostedScan", "is", "simple", "and", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"effective\"", "value": "Context: \"we doHostedScan is simple and effective. Run a wide set of industry-l\" (Category: complex-word)", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 713, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T11:39:10.062Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "we", "doHostedScan", "is", "simple", "and", "effective", "Run", "a", "wide", "set", "of", "industry-l", "Category", "complex-word"]}, {"type": "text", "description": "Word may need pronunciation guidance: \"industry\"", "value": "Context: \"effective. Run a wide set of industry-leading tools to uncover vuln\" (Category: complex-word)", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 713, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-063", "ruleName": "Pronunciation", "timestamp": "2025-07-11T11:39:10.062Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "effective", "Run", "a", "wide", "set", "of", "industry-leading", "tools", "to", "uncover", "vuln", "Category", "complex-word"]}], "recommendations": ["Provide pronunciation guidance for foreign words and technical terms", "Use phonetic notation (IPA) or simple pronunciation guides", "Consider audio pronunciation for complex words", "Use tooltips or expandable sections for pronunciation information", "Test pronunciation guidance with users who may be unfamiliar with the terms"], "executionTime": 666, "originalScore": 60}, "timestamp": 1752233950062, "hash": "b39cb53fe7e1840b83a8efebdc41f7ee", "accessCount": 1, "lastAccessed": 1752233950062, "size": 7698}
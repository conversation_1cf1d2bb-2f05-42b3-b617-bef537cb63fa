{"data": [{"type": "text", "description": "Non-text content analysis summary", "value": "0/72 elements pass automated checks, 61 require manual review", "severity": "error", "elementCount": 1, "affectedSelectors": ["elements", "pass", "automated", "checks", "require", "manual", "review"], "fixExample": {"before": "Current implementation", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 0, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.340Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"BbAmericas\" - accuracy verification needed", "selector": "img:nth-of-type(1)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(1)", "Alt", "text", "present", "BbAmericas", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 1, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.341Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Porsche\" - accuracy verification needed", "selector": "img:nth-of-type(2)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(2)", "Alt", "text", "present", "Porsche", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 2, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.341Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"ExpediaGroup\" - accuracy verification needed", "selector": "img:nth-of-type(3)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(3)", "Alt", "text", "present", "ExpediaGroup", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 3, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.341Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WeMakeApps\" - accuracy verification needed", "selector": "img:nth-of-type(4)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(4)", "Alt", "text", "present", "WeMakeApps", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 4, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.341Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"SibylSoft\" - accuracy verification needed", "selector": "img:nth-of-type(5)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(5)", "Alt", "text", "present", "SibylSoft", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 5, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.341Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Luminary\" - accuracy verification needed", "selector": "img:nth-of-type(6)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(6)", "Alt", "text", "present", "Luminary", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 6, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.341Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"CoinMe\" - accuracy verification needed", "selector": "img:nth-of-type(7)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(7)", "Alt", "text", "present", "CoinMe", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 7, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.341Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Appetize\" - accuracy verification needed", "selector": "img:nth-of-type(8)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(8)", "Alt", "text", "present", "Appetize", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 8, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.341Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WonderProxy\" - accuracy verification needed", "selector": "img:nth-of-type(9)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(9)", "Alt", "text", "present", "WonderProxy", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 9, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.341Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Median\" - accuracy verification needed", "selector": "img:nth-of-type(10)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(10)", "Alt", "text", "present", "Median", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 10, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.341Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TaxiCaller\" - accuracy verification needed", "selector": "img:nth-of-type(11)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(11)", "Alt", "text", "present", "TaxiCaller", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 11, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.341Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Yamaha\" - accuracy verification needed", "selector": "img:nth-of-type(12)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(12)", "Alt", "text", "present", "Yamaha", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 12, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.341Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"UniversityOfOxford\" - accuracy verification needed", "selector": "img:nth-of-type(13)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(13)", "Alt", "text", "present", "UniversityOfOxford", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 13, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.341Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"BbAmericas\" - accuracy verification needed", "selector": "img:nth-of-type(14)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(14)", "Alt", "text", "present", "BbAmericas", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 14, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Porsche\" - accuracy verification needed", "selector": "img:nth-of-type(15)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(15)", "Alt", "text", "present", "Porsche", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 15, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"ExpediaGroup\" - accuracy verification needed", "selector": "img:nth-of-type(16)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(16)", "Alt", "text", "present", "ExpediaGroup", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 16, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WeMakeApps\" - accuracy verification needed", "selector": "img:nth-of-type(17)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(17)", "Alt", "text", "present", "WeMakeApps", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 17, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"SibylSoft\" - accuracy verification needed", "selector": "img:nth-of-type(18)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(18)", "Alt", "text", "present", "SibylSoft", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 18, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Luminary\" - accuracy verification needed", "selector": "img:nth-of-type(19)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(19)", "Alt", "text", "present", "Luminary", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 19, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"CoinMe\" - accuracy verification needed", "selector": "img:nth-of-type(20)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(20)", "Alt", "text", "present", "CoinMe", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 20, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Appetize\" - accuracy verification needed", "selector": "img:nth-of-type(21)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(21)", "Alt", "text", "present", "Appetize", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 21, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WonderProxy\" - accuracy verification needed", "selector": "img:nth-of-type(22)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(22)", "Alt", "text", "present", "WonderProxy", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 22, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Median\" - accuracy verification needed", "selector": "img:nth-of-type(23)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(23)", "Alt", "text", "present", "Median", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 23, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TaxiCaller\" - accuracy verification needed", "selector": "img:nth-of-type(24)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(24)", "Alt", "text", "present", "TaxiCaller", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 24, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Yamaha\" - accuracy verification needed", "selector": "img:nth-of-type(25)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(25)", "Alt", "text", "present", "Yamaha", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 25, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"UniversityOfOxford\" - accuracy verification needed", "selector": "img:nth-of-type(26)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(26)", "Alt", "text", "present", "UniversityOfOxford", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 26, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"UniversityOfOxford\" - accuracy verification needed", "selector": "img:nth-of-type(27)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(27)", "Alt", "text", "present", "UniversityOfOxford", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 27, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Yamaha\" - accuracy verification needed", "selector": "img:nth-of-type(28)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(28)", "Alt", "text", "present", "Yamaha", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 28, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TaxiCaller\" - accuracy verification needed", "selector": "img:nth-of-type(29)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(29)", "Alt", "text", "present", "TaxiCaller", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 29, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Median\" - accuracy verification needed", "selector": "img:nth-of-type(30)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(30)", "Alt", "text", "present", "Median", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 30, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WonderProxy\" - accuracy verification needed", "selector": "img:nth-of-type(31)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(31)", "Alt", "text", "present", "WonderProxy", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 31, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Appetize\" - accuracy verification needed", "selector": "img:nth-of-type(32)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(32)", "Alt", "text", "present", "Appetize", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 32, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"CoinMe\" - accuracy verification needed", "selector": "img:nth-of-type(33)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(33)", "Alt", "text", "present", "CoinMe", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 33, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Luminary\" - accuracy verification needed", "selector": "img:nth-of-type(34)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(34)", "Alt", "text", "present", "Luminary", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 34, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"SibylSoft\" - accuracy verification needed", "selector": "img:nth-of-type(35)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(35)", "Alt", "text", "present", "SibylSoft", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 35, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.342Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WeMakeApps\" - accuracy verification needed", "selector": "img:nth-of-type(36)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(36)", "Alt", "text", "present", "WeMakeApps", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 36, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"ExpediaGroup\" - accuracy verification needed", "selector": "img:nth-of-type(37)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(37)", "Alt", "text", "present", "ExpediaGroup", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 37, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Porsche\" - accuracy verification needed", "selector": "img:nth-of-type(38)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(38)", "Alt", "text", "present", "Porsche", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 38, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"BbAmericas\" - accuracy verification needed", "selector": "img:nth-of-type(39)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(39)", "Alt", "text", "present", "BbAmericas", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 39, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"UniversityOfOxford\" - accuracy verification needed", "selector": "img:nth-of-type(40)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(40)", "Alt", "text", "present", "UniversityOfOxford", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 40, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Yamaha\" - accuracy verification needed", "selector": "img:nth-of-type(41)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(41)", "Alt", "text", "present", "Yamaha", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 41, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TaxiCaller\" - accuracy verification needed", "selector": "img:nth-of-type(42)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(42)", "Alt", "text", "present", "TaxiCaller", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 42, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Median\" - accuracy verification needed", "selector": "img:nth-of-type(43)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(43)", "Alt", "text", "present", "Median", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 43, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WonderProxy\" - accuracy verification needed", "selector": "img:nth-of-type(44)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(44)", "Alt", "text", "present", "WonderProxy", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 44, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Appetize\" - accuracy verification needed", "selector": "img:nth-of-type(45)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(45)", "Alt", "text", "present", "Appetize", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 45, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"CoinMe\" - accuracy verification needed", "selector": "img:nth-of-type(46)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(46)", "Alt", "text", "present", "CoinMe", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 46, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Luminary\" - accuracy verification needed", "selector": "img:nth-of-type(47)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(47)", "Alt", "text", "present", "Luminary", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 47, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"SibylSoft\" - accuracy verification needed", "selector": "img:nth-of-type(48)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(48)", "Alt", "text", "present", "SibylSoft", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 48, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WeMakeApps\" - accuracy verification needed", "selector": "img:nth-of-type(49)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(49)", "Alt", "text", "present", "WeMakeApps", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 49, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"ExpediaGroup\" - accuracy verification needed", "selector": "img:nth-of-type(50)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(50)", "Alt", "text", "present", "ExpediaGroup", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 50, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Porsche\" - accuracy verification needed", "selector": "img:nth-of-type(51)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(51)", "Alt", "text", "present", "Porsche", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 51, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"BbAmericas\" - accuracy verification needed", "selector": "img:nth-of-type(52)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(52)", "Alt", "text", "present", "BbAmericas", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 52, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "img.absolute.!h-[645px].!w-[1400px].object-contain", "severity": "error", "elementCount": 2, "affectedSelectors": ["img.absolute.!h-[645px].!w-[1400px].object-contain", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 53, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "img.absolute.object-contain", "severity": "error", "elementCount": 1, "affectedSelectors": ["img.absolute.object-contain", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 54, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "img.absolute.object-contain", "severity": "error", "elementCount": 1, "affectedSelectors": ["img.absolute.object-contain", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<img src=\"image.jpg\" alt=\"\">", "after": "<img src=\"image.jpg\" alt=\"Descriptive alternative text\">", "description": "Add meaningful alt text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2 2024\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://www.w3.org/WAI/tutorials/images/"]}, "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 55, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.343Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"<PERSON> Los<PERSON>\" - accuracy verification needed", "selector": "img.object-contain", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.object-contain", "Alt", "text", "present", "<PERSON>", "<PERSON><PERSON>", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 56, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.344Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Alex\" - accuracy verification needed", "selector": "img.object-contain", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.object-contain", "Alt", "text", "present", "<PERSON>", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 57, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.344Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"<PERSON>\" - accuracy verification needed", "selector": "img.object-contain", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.object-contain", "Alt", "text", "present", "<PERSON>", "<PERSON>", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 58, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.344Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"<PERSON>\" - accuracy verification needed", "selector": "img.object-contain", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.object-contain", "Alt", "text", "present", "<PERSON>", "<PERSON>", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 134, "checkSpecificData": {"automationRate": 0.95, "checkType": "image-alternative-analysis", "manualReviewRequired": false, "imageAnalysis": true, "altTextValidation": true, "evidenceIndex": 59, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.344Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752233807344, "hash": "7cfb4451b42e43de6bb44b6ce5dc759b", "accessCount": 1, "lastAccessed": 1752233807344, "size": 51531}
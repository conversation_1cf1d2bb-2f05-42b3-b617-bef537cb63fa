{"data": {"ruleId": "WCAG-039", "ruleName": "Images of Text", "category": "perceivable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0535, "automated": true, "evidence": [{"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"BbAmericas\" | Src: https://hostedscan.com/customers/v2/bb-americas-white.svg", "selector": "img:nth-of-type(7)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(7)", "Alt", "BbAmericas", "Src", "https", "hostedscan.com", "customers", "v2", "bb-americas-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"ExpediaGroup\" | Src: https://hostedscan.com/customers/v2/expedia-group-white.svg", "selector": "img:nth-of-type(9)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(9)", "Alt", "ExpediaGroup", "Src", "https", "hostedscan.com", "customers", "v2", "expedia-group-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"WeMakeApps\" | Src: https://hostedscan.com/customers/v2/we-make-apps-white.svg", "selector": "img:nth-of-type(10)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(10)", "Alt", "WeMakeApps", "Src", "https", "hostedscan.com", "customers", "v2", "we-make-apps-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"SibylSoft\" | Src: https://hostedscan.com/customers/v2/sibyl-soft-white.svg", "selector": "img:nth-of-type(11)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(11)", "Alt", "SibylSoft", "Src", "https", "hostedscan.com", "customers", "v2", "sibyl-soft-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Luminary\" | Src: https://hostedscan.com/customers/v2/luminary-white.svg", "selector": "img:nth-of-type(12)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(12)", "Alt", "Luminary", "Src", "https", "hostedscan.com", "customers", "v2", "luminary-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"CoinMe\" | Src: https://hostedscan.com/customers/v2/coin-me-white.svg", "selector": "img:nth-of-type(13)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(13)", "Alt", "CoinMe", "Src", "https", "hostedscan.com", "customers", "v2", "coin-me-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Appetize\" | Src: https://hostedscan.com/customers/v2/appetize-white.svg", "selector": "img:nth-of-type(14)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(14)", "Alt", "Appetize", "Src", "https", "hostedscan.com", "customers", "v2", "appetize-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"WonderProxy\" | Src: https://hostedscan.com/customers/v2/wonder-proxy-white.svg", "selector": "img:nth-of-type(15)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(15)", "Alt", "WonderProxy", "Src", "https", "hostedscan.com", "customers", "v2", "wonder-proxy-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Median\" | Src: https://hostedscan.com/customers/v2/median-white.svg", "selector": "img:nth-of-type(16)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(16)", "Alt", "Median", "Src", "https", "hostedscan.com", "customers", "v2", "median-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"TaxiCaller\" | Src: https://hostedscan.com/customers/v2/taxi-caller-white.svg", "selector": "img:nth-of-type(17)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(17)", "Alt", "TaxiCaller", "Src", "https", "hostedscan.com", "customers", "v2", "taxi-caller-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Yamaha\" | Src: https://hostedscan.com/customers/v2/yamaha-white.svg", "selector": "img:nth-of-type(18)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(18)", "Alt", "Yamaha", "Src", "https", "hostedscan.com", "customers", "v2", "yamaha-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"UniversityOfOxford\" | Src: https://hostedscan.com/customers/v2/university-of-oxford-white.svg", "selector": "img:nth-of-type(19)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(19)", "Alt", "UniversityOfOxford", "Src", "https", "hostedscan.com", "customers", "v2", "university-of-oxford-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"BbAmericas\" | Src: https://hostedscan.com/customers/v2/bb-americas-white.svg", "selector": "img:nth-of-type(20)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(20)", "Alt", "BbAmericas", "Src", "https", "hostedscan.com", "customers", "v2", "bb-americas-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"ExpediaGroup\" | Src: https://hostedscan.com/customers/v2/expedia-group-white.svg", "selector": "img:nth-of-type(22)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(22)", "Alt", "ExpediaGroup", "Src", "https", "hostedscan.com", "customers", "v2", "expedia-group-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"WeMakeApps\" | Src: https://hostedscan.com/customers/v2/we-make-apps-white.svg", "selector": "img:nth-of-type(23)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(23)", "Alt", "WeMakeApps", "Src", "https", "hostedscan.com", "customers", "v2", "we-make-apps-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"SibylSoft\" | Src: https://hostedscan.com/customers/v2/sibyl-soft-white.svg", "selector": "img:nth-of-type(24)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(24)", "Alt", "SibylSoft", "Src", "https", "hostedscan.com", "customers", "v2", "sibyl-soft-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Luminary\" | Src: https://hostedscan.com/customers/v2/luminary-white.svg", "selector": "img:nth-of-type(25)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(25)", "Alt", "Luminary", "Src", "https", "hostedscan.com", "customers", "v2", "luminary-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"CoinMe\" | Src: https://hostedscan.com/customers/v2/coin-me-white.svg", "selector": "img:nth-of-type(26)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(26)", "Alt", "CoinMe", "Src", "https", "hostedscan.com", "customers", "v2", "coin-me-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Appetize\" | Src: https://hostedscan.com/customers/v2/appetize-white.svg", "selector": "img:nth-of-type(27)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(27)", "Alt", "Appetize", "Src", "https", "hostedscan.com", "customers", "v2", "appetize-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"WonderProxy\" | Src: https://hostedscan.com/customers/v2/wonder-proxy-white.svg", "selector": "img:nth-of-type(28)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(28)", "Alt", "WonderProxy", "Src", "https", "hostedscan.com", "customers", "v2", "wonder-proxy-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Median\" | Src: https://hostedscan.com/customers/v2/median-white.svg", "selector": "img:nth-of-type(29)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(29)", "Alt", "Median", "Src", "https", "hostedscan.com", "customers", "v2", "median-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.145Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"TaxiCaller\" | Src: https://hostedscan.com/customers/v2/taxi-caller-white.svg", "selector": "img:nth-of-type(30)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(30)", "Alt", "TaxiCaller", "Src", "https", "hostedscan.com", "customers", "v2", "taxi-caller-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.146Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Yamaha\" | Src: https://hostedscan.com/customers/v2/yamaha-white.svg", "selector": "img:nth-of-type(31)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(31)", "Alt", "Yamaha", "Src", "https", "hostedscan.com", "customers", "v2", "yamaha-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.146Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"UniversityOfOxford\" | Src: https://hostedscan.com/customers/v2/university-of-oxford-white.svg", "selector": "img:nth-of-type(32)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(32)", "Alt", "UniversityOfOxford", "Src", "https", "hostedscan.com", "customers", "v2", "university-of-oxford-white.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.146Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Scan report image\" | Src: https://hostedscan.com/images/solutions/scan-report-image.webp", "selector": "img:nth-of-type(66)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(66)", "Alt", "<PERSON><PERSON>", "report", "image", "Src", "https", "hostedscan.com", "images", "solutions", "scan-report-image.webp"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.146Z"}}}, {"type": "code", "description": "Image potentially containing text: Context suggests text content", "value": "Alt: \"\" | Src: ", "selector": "img:nth-of-type(69)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(69)", "Alt", "Src"], "severity": "warning", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.146Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"mail alerts\" | Src: https://hostedscan.com/images/solutions/schedule.webp", "selector": "img:nth-of-type(70)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(70)", "Alt", "mail", "alerts", "Src", "https", "hostedscan.com", "images", "solutions", "schedule.webp"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.146Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"targets\" | Src: https://hostedscan.com/images/solutions/targets.webp", "selector": "img:nth-of-type(73)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(73)", "Alt", "targets", "Src", "https", "hostedscan.com", "images", "solutions", "targets.webp"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.146Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"<PERSON>\" | Src: https://hostedscan.com/testimonials/enuan.svg", "selector": "img:nth-of-type(76)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(76)", "Alt", "<PERSON>", "<PERSON><PERSON>", "Src", "https", "hostedscan.com", "testimonials", "enuan.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.146Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"<PERSON>\" | Src: https://hostedscan.com/testimonials/principle-networks.svg", "selector": "img:nth-of-type(77)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(77)", "Alt", "<PERSON>", "Src", "https", "hostedscan.com", "testimonials", "principle-networks.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.146Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"<PERSON>\" | Src: https://hostedscan.com/testimonials/fcskc.svg", "selector": "img:nth-of-type(78)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(78)", "Alt", "<PERSON>", "<PERSON>", "Src", "https", "hostedscan.com", "testimonials", "fcskc.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.146Z"}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"<PERSON>\" | Src: https://hostedscan.com/testimonials/cobrowse.svg", "selector": "img:nth-of-type(79)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(79)", "Alt", "<PERSON>", "<PERSON>", "Src", "https", "hostedscan.com", "testimonials", "cobrowse.svg"], "severity": "error", "metadata": {"scanDuration": 1326, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T11:38:30.146Z"}}}], "recommendations": ["Use actual HTML text instead of images of text when possible", "Reserve images of text for logos, branding, or essential visual presentation", "Provide text alternatives that include the same text as the image", "Use CSS for visual text styling instead of images", "Ensure images of text can be resized without loss of quality"], "executionTime": 19, "originalScore": 0}, "timestamp": 1752233910146, "hash": "885531203871fee7e6512994a9fef920", "accessCount": 1, "lastAccessed": 1752233910146, "size": 20590}
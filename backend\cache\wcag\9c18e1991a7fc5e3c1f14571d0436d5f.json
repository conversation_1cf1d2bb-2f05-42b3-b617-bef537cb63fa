{"data": {"ruleId": "WCAG-060", "ruleName": "Unusual Words", "category": "understandable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0305, "automated": true, "evidence": [{"type": "text", "description": "Unusual words without definitions", "value": "Found 13 unusual words that may need definitions for AAA compliance", "elementCount": 1, "affectedSelectors": ["Found", "unusual", "words", "that", "may", "need", "definitions", "for", "AAA", "compliance"], "severity": "warning", "metadata": {"scanDuration": 5408, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T18:13:41.160Z"}}}, {"type": "text", "description": "Unusual word without definition: \"ems\"", "value": "Context: \"flex-direction:column;align-items:flex-start;}.kadence-column41\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 5408, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T18:13:41.160Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "flex-direction", "column", "align-items", "flex-start", "kadence-column41"]}, {"type": "text", "description": "Unusual word without definition: \"physicians\"", "value": "Context: \"bottom:0px;padding-left:0px;}\nPhysicians\n\n\n.kt-svg-icon-list-item-419_\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 5408, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T18:13:41.160Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "bottom", "px", "padding-left", "Physicians", "kt-svg-icon-list-item-419_"]}, {"type": "text", "description": "Unusual word without definition: \"teams\"", "value": "Context: \":0px;}\nCoordinate care across teams and facilities with a full su\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 5408, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T18:13:41.160Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "px", "Coordinate", "care", "across", "teams", "and", "facilities", "with", "a", "full", "su"]}, {"type": "text", "description": "Unusual word without definition: \"nurse\"", "value": "Context: \"bottom:0px;padding-left:0px;}\nNurses\n\n\n.kt-svg-icon-list-item-419\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 5408, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T18:13:41.160Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "bottom", "px", "padding-left", "Nurses", "kt-svg-icon-list-item-419"]}, {"type": "text", "description": "Unusual word without definition: \"executives\"", "value": "Context: \"bottom:0px;padding-left:0px;}\nExecutives\n\n\n.kt-svg-icon-list-item-419_\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 5408, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T18:13:41.160Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "bottom", "px", "padding-left", "Executives", "kt-svg-icon-list-item-419_"]}, {"type": "text", "description": "Unusual word without definition: \"rave\"", "value": "Context: \"ealthcare Communication\n\n\n\n\n\n\nRAVE Reviews by\n\n\n\nReal Customers\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 5408, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T18:13:41.160Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "ealthcare", "Communication", "RAVE", "Reviews", "by", "Real", "Customers"]}, {"type": "text", "description": "Unusual word without definition: \"klas\"", "value": "Context: \"ement for us.”\n\n\n\nDirector\n\n\n\nKLAS Research, October 2024\n\n\n\n\n\n“\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 5408, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T18:13:41.160Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "ement", "for", "us", "Director", "KLAS", "Research", "October"]}, {"type": "text", "description": "Unusual word without definition: \"ehr\"", "value": "Context: \"we hope to integrate with our EHR so that we can send messages\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 5408, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T18:13:41.160Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "we", "hope", "to", "integrate", "with", "our", "EHR", "so", "that", "can", "send", "messages"]}, {"type": "text", "description": "Unusual word without definition: \"cio\"", "value": "Context: \"ed rather than bolted on.”\n\n\n\nCIO\n\n\n\nKLAS Research, October 202\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 5408, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T18:13:41.160Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "ed", "rather", "than", "bolted", "on", "CIO", "KLAS", "Research", "October"]}, {"type": "text", "description": "Unusual word without definition: \"top\"", "value": "Context: \"content-edge-padding);padding-top:var(--global-kb-spacing-md, 2\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 5408, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T18:13:41.160Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "content-edge-padding", "padding-top", "var", "global-kb-spacing-md"]}], "recommendations": ["Provide definitions for unusual words, jargon, and technical terms", "Use <abbr> elements with title attributes for acronyms", "Consider creating a glossary page for frequently used technical terms", "Provide inline definitions or explanations for specialized vocabulary", "Link unusual words to their definitions in a glossary"], "executionTime": 5223, "originalScore": 40, "thresholdApplied": 75, "scoringDetails": "40.0% (threshold: 75%) - FAILED"}, "timestamp": 1752257621160, "hash": "dcba17f4f9d74918a1fd6dae26bbb754", "accessCount": 1, "lastAccessed": 1752257621160, "size": 6981, "metadata": {"originalKey": "rule:WCAG-060:053b13d2:add92319", "normalizedKey": "rule_wcag-060_053b13d2_add92319", "savedAt": 1752257621161, "version": "1.1", "keyHash": "3b570c863dddd6fd7e097bb74cce0854"}}
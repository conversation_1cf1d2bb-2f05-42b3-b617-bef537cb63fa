{"data": [{"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding), CSS class suggests text content", "value": "Alt: \"TigerConnect-Clinical-Healthcare-Communications-Logo\" | Src: https://tigerconnect.com/wp-content/uploads/2024/01/TigerConnect-Clinical-Healthcare-Communications-", "selector": "img:nth-of-type(22)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(22)", "Alt", "TigerConnect-Clinical-Healthcare-Communications-Logo", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "TigerConnect-Clinical-Healthcare-Communications-", "//tigerconnect.com/wp-content/uploads/2024/01/TigerConnect-Clinical-Healthcare-Communications-"], "severity": "info", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.684Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 93\" | Src: https://tigerconnect.com/wp-content/uploads/2024/05/group-93.webp", "selector": "img:nth-of-type(103)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(103)", "Alt", "group", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "group-93.webp", "//tigerconnect.com/wp-content/uploads/2024/05/group-93.webp"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.684Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"CareConduit logo\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/05/CareConduit-logo.svg", "selector": "img:nth-of-type(104)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(104)", "Alt", "CareConduit", "logo", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "CareConduit-logo.svg", "//newtc.kinsta.cloud/wp-content/uploads/2025/05/CareConduit-logo.svg"], "severity": "info", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.684Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Ambulance white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/02/Ambulance-white.png", "selector": "img:nth-of-type(105)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(105)", "Alt", "Ambulance", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Ambulance-white.png", "//newtc.kinsta.cloud/wp-content/uploads/2025/02/Ambulance-white.png"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.684Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group message white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-message-white.png", "selector": "img:nth-of-type(106)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(106)", "Alt", "group", "message", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-message-white.png", "//newtc.kinsta.cloud/wp-content/uploads/2024/05/group-message-white.png"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 94\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-94.webp", "selector": "img:nth-of-type(107)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(107)", "Alt", "group", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-94.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/05/group-94.webp"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Alarm Mgt 1w\" | Src: https://tigerconnect.com/wp-content/uploads/2024/07/Alarm-Mgt_1w.png", "selector": "img:nth-of-type(108)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(108)", "Alt", "Alarm", "Mgt", "w", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Alarm-Mgt_1w.png", "//tigerconnect.com/wp-content/uploads/2024/07/Alarm-Mgt_1w.png"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 6, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 93\" | Src: https://tigerconnect.com/wp-content/uploads/2024/05/group-93.webp", "selector": "img:nth-of-type(109)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(109)", "Alt", "group", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "group-93.webp", "//tigerconnect.com/wp-content/uploads/2024/05/group-93.webp"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 7, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"CareConduit logo\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/05/CareConduit-logo.svg", "selector": "img:nth-of-type(110)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(110)", "Alt", "CareConduit", "logo", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "CareConduit-logo.svg", "//newtc.kinsta.cloud/wp-content/uploads/2025/05/CareConduit-logo.svg"], "severity": "info", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 8, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Ambulance white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/02/Ambulance-white.png", "selector": "img:nth-of-type(111)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(111)", "Alt", "Ambulance", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Ambulance-white.png", "//newtc.kinsta.cloud/wp-content/uploads/2025/02/Ambulance-white.png"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 9, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group message white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-message-white.png", "selector": "img:nth-of-type(112)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(112)", "Alt", "group", "message", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-message-white.png", "//newtc.kinsta.cloud/wp-content/uploads/2024/05/group-message-white.png"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 10, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 94\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-94.webp", "selector": "img:nth-of-type(113)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(113)", "Alt", "group", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-94.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/05/group-94.webp"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 11, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Alarm Mgt 1w\" | Src: https://tigerconnect.com/wp-content/uploads/2024/07/Alarm-Mgt_1w.png", "selector": "img:nth-of-type(114)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(114)", "Alt", "Alarm", "Mgt", "w", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Alarm-Mgt_1w.png", "//tigerconnect.com/wp-content/uploads/2024/07/Alarm-Mgt_1w.png"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 12, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 93\" | Src: https://tigerconnect.com/wp-content/uploads/2024/05/group-93.webp", "selector": "img:nth-of-type(115)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(115)", "Alt", "group", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "group-93.webp", "//tigerconnect.com/wp-content/uploads/2024/05/group-93.webp"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 13, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"CareConduit logo\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/05/CareConduit-logo.svg", "selector": "img:nth-of-type(116)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(116)", "Alt", "CareConduit", "logo", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "CareConduit-logo.svg", "//newtc.kinsta.cloud/wp-content/uploads/2025/05/CareConduit-logo.svg"], "severity": "info", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 14, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Ambulance white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/02/Ambulance-white.png", "selector": "img:nth-of-type(117)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(117)", "Alt", "Ambulance", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Ambulance-white.png", "//newtc.kinsta.cloud/wp-content/uploads/2025/02/Ambulance-white.png"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 15, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group message white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-message-white.png", "selector": "img:nth-of-type(118)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(118)", "Alt", "group", "message", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-message-white.png", "//newtc.kinsta.cloud/wp-content/uploads/2024/05/group-message-white.png"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 16, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 94\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-94.webp", "selector": "img:nth-of-type(119)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(119)", "Alt", "group", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-94.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/05/group-94.webp"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 17, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Alarm Mgt 1w\" | Src: https://tigerconnect.com/wp-content/uploads/2024/07/Alarm-Mgt_1w.png", "selector": "img:nth-of-type(120)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(120)", "Alt", "Alarm", "Mgt", "w", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Alarm-Mgt_1w.png", "//tigerconnect.com/wp-content/uploads/2024/07/Alarm-Mgt_1w.png"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 18, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 93\" | Src: https://tigerconnect.com/wp-content/uploads/2024/05/group-93.webp", "selector": "img:nth-of-type(121)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(121)", "Alt", "group", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "group-93.webp", "//tigerconnect.com/wp-content/uploads/2024/05/group-93.webp"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 19, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"CareConduit logo\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/05/CareConduit-logo.svg", "selector": "img:nth-of-type(122)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(122)", "Alt", "CareConduit", "logo", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "CareConduit-logo.svg", "//newtc.kinsta.cloud/wp-content/uploads/2025/05/CareConduit-logo.svg"], "severity": "info", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 20, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Ambulance white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/02/Ambulance-white.png", "selector": "img:nth-of-type(123)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(123)", "Alt", "Ambulance", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Ambulance-white.png", "//newtc.kinsta.cloud/wp-content/uploads/2025/02/Ambulance-white.png"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 21, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group message white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-message-white.png", "selector": "img:nth-of-type(124)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(124)", "Alt", "group", "message", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-message-white.png", "//newtc.kinsta.cloud/wp-content/uploads/2024/05/group-message-white.png"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 22, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Workflow Critical Response\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Critical-Response.webp", "selector": "img:nth-of-type(125)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(125)", "Alt", "Workflow", "Critical", "Response", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Critical-Response.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Critical-Response.webp"], "severity": "info", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 23, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Workflow Emergency Department\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Emergency-Department.webp", "selector": "img:nth-of-type(126)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(126)", "Alt", "Workflow", "Emergency", "Department", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Emergency-Department.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Emergency-Department.webp"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 24, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Workflow Inpatient Care\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Inpatient-Care.webp", "selector": "img:nth-of-type(127)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(127)", "Alt", "Workflow", "Inpatient", "Care", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Inpatient-Care.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Inpatient-Care.webp"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 25, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Workflow Operating Room\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Operating-Room.webp", "selector": "img:nth-of-type(128)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(128)", "Alt", "Workflow", "Operating", "Room", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Operating-Room.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Operating-Room.webp"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 26, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Workflow Post Acute Ambulatory\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Post-Acute-Ambulatory.webp", "selector": "img:nth-of-type(129)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(129)", "Alt", "Workflow", "Post", "Acute", "Ambulatory", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Post-Acute-Ambulatory.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Post-Acute-Ambulatory.webp"], "severity": "error", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 27, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content", "value": "Alt: \"blog header why healthcare needs purpose built collaboration solutions\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/04/blog-header-why-healthcare-needs-purpose-built", "selector": "img:nth-of-type(131)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(131)", "Alt", "blog", "header", "why", "healthcare", "needs", "purpose", "built", "collaboration", "solutions", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "blog-header-why-healthcare-needs-purpose-built", "//newtc.kinsta.cloud/wp-content/uploads/2024/04/blog-header-why-healthcare-needs-purpose-built"], "severity": "warning", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 28, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo Geisinger G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-Geisinger-G.png", "selector": "img:nth-of-type(135)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(135)", "Alt", "Logo", "<PERSON><PERSON><PERSON><PERSON>", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-Geisinger-G.png", "//tigerconnect.com/wp-content/uploads/2024/02/Logo-Geisinger-G.png"], "severity": "info", "metadata": {"scanDuration": 15, "elementsAnalyzed": 76, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 29, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:46:18.685Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259578685, "hash": "702b36bdd937420e56e652512a4d4fa0", "accessCount": 1, "lastAccessed": 1752259578685, "size": 31483, "metadata": {"originalKey": "WCAG-039:WCAG-039:Y29kZTpJbWFnZSBwb3RlbnRpYWxseSBj", "normalizedKey": "wcag-039_wcag-039_y29kztpjbwfnzsbwb3rlbnrpywxsesbj", "savedAt": 1752259578686, "version": "1.1", "keyHash": "6a1cde85cc98d3d11c8d5b8704c2a250"}}
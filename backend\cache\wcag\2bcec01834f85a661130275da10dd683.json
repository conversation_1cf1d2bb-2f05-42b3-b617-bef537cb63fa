{"data": [{"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding), CSS class suggests text content", "value": "Alt: \"TigerConnect-Clinical-Healthcare-Communications-Logo\" | Src: https://tigerconnect.com/wp-content/uploads/2024/01/TigerConnect-Clinical-Healthcare-Communications-", "selector": "img:nth-of-type(7)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(7)", "Alt", "TigerConnect-Clinical-Healthcare-Communications-Logo", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "TigerConnect-Clinical-Healthcare-Communications-", "//tigerconnect.com/wp-content/uploads/2024/01/TigerConnect-Clinical-Healthcare-Communications-"], "severity": "info", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.283Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Ambulance white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/02/Ambulance-white.png", "selector": "img:nth-of-type(86)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(86)", "Alt", "Ambulance", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Ambulance-white.png", "//newtc.kinsta.cloud/wp-content/uploads/2025/02/Ambulance-white.png"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.283Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group message white\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-message-white.png", "selector": "img:nth-of-type(87)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(87)", "Alt", "group", "message", "white", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-message-white.png", "//newtc.kinsta.cloud/wp-content/uploads/2024/05/group-message-white.png"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.283Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 94\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/05/group-94.webp", "selector": "img:nth-of-type(88)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(88)", "Alt", "group", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "group-94.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/05/group-94.webp"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.283Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Alarm Mgt 1w\" | Src: https://tigerconnect.com/wp-content/uploads/2024/07/Alarm-Mgt_1w.png", "selector": "img:nth-of-type(89)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(89)", "Alt", "Alarm", "Mgt", "w", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Alarm-Mgt_1w.png", "//tigerconnect.com/wp-content/uploads/2024/07/Alarm-Mgt_1w.png"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.283Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"group 93\" | Src: https://tigerconnect.com/wp-content/uploads/2024/05/group-93.webp", "selector": "img:nth-of-type(90)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(90)", "Alt", "group", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "group-93.webp", "//tigerconnect.com/wp-content/uploads/2024/05/group-93.webp"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.283Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"CareConduit logo\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2025/05/CareConduit-logo.svg", "selector": "img:nth-of-type(91)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(91)", "Alt", "CareConduit", "logo", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "CareConduit-logo.svg", "//newtc.kinsta.cloud/wp-content/uploads/2025/05/CareConduit-logo.svg"], "severity": "info", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 6, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.283Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Workflow Critical Response\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Critical-Response.webp", "selector": "img:nth-of-type(92)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(92)", "Alt", "Workflow", "Critical", "Response", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Critical-Response.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Critical-Response.webp"], "severity": "info", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 7, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.283Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Workflow Emergency Department\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Emergency-Department.webp", "selector": "img:nth-of-type(93)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(93)", "Alt", "Workflow", "Emergency", "Department", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Emergency-Department.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Emergency-Department.webp"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 8, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.283Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Workflow Inpatient Care\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Inpatient-Care.webp", "selector": "img:nth-of-type(94)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(94)", "Alt", "Workflow", "Inpatient", "Care", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Inpatient-Care.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Inpatient-Care.webp"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 9, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.283Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Workflow Operating Room\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Operating-Room.webp", "selector": "img:nth-of-type(95)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(95)", "Alt", "Workflow", "Operating", "Room", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Operating-Room.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Operating-Room.webp"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 10, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.283Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Workflow Post Acute Ambulatory\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Post-Acute-Ambulatory.webp", "selector": "img:nth-of-type(96)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(96)", "Alt", "Workflow", "Post", "Acute", "Ambulatory", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Workflow-Post-Acute-Ambulatory.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/02/Workflow-Post-Acute-Ambulatory.webp"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 11, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.283Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content", "value": "Alt: \"blog header why healthcare needs purpose built collaboration solutions\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/04/blog-header-why-healthcare-needs-purpose-built", "selector": "img:nth-of-type(98)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(98)", "Alt", "blog", "header", "why", "healthcare", "needs", "purpose", "built", "collaboration", "solutions", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "blog-header-why-healthcare-needs-purpose-built", "//newtc.kinsta.cloud/wp-content/uploads/2024/04/blog-header-why-healthcare-needs-purpose-built"], "severity": "warning", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 12, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.283Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo CapHealth G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-CapHealth-G.png", "selector": "img:nth-of-type(100)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(100)", "Alt", "Logo", "CapHealth", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-CapHealth-G.png", "//tigerconnect.com/wp-content/uploads/2024/02/Logo-CapHealth-G.png"], "severity": "info", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 13, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo CommonSpirit G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-CommonSpirit-G.png", "selector": "img:nth-of-type(101)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(101)", "Alt", "Logo", "CommonSpirit", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-CommonSpirit-G.png", "//tigerconnect.com/wp-content/uploads/2024/02/Logo-CommonSpirit-G.png"], "severity": "info", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 14, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo Geisinger G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-Geisinger-G.png", "selector": "img:nth-of-type(102)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(102)", "Alt", "Logo", "<PERSON><PERSON><PERSON><PERSON>", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-Geisinger-G.png", "//tigerconnect.com/wp-content/uploads/2024/02/Logo-Geisinger-G.png"], "severity": "info", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 15, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo Innovation G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-Innovation-G.png", "selector": "img:nth-of-type(103)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(103)", "Alt", "Logo", "Innovation", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-Innovation-G.png", "//tigerconnect.com/wp-content/uploads/2024/02/Logo-Innovation-G.png"], "severity": "info", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 16, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"Logo UMMC G\" | Src: https://tigerconnect.com/wp-content/uploads/2024/02/Logo-UMMC-G.png", "selector": "img:nth-of-type(104)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(104)", "Alt", "Logo", "UMMC", "G", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Logo-UMMC-G.png", "//tigerconnect.com/wp-content/uploads/2024/02/Logo-UMMC-G.png"], "severity": "info", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 17, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Doctors with tablet\" | Src: https://tigerconnect.com/wp-content/uploads/2025/02/Doctors-with-tablet.jpg", "selector": "img:nth-of-type(110)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(110)", "Alt", "Doctors", "with", "tablet", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Doctors-with-tablet.jpg", "//tigerconnect.com/wp-content/uploads/2025/02/Doctors-with-tablet.jpg"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 18, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Homepage SS 2\" | Src: https://tigerconnect.com/wp-content/uploads/2025/02/Homepage-SS-2.svg", "selector": "img:nth-of-type(115)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(115)", "Alt", "Homepage", "SS", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Homepage-SS-2.svg", "//tigerconnect.com/wp-content/uploads/2025/02/Homepage-SS-2.svg"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 19, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Physician Scheduling Product SS\" | Src: https://tigerconnect.com/wp-content/uploads/2025/02/Physician-Scheduling-Product-SS.png", "selector": "img:nth-of-type(116)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(116)", "Alt", "Physician", "Scheduling", "Product", "SS", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Physician-Scheduling-Product-SS.png", "//tigerconnect.com/wp-content/uploads/2025/02/Physician-Scheduling-Product-SS.png"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 20, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Clinical Collaboration Product SS\" | Src: https://tigerconnect.com/wp-content/uploads/2025/02/Clinical-Collaboration-Product-SS.png", "selector": "img:nth-of-type(117)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(117)", "Alt", "Clinical", "Collaboration", "Product", "SS", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "Clinical-Collaboration-Product-SS.png", "//tigerconnect.com/wp-content/uploads/2025/02/Clinical-Collaboration-Product-SS.png"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 21, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"AMEN Product SS\" | Src: https://tigerconnect.com/wp-content/uploads/2025/02/AMEN-Product-SS-590x1024.png", "selector": "img:nth-of-type(118)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(118)", "Alt", "AMEN", "Product", "SS", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "AMEN-Product-SS-590x1024.png", "//tigerconnect.com/wp-content/uploads/2025/02/AMEN-Product-SS-590x1024.png"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 22, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"PE Product SS\" | Src: https://tigerconnect.com/wp-content/uploads/2025/02/PE-Product-SS-590x1024.png", "selector": "img:nth-of-type(119)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(119)", "Alt", "PE", "Product", "SS", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "PE-Product-SS-590x1024.png", "//tigerconnect.com/wp-content/uploads/2025/02/PE-Product-SS-590x1024.png"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 23, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Users Physicians\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Users-Physicians.webp", "selector": "img:nth-of-type(120)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(120)", "Alt", "Users", "Physicians", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Users-Physicians.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/02/Users-Physicians.webp"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 24, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Users IT\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Users-IT.webp", "selector": "img:nth-of-type(122)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(122)", "Alt", "Users", "IT", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Users-IT.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/02/Users-IT.webp"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 25, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"Users Nurse1\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/Users-Nurse1.webp", "selector": "img:nth-of-type(124)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(124)", "Alt", "Users", "Nurse1", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "Users-Nurse1.webp", "//newtc.kinsta.cloud/wp-content/uploads/2024/02/Users-Nurse1.webp"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 26, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"ICO\" | Src: https://newtc.kinsta.cloud/wp-content/uploads/2024/02/ICO-itex2.png", "selector": "img:nth-of-type(126)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(126)", "Alt", "ICO", "Src", "https", "newtc.kinsta.cloud", "wp-content", "uploads", "ICO-itex2.png", "//newtc.kinsta.cloud/wp-content/uploads/2024/02/ICO-itex2.png"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 27, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content", "value": "Alt: \"2024 best in klas recognition\" | Src: https://tigerconnect.com/wp-content/uploads/2025/01/2024-best-in-klas-recognition.png", "selector": "img:nth-of-type(128)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(128)", "Alt", "best", "in", "klas", "recognition", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "best-in-klas-recognition.png", "//tigerconnect.com/wp-content/uploads/2025/01/2024-best-in-klas-recognition.png"], "severity": "error", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 28, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Image potentially containing text: Filename suggests text content, Alt text suggests text content, Appears to be essential (logo/branding)", "value": "Alt: \"g2 logo png\" | Src: https://tigerconnect.com/wp-content/uploads/2025/01/g2-logo-png.png", "selector": "img:nth-of-type(129)", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(129)", "Alt", "g2", "logo", "png", "Src", "https", "tigerconnect.com", "wp-content", "uploads", "g2-logo-png.png", "//tigerconnect.com/wp-content/uploads/2025/01/g2-logo-png.png"], "severity": "info", "metadata": {"scanDuration": 5035, "elementsAnalyzed": 43, "checkSpecificData": {"automationRate": 0.75, "checkType": "image-text-analysis", "imageAnalysis": true, "textDetection": true, "aiImageAnalysis": true, "contentQualityAnalysis": true, "evidenceIndex": 29, "ruleId": "WCAG-039", "ruleName": "Images of Text", "timestamp": "2025-07-11T18:12:04.284Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752257524285, "hash": "cc63387b0b1ce897c2a42682bd41a869", "accessCount": 1, "lastAccessed": 1752257524285, "size": 31980, "metadata": {"originalKey": "WCAG-039:WCAG-039:Y29kZTpJbWFnZSBwb3RlbnRpYWxseSBj", "normalizedKey": "wcag-039_wcag-039_y29kztpjbwfnzsbwb3rlbnrpywxsesbj", "savedAt": 1752257524286, "version": "1.1", "keyHash": "6a1cde85cc98d3d11c8d5b8704c2a250"}}
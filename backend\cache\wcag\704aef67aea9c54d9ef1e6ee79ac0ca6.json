{"data": {"ruleId": "WCAG-044", "ruleName": "Timing Adjustable", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "passed", "score": 75, "maxScore": 100, "weight": 0.0687, "automated": true, "evidence": [{"type": "code", "description": "Timing pattern detection: high risk", "value": "Timeouts: 0, Sessions: 0, Auto-refresh: 0, Countdowns: 0", "severity": "error", "elementCount": 1, "affectedSelectors": ["Timeouts", "Sessions", "Auto-refresh", "Countdowns"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-044", "ruleName": "Timing Adjustable", "timestamp": "2025-07-11T18:46:03.128Z"}}}, {"type": "text", "description": "Detected timing patterns", "value": "JavaScript timers, Auto-refresh, Countdown timer, Session timeout", "severity": "info", "elementCount": 1, "affectedSelectors": ["JavaScript", "timers", "Auto-refresh", "Countdown", "timer", "Session", "timeout"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-044", "ruleName": "Timing Adjustable", "timestamp": "2025-07-11T18:46:03.128Z"}}}, {"type": "text", "description": "Timeout control validation: Adequate controls available", "value": "Controls: none needed", "severity": "info", "elementCount": 1, "affectedSelectors": ["Controls", "none", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-044", "ruleName": "Timing Adjustable", "timestamp": "2025-07-11T18:46:03.128Z"}}}, {"type": "text", "description": "Session management analysis: Low risk session management", "value": "Risk level: low, Timeout: 0s", "severity": "info", "elementCount": 1, "affectedSelectors": ["Risk", "level", "low", "Timeout", "s"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-044", "ruleName": "Timing Adjustable", "timestamp": "2025-07-11T18:46:03.128Z"}}}, {"type": "text", "description": "Auto-refresh detection: No problematic auto-refresh detected", "value": "No auto-refresh elements found", "severity": "info", "elementCount": 1, "affectedSelectors": ["No", "auto-refresh", "elements", "found"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-044", "ruleName": "Timing Adjustable", "timestamp": "2025-07-11T18:46:03.128Z"}}}], "recommendations": ["Add user controls for timing-based content (extend, disable, or adjust)"], "executionTime": 22, "originalScore": 75, "thresholdApplied": 75, "scoringDetails": "75.0% (threshold: 75%) - PASSED"}, "timestamp": 1752259563128, "hash": "5a61c0bef5a871831da4f75af8e5b629", "accessCount": 1, "lastAccessed": 1752259563128, "size": 2779, "metadata": {"originalKey": "rule:WCAG-044:053b13d2:add92319", "normalizedKey": "rule_wcag-044_053b13d2_add92319", "savedAt": 1752259563129, "version": "1.1", "keyHash": "7dd70d5035ce09ade997884ea649fd8d"}}
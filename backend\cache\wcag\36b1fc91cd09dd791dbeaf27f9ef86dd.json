{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "metadata": {"scanDuration": 66, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T18:03:32.292Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752257012295, "hash": "465755f14cdc47419d56f544836a3861", "accessCount": 1, "lastAccessed": 1752257012295, "size": 777, "metadata": {"originalKey": "WCAG-060:WCAG-060:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-060_wcag-060_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752257012295, "version": "1.1", "keyHash": "68cf86d9b875aaff4ec797e310982824"}}
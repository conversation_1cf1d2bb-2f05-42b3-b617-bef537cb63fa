{"data": [{"type": "text", "description": "Tab navigation is functional", "value": "Focus moved to: a", "severity": "info", "elementCount": 1, "affectedSelectors": ["Focus", "moved", "to", "a"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 2, "checkSpecificData": {"totalInteractiveElements": 2, "keyboardAccessibleElements": 2, "automationRate": 0.85, "checkType": "keyboard-accessibility", "interactionPatterns": true, "focusManagement": true, "evidenceIndex": 0, "ruleId": "WCAG-005", "ruleName": "Keyboard", "timestamp": "2025-07-11T18:44:58.903Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Reverse tab navigation is functional", "value": "Shift+Tab successfully moves focus backwards", "severity": "info", "elementCount": 1, "affectedSelectors": ["Shift", "Tab", "successfully", "moves", "focus", "backwards"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 2, "checkSpecificData": {"totalInteractiveElements": 2, "keyboardAccessibleElements": 2, "automationRate": 0.85, "checkType": "keyboard-accessibility", "interactionPatterns": true, "focusManagement": true, "evidenceIndex": 1, "ruleId": "WCAG-005", "ruleName": "Keyboard", "timestamp": "2025-07-11T18:44:58.903Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259498903, "hash": "dd581b930082a7b30c32edd78b831c64", "accessCount": 1, "lastAccessed": 1752259498903, "size": 1456, "metadata": {"originalKey": "WCAG-005:WCAG-005:dGV4dDpUYWIgbmF2aWdhdGlvbiBpcyBm", "normalizedKey": "wcag-005_wcag-005_dgv4ddpuywigbmf2awdhdglvbibpcybm", "savedAt": 1752259498904, "version": "1.1", "keyHash": "c2192a68ba8931659f25df78ebaf4750"}}
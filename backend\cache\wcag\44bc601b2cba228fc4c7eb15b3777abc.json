{"data": [{"type": "code", "description": "Advanced trap detection: custom trap detected", "value": "Severity: critical, Affected: a:nth-of-type(1), button:nth-of-type(2), a:nth-of-type(3), a:nth-of-type(4), button:nth-of-type(5), a:nth-of-type(6), a:nth-of-type(7), a:nth-of-type(8), input:nth-of-type(10), button:nth-of-type(11), a:nth-of-type(12), a:nth-of-type(13), a:nth-of-type(14), a:nth-of-type(15), a:nth-of-type(16), a:nth-of-type(17), a:nth-of-type(18), a:nth-of-type(19), button:nth-of-type(23), a:nth-of-type(24), a:nth-of-type(25), a:nth-of-type(26), a:nth-of-type(27), a:nth-of-type(28), a:nth-of-type(29), a:nth-of-type(30), a:nth-of-type(31), a:nth-of-type(32), a:nth-of-type(34), a:nth-of-type(35), a:nth-of-type(36), a:nth-of-type(37), a:nth-of-type(38), button:nth-of-type(41), a:nth-of-type(42), a:nth-of-type(43), a:nth-of-type(160), a:nth-of-type(161), a:nth-of-type(162), a:nth-of-type(163), a:nth-of-type(164), a:nth-of-type(165), a:nth-of-type(166), a:nth-of-type(167), a:nth-of-type(168), a:nth-of-type(169), a:nth-of-type(170), a:nth-of-type(171), a:nth-of-type(172), a:nth-of-type(173), a:nth-of-type(174), a:nth-of-type(175), a:nth-of-type(176), a:nth-of-type(177), a:nth-of-type(178), a:nth-of-type(179), a:nth-of-type(180), a:nth-of-type(181), a:nth-of-type(182), a:nth-of-type(183), a:nth-of-type(184), a:nth-of-type(185), a:nth-of-type(186), a:nth-of-type(187), a:nth-of-type(188), a:nth-of-type(189), a:nth-of-type(190), a:nth-of-type(207), a:nth-of-type(208), a:nth-of-type(209), a:nth-of-type(210), a:nth-of-type(211), a:nth-of-type(212), a:nth-of-type(213), a:nth-of-type(214), Escape routes: 0", "severity": "error", "elementCount": 1, "affectedSelectors": ["Severity", "critical", "Affected", "a", "nth-of-type", "button", "input", "Escape", "routes"], "metadata": {"scanDuration": 238, "elementsAnalyzed": 2, "checkSpecificData": {"automationRate": 0.85, "checkType": "keyboard-trap-analysis", "focusManagementValidation": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "modalTrapDetection": true, "escapeRouteValidation": true, "evidenceIndex": 0, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-11T11:39:30.869Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Escape mechanisms validation: Insufficient escape routes", "value": "Only 1/5 escape mechanisms found", "severity": "warning", "elementCount": 1, "affectedSelectors": ["Only", "escape", "mechanisms", "found"], "metadata": {"scanDuration": 238, "elementsAnalyzed": 2, "checkSpecificData": {"automationRate": 0.85, "checkType": "keyboard-trap-analysis", "focusManagementValidation": true, "advancedFocusTracking": true, "accessibilityPatterns": true, "modalTrapDetection": true, "escapeRouteValidation": true, "evidenceIndex": 1, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-11T11:39:30.869Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752233970869, "hash": "268a1ecc18537af6d67cfba4434abcd9", "accessCount": 1, "lastAccessed": 1752233970869, "size": 3132}
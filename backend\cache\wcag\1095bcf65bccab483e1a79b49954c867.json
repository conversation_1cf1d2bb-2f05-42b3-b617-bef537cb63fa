{"data": {"ruleId": "WCAG-056", "ruleName": "Motion Actuation", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "passed", "score": 100, "maxScore": 100, "weight": 0.0458, "automated": true, "evidence": [{"type": "info", "description": "No motion-triggered functionality detected", "value": "<PERSON> does not appear to use device motion for triggering functions", "severity": "info", "metadata": {"scanDuration": 5372, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-056", "ruleName": "Motion Actuation", "timestamp": "2025-07-11T18:13:20.030Z"}}, "elementCount": 1, "affectedSelectors": ["Page", "does", "not", "appear", "to", "use", "device", "motion", "for", "triggering", "functions"]}], "recommendations": [], "executionTime": 5082, "originalScore": 100, "thresholdApplied": 75, "scoringDetails": "100.0% (threshold: 75%) - PASSED"}, "timestamp": 1752257600030, "hash": "90c116e8b47d91c74cb0b38ff2cd0ccd", "accessCount": 1, "lastAccessed": 1752257600030, "size": 896, "metadata": {"originalKey": "rule:WCAG-056:053b13d2:add92319", "normalizedKey": "rule_wcag-056_053b13d2_add92319", "savedAt": 1752257600031, "version": "1.1", "keyHash": "ad3f425ee4bddd3cbcd322951f0af9a3"}}
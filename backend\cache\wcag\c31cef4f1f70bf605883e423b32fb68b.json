{"data": {"ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0611, "automated": true, "evidence": [{"type": "info", "description": "Advanced layout analysis for skip links context", "element": "skip-links", "value": "{\"overallScore\":80,\"criticalIssues\":[],\"recommendations\":[\"Increase target sizes to meet WCAG minimum requirements\"],\"performanceMetrics\":{\"analysisTime\":1491,\"elementsAnalyzed\":1000,\"breakpointsTested\":5}}", "severity": "info", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "recommendations", "Increase", "target", "sizes", "to", "meet", "WCAG", "minimum", "requirements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "breakpointsTested"], "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.344Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(2)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(2)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.344Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(3)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(3)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.344Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(4)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(4)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.344Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(5)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(5)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.344Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(6)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(6)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.344Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(7)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(7)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.344Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(8)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(8)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.344Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(9)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(9)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(10)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(10)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(11)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(11)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(12)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(12)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(13)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(13)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(14)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(14)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(15)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(15)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(16)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(16)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(17)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(17)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(18)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(18)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(19)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(19)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(20)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(20)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(21)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(21)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(22)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(22)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(23)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(23)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(24)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(24)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(25)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(25)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(26)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(26)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(27)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(27)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1934, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T10:49:02.345Z"}}}], "recommendations": ["Ensure skip links are visible and functional", "Verify skip link targets exist and are properly labeled", "Test skip links with keyboard navigation"], "executionTime": 1513, "originalScore": 40}, "timestamp": 1752230942345, "hash": "7fadf50f1dd31fb5ce00149fecdbb055", "accessCount": 1, "lastAccessed": 1752230942345, "size": 13753}
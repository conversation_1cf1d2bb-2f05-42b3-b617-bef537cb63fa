{"data": [{"element": "#demo-account-login-button", "text": {"text": "Demo Account", "color": {"r": 5, "g": 17, "b": 41, "a": 1, "hex": "#051129", "hsl": {"h": 219.**************, "s": 78.**************, "l": 9.***************}, "luminance": 0.*****************}, "fontSize": 18, "fontWeight": "700", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 5, "g": 17, "b": 41, "a": 1, "hex": "#051129", "hsl": {"h": 219.**************, "s": 78.**************, "l": 9.***************}, "luminance": 0.*****************}}, "background": {"type": "solid", "primaryColor": {"r": 88, "g": 250, "b": 128, "a": 1, "hex": "#58fa80", "hsl": {"h": 134.*************, "s": 94.**************, "l": 66.**************}, "luminance": 0.***************}, "hasTransparency": false, "effectiveColor": {"r": 88, "g": 250, "b": 128, "a": 1, "hex": "#58fa80", "hsl": {"h": 134.*************, "s": 94.**************, "l": 66.**************}, "luminance": 0.***************}, "confidence": 0.9}, "contrast": {"ratio": 13.77, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.****************, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(5, 17, 41)", "backgroundColor": "", "background": "rgb(88, 250, 128) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}, {"element": "#demo-account-login-button", "text": {"text": "Demo Account", "color": {"r": 5, "g": 17, "b": 41, "a": 1, "hex": "#051129", "hsl": {"h": 219.**************, "s": 78.**************, "l": 9.***************}, "luminance": 0.*****************}, "fontSize": 18, "fontWeight": "700", "isLarge": true, "hasTextShadow": false, "hasOutline": true, "effectiveColor": {"r": 5, "g": 17, "b": 41, "a": 1, "hex": "#051129", "hsl": {"h": 219.**************, "s": 78.**************, "l": 9.***************}, "luminance": 0.*****************}}, "background": {"type": "solid", "primaryColor": {"r": 88, "g": 250, "b": 128, "a": 1, "hex": "#58fa80", "hsl": {"h": 134.*************, "s": 94.**************, "l": 66.**************}, "luminance": 0.***************}, "hasTransparency": false, "effectiveColor": {"r": 88, "g": 250, "b": 128, "a": 1, "hex": "#58fa80", "hsl": {"h": 134.*************, "s": 94.**************, "l": 66.**************}, "luminance": 0.***************}, "confidence": 0.9}, "contrast": {"ratio": 13.77, "passes": {"aa": true, "aaa": true, "aaLarge": true, "aaaLarge": true}, "recommendation": "Excellent contrast ratio - meets all WCAG requirements", "confidence": 0.****************, "issues": []}, "cssCustomProperties": {}, "computedStyles": {"color": "rgb(5, 17, 41)", "backgroundColor": "", "background": "rgb(88, 250, 128) none repeat scroll 0% 0% / auto padding-box border-box", "fontSize": "", "fontWeight": "", "textShadow": "", "webkitTextStroke": "", "opacity": "1", "filter": "none"}, "accessibility": {"isAccessible": true, "level": "AAA", "improvements": ["Verify that text effects do not interfere with readability"]}}], "timestamp": 1752233838116, "hash": "6dfb02bde9117fdbc69d40950ce2f31f", "accessCount": 1, "lastAccessed": 1752233838116, "size": 3013}
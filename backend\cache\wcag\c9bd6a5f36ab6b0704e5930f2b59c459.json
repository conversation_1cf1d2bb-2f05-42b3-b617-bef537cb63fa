{"data": {"ruleId": "WCAG-060", "ruleName": "Unusual Words", "category": "understandable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0305, "automated": true, "evidence": [{"type": "text", "description": "Unusual words without definitions", "value": "Found 34 unusual words that may need definitions for AAA compliance", "elementCount": 1, "affectedSelectors": ["Found", "unusual", "words", "that", "may", "need", "definitions", "for", "AAA", "compliance"], "severity": "warning", "metadata": {"scanDuration": 99, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.238Z"}}}, {"type": "text", "description": "Unusual word without definition: \"json\"", "value": "Context: \"n reporting or export as CSV, JSON and XML to take into your BI\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 99, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.238Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "n", "reporting", "or", "export", "as", "CSV", "JSON", "and", "XML", "to", "take", "into", "your", "BI"]}, {"type": "text", "description": "Unusual word without definition: \"xml\"", "value": "Context: \"ng or export as CSV, JSON and XML to take into your BI tools, f\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 99, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.238Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "ng", "or", "export", "as", "CSV", "JSON", "and", "XML", "to", "take", "into", "your", "BI", "tools", "f"]}, {"type": "text", "description": "Unusual word without definition: \"rest\"", "value": "Context: \"risks are alerted.Get started REST API & WEBHOOKSDeveloper APIs\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 99, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.238Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "risks", "are", "alerted.Get", "started", "REST", "API", "WEBHOOKSDeveloper", "APIs"]}, {"type": "text", "description": "Unusual word without definition: \"ssl\"", "value": "Context: \"nerability scanswithout the hassleScan your websites, servers,\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 99, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.238Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "nerability", "scanswithout", "the", "hassleScan", "your", "websites", "servers"]}, {"type": "text", "description": "Unusual word without definition: \"tls\"", "value": "Context: \"P Zap SslyzeAnalyzes your SSL/TLS configuration and detects bad\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 99, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.238Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "P", "Zap", "SslyzeAnalyzes", "your", "SSL", "TLS", "configuration", "and", "detects", "bad"]}, {"type": "text", "description": "Unusual word without definition: \"leverage\"", "value": "Context: \"YOUR ENTIRE ATTACK SURFACELeverage the industry's  most-trusted\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 99, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.238Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "YOUR", "ENTIRE", "ATTACK", "SURFACELeverage", "the", "industry", "s", "most-trusted"]}, {"type": "text", "description": "Unusual word without definition: \"proven\"", "value": "Context: \"ities and secure your company.PROVEN TOOLSLevel up your company's\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 99, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.238Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "ities", "and", "secure", "your", "company.PROVEN", "TOOLSLevel", "up", "company", "s"]}, {"type": "text", "description": "Unusual word without definition: \"soc\"", "value": "Context: \"tial for your compliance with SOC 2, ISO 27001, cyber insurance\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 99, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.238Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "tial", "for", "your", "compliance", "with", "SOC", "ISO", "cyber", "insurance"]}, {"type": "text", "description": "Unusual word without definition: \"iso\"", "value": "Context: \"r your compliance with SOC 2, ISO 27001, cyber insurance, and m\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 99, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.238Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "r", "your", "compliance", "with", "SOC", "ISO", "cyber", "insurance", "and", "m"]}, {"type": "text", "description": "Unusual word without definition: \"gdpr\"", "value": "Context: \"ilityWith regulations such as GDPR and CCPA, failure to maintain\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 99, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.238Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "ilityWith", "regulations", "such", "as", "GDPR", "and", "CCPA", "failure", "to", "maintain"]}], "recommendations": ["Provide definitions for unusual words, jargon, and technical terms", "Use <abbr> elements with title attributes for acronyms", "Consider creating a glossary page for frequently used technical terms", "Provide inline definitions or explanations for specialized vocabulary", "Link unusual words to their definitions in a glossary"], "executionTime": 65, "originalScore": 40}, "timestamp": 1752233948238, "hash": "a22c242c56f4fd61550f2e20062ae8d2", "accessCount": 1, "lastAccessed": 1752233948238, "size": 6884}
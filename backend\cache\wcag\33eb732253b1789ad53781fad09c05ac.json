{"data": {"ruleId": "WCAG-061", "ruleName": "Abbreviations", "category": "understandable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0305, "automated": true, "evidence": [{"type": "text", "description": "Abbreviations without expanded forms", "value": "Found 36 abbreviations that need expanded forms for AAA compliance", "elementCount": 1, "affectedSelectors": ["Found", "abbreviations", "that", "need", "expanded", "forms", "for", "AAA", "compliance"], "severity": "warning", "metadata": {"scanDuration": 920, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.192Z"}}}, {"type": "text", "description": "Abbreviation without expansion: \"proven\"", "value": "Context: \"vulnerabilities and secure your company.PROVEN TOOLSLevel up your company's cybersecur\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 920, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.192Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "vulnerabilities", "and", "secure", "your", "company.PROVEN", "TOOLSLevel", "up", "company", "s", "cybersecur"]}, {"type": "text", "description": "Abbreviation without expansion: \"soc\"", "value": "Context: \"g is essential for your compliance with SOC 2, ISO 27001, cyber insurance, and more\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 920, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.192Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "g", "is", "essential", "for", "your", "compliance", "with", "SOC", "ISO", "cyber", "insurance", "and", "more"]}, {"type": "text", "description": "Abbreviation without expansion: \"iso\"", "value": "Context: \"sential for your compliance with SOC 2, ISO 27001, cyber insurance, and moreLearn m\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 920, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.192Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "sential", "for", "your", "compliance", "with", "SOC", "ISO", "cyber", "insurance", "and", "<PERSON><PERSON><PERSON><PERSON>", "m"]}, {"type": "text", "description": "Abbreviation without expansion: \"gdpr\"", "value": "Context: \"educe liabilityWith regulations such as GDPR and CCPA, failure to maintain reasonabl\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 920, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.192Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "educe", "liabilityWith", "regulations", "such", "as", "GDPR", "and", "CCPA", "failure", "to", "maintain", "<PERSON><PERSON><PERSON>"]}, {"type": "text", "description": "Abbreviation without expansion: \"ccpa\"", "value": "Context: \"bilityWith regulations such as GDPR and CCPA, failure to maintain reasonable securit\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 920, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.192Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "bilityWith", "regulations", "such", "as", "GDPR", "and", "CCPA", "failure", "to", "maintain", "reasonable", "securit"]}, {"type": "text", "description": "Abbreviation without expansion: \"owasp\"", "value": "Context: \"curity vulnerabilitiesIdentify CVEs and OWASP Top 10 issues in your systems. Prioriti\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 920, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.192Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "curity", "vulnerabilitiesIdentify", "CVEs", "and", "OWASP", "Top", "issues", "in", "your", "systems", "Prioriti"]}, {"type": "text", "description": "Abbreviation without expansion: \"reporting\"", "value": "Context: \"arted Run a free scanDemo AccountCUSTOM REPORTING ENGINEComprehensive reports, that alway\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 920, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.192Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "arted", "Run", "a", "free", "scanDemo", "AccountCUSTOM", "REPORTING", "ENGINEComprehensive", "reports", "that", "alway"]}, {"type": "text", "description": "Abbreviation without expansion: \"pdf\"", "value": "Context: \"ate vulnerability risksGet an executive PDF to share. See at a glance the vulnerabi\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 920, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.192Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "ate", "vulnerability", "risksGet", "an", "executive", "PDF", "to", "share", "See", "at", "a", "glance", "the", "vulnerabi"]}, {"type": "text", "description": "Abbreviation without expansion: \"csv\"", "value": "Context: \"Use our built in reporting or export as CSV, JSON and XML to take into your BI tool\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 920, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.192Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "Use", "our", "built", "in", "reporting", "or", "export", "as", "CSV", "JSON", "and", "XML", "to", "take", "into", "your", "BI", "tool"]}, {"type": "text", "description": "Abbreviation without expansion: \"json\"", "value": "Context: \"ur built in reporting or export as CSV, JSON and XML to take into your BI tools, for\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 920, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.193Z"}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "ur", "built", "in", "reporting", "or", "export", "as", "CSV", "JSON", "and", "XML", "to", "take", "into", "your", "BI", "tools", "for"]}], "recommendations": ["Use <abbr> elements with title attributes for abbreviations", "Provide inline expansions for abbreviations on first use", "Create a glossary page for frequently used abbreviations", "Consider the reading level and technical knowledge of your audience", "Ensure abbreviations are consistently expanded throughout the site"], "executionTime": 863, "originalScore": 50}, "timestamp": 1752233949193, "hash": "96091d1bd59f9eed1c0bcec62b3607c3", "accessCount": 1, "lastAccessed": 1752233949193, "size": 7364}
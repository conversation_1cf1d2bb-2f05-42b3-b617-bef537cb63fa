{"data": {"ruleId": "WCAG-022", "ruleName": "Accessible Authentication (Minimum)", "category": "understandable", "wcagVersion": "2.2", "successCriterion": "0.0.0", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.05, "automated": false, "evidence": [{"type": "error", "description": "Error during authentication analysis", "value": "this.getElementSelector is not a function\npptr:evaluate;AccessibleAuthenticationCheck.detectCognitiveFunctionTests%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cchecks%5Caccessible-authentication.ts%3A352%3A27):19:40\nNodeList.forEach (<anonymous>)", "severity": "error", "elementCount": 1, "affectedSelectors": ["this.getElementSelector", "is", "not", "a", "function", "pptr", "evaluate", "AccessibleAuthenticationCheck.detectCognitiveFunctionTests", "D", "A", "CWeb", "projects", "CCom<PERSON>ly", "Checker", "Cbackend", "Csrc", "Ccompliance", "Cwcag", "Cchecks", "Caccessible-authentication.ts", "A352", "A27", "NodeList.forEach", "anonymous"], "metadata": {"scanDuration": 1030, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-022", "ruleName": "Accessible Authentication (Minimum)", "timestamp": "2025-07-11T18:46:01.559Z"}}}], "recommendations": ["Review authentication mechanisms manually for accessibility compliance"], "executionTime": 0, "errorMessage": "Failed to analyze authentication accessibility", "manualReviewItems": []}, "timestamp": 1752259561559, "hash": "787712bbfa6260e45512b9a067f6e1b6", "accessCount": 1, "lastAccessed": 1752259561559, "size": 1427, "metadata": {"originalKey": "rule:WCAG-022:053b13d2:add92319", "normalizedKey": "rule_wcag-022_053b13d2_add92319", "savedAt": 1752259561559, "version": "1.1", "keyHash": "509ec84aae7cd1ce63651abd03fb9310"}}
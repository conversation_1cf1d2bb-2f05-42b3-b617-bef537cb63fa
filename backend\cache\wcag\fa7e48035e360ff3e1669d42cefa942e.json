{"data": [{"type": "code", "description": "Moving content without user controls: JavaScript-based animation detected", "value": "Moving content detected", "selector": "script", "severity": "error", "elementCount": 1, "affectedSelectors": ["script", "Moving", "content", "detected"], "metadata": {"scanDuration": 56, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-analysis", "mediaElementsAnalyzed": true, "animationDetection": true, "accessibilityPatterns": true, "multimediaAccessibilityTesting": true, "evidenceIndex": 0, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T10:45:51.365Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752230751365, "hash": "9fa540a9cb4af39ed0d38778bcd83994", "accessCount": 1, "lastAccessed": 1752230751365, "size": 794}
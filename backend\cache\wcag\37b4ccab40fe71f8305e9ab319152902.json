{"data": [{"type": "error", "description": "Error during authentication analysis", "value": "this.getElementSelector is not a function\npptr:evaluate;AccessibleAuthenticationCheck.detectCognitiveFunctionTests%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cchecks%5Caccessible-authentication.ts%3A352%3A27):19:40\nNodeList.forEach (<anonymous>)", "severity": "error", "elementCount": 1, "affectedSelectors": ["this.getElementSelector", "is", "not", "a", "function", "pptr", "evaluate", "AccessibleAuthenticationCheck.detectCognitiveFunctionTests", "D", "A", "CWeb", "projects", "CCom<PERSON>ly", "Checker", "Cbackend", "Csrc", "Ccompliance", "Cwcag", "Cchecks", "Caccessible-authentication.ts", "A352", "A27", "NodeList.forEach", "anonymous"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.5, "checkType": "authentication-flow-analysis", "manualReviewRequired": false, "authenticationElementDetection": true, "cognitiveLoadAssessment": true, "alternativeMethodValidation": true, "formAccessibilityAnalysis": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-022", "ruleName": "Accessible Authentication (Minimum)", "timestamp": "2025-07-11T18:01:37.489Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752256897490, "hash": "0d0686427c0a43226a57c41f735a175e", "accessCount": 1, "lastAccessed": 1752256897490, "size": 1368, "metadata": {"originalKey": "WCAG-022:WCAG-022:ZXJyb3I6RXJyb3IgZHVyaW5nIGF1dGhl", "normalizedKey": "wcag-022_wcag-022_zxjyb3i6rxjyb3igzhvyaw5nigf1dghl", "savedAt": 1752256897490, "version": "1.1", "keyHash": "2fc82d40cf7884b6e7c48782f40c1064"}}
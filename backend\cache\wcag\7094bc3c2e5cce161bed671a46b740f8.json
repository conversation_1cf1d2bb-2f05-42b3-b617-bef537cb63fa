{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "metadata": {"scanDuration": 5, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.9, "checkType": "bypass-mechanism-analysis", "skipLinkAnalysis": true, "landmarkAnalysis": true, "navigationAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-028", "ruleName": "Bypass Blocks", "timestamp": "2025-07-11T18:03:54.386Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752257034387, "hash": "6c519e22b9f5dc09ab3d8db8ba0a3ef5", "accessCount": 1, "lastAccessed": 1752257034387, "size": 835, "metadata": {"originalKey": "WCAG-028:WCAG-028:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-028_wcag-028_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752257034387, "version": "1.1", "keyHash": "f4bc57f2c6f956f134b24e0ce248429b"}}
{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame 'A362B1B11B79F8E7E1503ABC8B8AFA25'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A362B1B11B79F8E7E1503ABC8B8AFA25"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.9, "checkType": "bypass-mechanism-analysis", "skipLinkAnalysis": true, "landmarkAnalysis": true, "navigationAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-028", "ruleName": "Bypass Blocks", "timestamp": "2025-07-11T18:49:53.849Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259793849, "hash": "ddfc2b82899c068497baf199b79eb0d3", "accessCount": 1, "lastAccessed": 1752259793849, "size": 836, "metadata": {"originalKey": "WCAG-028:WCAG-028:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-028_wcag-028_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752259793849, "version": "1.1", "keyHash": "f4bc57f2c6f956f134b24e0ce248429b"}}
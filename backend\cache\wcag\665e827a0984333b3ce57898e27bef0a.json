{"data": {"ruleId": "WCAG-025", "ruleName": "Landmarks", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "info", "description": "Advanced layout analysis for landmark structure validation", "element": "landmark-elements", "value": "{\"overallScore\":80,\"criticalIssues\":[],\"recommendations\":[\"Increase target sizes to meet WCAG minimum requirements\"],\"performanceMetrics\":{\"analysisTime\":887,\"elementsAnalyzed\":1000,\"breakpointsTested\":5}}", "severity": "info", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "recommendations", "Increase", "target", "sizes", "to", "meet", "WCAG", "minimum", "requirements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "breakpointsTested"], "metadata": {"scanDuration": 959, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-025", "ruleName": "Landmarks", "timestamp": "2025-07-11T11:39:28.694Z"}}}, {"type": "warning", "description": "Content elements outside landmark regions", "value": "5 elements not in landmarks", "selector": "p, h1, h2, h3, h4, h5, h6, article, section, div", "elementCount": 10, "affectedSelectors": ["p", "h1", "h2", "h3", "h4", "h5", "h6", "article", "section", "div", "elements", "not", "in", "landmarks"], "severity": "warning", "metadata": {"scanDuration": 959, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-025", "ruleName": "Landmarks", "timestamp": "2025-07-11T11:39:28.694Z"}}}, {"type": "info", "description": "Good landmark structure detected", "value": "Found 4 landmarks with proper structure", "selector": "main, nav, header, footer, aside", "elementCount": 5, "affectedSelectors": ["main", "nav", "header", "footer", "aside", "Found", "landmarks", "with", "proper", "structure"], "severity": "info", "metadata": {"scanDuration": 959, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-025", "ruleName": "Landmarks", "timestamp": "2025-07-11T11:39:28.694Z"}}}], "recommendations": ["Move content into appropriate landmark regions", "Use semantic HTML5 landmark elements (main, nav, header, footer, aside)", "Ensure all content is contained within appropriate landmarks", "Provide accessible names for multiple landmarks of the same type"], "executionTime": 895, "originalScore": 90}, "timestamp": 1752233968694, "hash": "bb6777049f163e89751eff5f4f2ab6f8", "accessCount": 1, "lastAccessed": 1752233968694, "size": 2459}
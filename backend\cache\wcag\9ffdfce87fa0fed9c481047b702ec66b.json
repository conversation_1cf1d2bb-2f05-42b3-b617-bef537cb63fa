{"data": [{"type": "info", "description": "Advanced keyboard accessibility analysis with comprehensive interaction testing", "element": "interactive-elements", "value": "{\"overallScore\":17,\"criticalIssues\":[\"Focus indicator obscured for element: a.w-full\",\"Focus indicator obscured for element: #demo-account-login-button\",\"Focus indicator obscured for element: a.mt-10\",\"Focus indicator obscured for element: a.mt-10\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.w-full\",\"Focus indicator obscured for element: #demo-account-login-button\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Failed to analyze focus obstruction for #headlessui-menu-button-:Rspmm:: DOMException: SyntaxError: Failed to execute 'querySelector' on 'Document': '#headlessui-menu-button-:Rspmm:' is not a valid selector.\",\"Failed to analyze focus obstruction for #headlessui-menu-button-:Ru9mm:: DOMException: SyntaxError: Failed to execute 'querySelector' on 'Document': '#headlessui-menu-button-:Ru9mm:' is not a valid selector.\",\"Focus indicator obscured for element: button.right-1\",\"Focus indicator obscured for element: button.rounded-full\",\"Focus indicator obscured for element: button.rounded-full\",\"Focus indicator obscured for element: input.grow\"],\"recommendations\":[\"Ensure focus indicator for a.w-full is not obscured by other elements\",\"Ensure focus indicator for #demo-account-login-button is not obscured by other elements\",\"Ensure focus indicator for a.mt-10 is not obscured by other elements\",\"Ensure focus indicator for a.mt-10 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.w-full is not obscured by other elements\",\"Ensure focus indicator for #demo-account-login-button is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for button.right-1 is not obscured by other elements\",\"Ensure focus indicator for button.rounded-full is not obscured by other elements\",\"Ensure focus indicator for button.rounded-full is not obscured by other elements\",\"Ensure focus indicator for input.grow is not obscured by other elements\"],\"performanceMetrics\":{\"analysisTime\":1292,\"elementsAnalyzed\":75,\"obstructionsFound\":60}}", "severity": "error", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "Focus", "indicator", "obscured", "for", "element", "a.w-full", "#demo-account-login-button", "a.mt-10", "a.mt-4", "a.relative", "a.text-sm", "Failed", "to", "analyze", "focus", "obstruction", "#headlessui-menu-button-", "Rspmm", "DOMException", "SyntaxError", "execute", "querySelector", "on", "Document", "is", "not", "a", "valid", "selector", "Ru9mm", "button.right-1", "button.rounded-full", "input.grow", "recommendations", "Ensure", "by", "other", "elements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "obstructionsFound"], "fixExample": {"before": "Current implementation", "after": "<button tabindex=\"0\" onkeydown=\"handleKeyDown(event)\">Action</button>", "description": "Ensure keyboard accessibility", "codeExample": "<button tabindex=\"0\" onkeydown=\"handleKeyDown(event)\">Action</button>", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/keyboard.html", "https://webaim.org/techniques/keyboard/"]}, "metadata": {"scanDuration": 1309, "elementsAnalyzed": 3, "checkSpecificData": {"automationRate": 0.85, "checkType": "keyboard-interaction-analysis", "keyboardTesting": true, "focusManagement": true, "advancedKeyboardTracking": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-051", "ruleName": "Keyboard Accessible", "timestamp": "2025-07-11T11:38:52.515Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Keyboard accessibility issue: Disabled element still in tab order", "value": "button | Focusable: true | TabIndex: 0", "selector": "button:nth-of-type(22)", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(22)", "button", "Focusable", "true", "TabIndex"], "severity": "warning", "metadata": {"scanDuration": 1309, "elementsAnalyzed": 3, "checkSpecificData": {"automationRate": 0.85, "checkType": "keyboard-interaction-analysis", "keyboardTesting": true, "focusManagement": true, "advancedKeyboardTracking": true, "accessibilityPatterns": true, "evidenceIndex": 1, "ruleId": "WCAG-051", "ruleName": "Keyboard Accessible", "timestamp": "2025-07-11T11:38:52.515Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Keyboard accessibility issue: Disabled element still in tab order", "value": "button | Focusable: true | TabIndex: 0", "selector": "button:nth-of-type(40)", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(40)", "button", "Focusable", "true", "TabIndex"], "severity": "warning", "metadata": {"scanDuration": 1309, "elementsAnalyzed": 3, "checkSpecificData": {"automationRate": 0.85, "checkType": "keyboard-interaction-analysis", "keyboardTesting": true, "focusManagement": true, "advancedKeyboardTracking": true, "accessibilityPatterns": true, "evidenceIndex": 2, "ruleId": "WCAG-051", "ruleName": "Keyboard Accessible", "timestamp": "2025-07-11T11:38:52.515Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752233932515, "hash": "1a16bdc83727780fa794acb8a0d05d8b", "accessCount": 1, "lastAccessed": 1752233932515, "size": 11642}
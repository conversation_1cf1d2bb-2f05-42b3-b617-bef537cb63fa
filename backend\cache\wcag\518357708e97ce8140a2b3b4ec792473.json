{"data": {"landmarks": {"total": 30, "valid": 30, "foundTypes": ["main", "navigation", "banner", "contentinfo", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "region", "form"], "missing": [], "duplicates": ["region"], "analysis": [{"type": "main", "selector": "div > div.relative.flex > main.flex.w-full", "hasLabel": false, "isUnique": true, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "navigation", "selector": "div > div.relative.flex > header.fixed.top-0 > nav.flex.w-full", "hasLabel": false, "isUnique": true, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "banner", "selector": "div > div.relative.flex > header.fixed.top-0", "hasLabel": false, "isUnique": true, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "contentinfo", "selector": "div > div.relative.flex > footer.flex.w-full", "hasLabel": false, "isUnique": true, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div > div.relative.flex > main.flex.w-full > div.flex.w-full > section.w-full.max-w-[1400px]", "hasLabel": false, "isUnique": true, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div > div.relative.flex > main.flex.w-full > div.flex.w-full > section.w-full.max-w-[1400px]", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div > div.relative.flex > main.flex.w-full > div.flex.w-full > section.w-full.max-w-[1400px]", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div > div.relative.flex > main.flex.w-full > div.flex.w-full > section.w-full.max-w-[1400px]", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div > div.relative.flex > main.flex.w-full > div.flex.w-full > section.w-full.max-w-[1400px]", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div > div.relative.flex > main.flex.w-full > div.flex.w-full > section.w-full.max-w-[1400px]", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > main.flex.w-full > div.w-full.overflow-x-hidden > div.flex.w-full > section.w-full.max-w-[1400px]", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div > div.relative.flex > main.flex.w-full > div.flex.w-full > section.w-full.max-w-[1400px]", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div > div.relative.flex > main.flex.w-full > div.flex.w-full > section.w-full.max-w-[1400px]", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div > div.relative.flex > main.flex.w-full > div.relative.flex > section.w-full.max-w-[1400px]", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "main.flex.w-full > div.relative.flex > section.w-full.max-w-[1400px] > div.z-10.flex > section.w-full.max-w-[1400px]", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > footer.flex.w-full > div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > footer.flex.w-full > div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > footer.flex.w-full > div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > footer.flex.w-full > div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > footer.flex.w-full > div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > footer.flex.w-full > div.hidden.w-full > div.flex.w-full > section.flex.h-[27.75rem]", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > footer.flex.w-full > div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > footer.flex.w-full > div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > footer.flex.w-full > div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > footer.flex.w-full > div.hidden.w-full > div.flex.w-full > section.flex.h-[27.75rem]", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > footer.flex.w-full > div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > footer.flex.w-full > div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > footer.flex.w-full > div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "region", "selector": "div.relative.flex > footer.flex.w-full > div.hidden.w-full > div.flex.w-full > section.flex.h-[27.75rem]", "hasLabel": false, "isUnique": false, "isProperlyNested": true, "issues": [], "recommendations": []}, {"type": "form", "selector": "#trial-scan", "hasLabel": false, "isUnique": true, "isProperlyNested": true, "issues": [], "recommendations": []}]}, "headings": {"total": 76, "structure": [{"level": 1, "text": "Trusted vulnerability scanswithout the hassle", "selector": "main.flex.w-full > div.flex.w-full > section.w-full.max-w-[1400px] > div.relative.z-10 > h1.font-formadjrdisplay.text-4xl", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "Preferred by teams who take cybersecurity seriously", "selector": "main.flex.w-full > div.flex.w-full > section.w-full.max-w-[1400px] > div.flex.flex-col > h3.mb-14.text-center", "hasProperHierarchy": false, "isSkipped": true, "isEmpty": false, "issues": ["Heading level skipped (h1 to h3)"]}, {"level": 2, "text": "Level up your company's cybersecurity", "selector": "div.relative.flex > main.flex.w-full > div.flex.w-full > section.w-full.max-w-[1400px] > h2.text-center.font-formadjrdisplay", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "Meet compliance requirements", "selector": "div.grid.w-full > div.w-full.rounded-xl > div.relative.z-10 > div.flex.flex-col > h3.font-formadjrdisplay.text-2xl", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "Reduce liability", "selector": "div.grid.w-full > div.w-full.rounded-xl > div.flex.h-full > div.flex.flex-col > h3.font-formadjrdisplay.text-2xl", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "Mitigate security vulnerabilities", "selector": "div.grid.w-full > div.w-full.rounded-xl > div.relative.z-10 > div.flex.flex-col > h3.font-formadjrdisplay.text-2xl", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 3, "text": "Detect misconfigurations", "selector": "div.grid.w-full > div.w-full.rounded-xl > div.flex.h-full > div.flex.flex-col > h3.font-formadjrdisplay.text-2xl", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Comprehensive reports, that always look good", "selector": "div.relative.flex > main.flex.w-full > div.flex.w-full > section.w-full.max-w-[1400px] > h2.text-center.font-formadjrdisplay", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "What our customers are saying", "selector": "div.w-full.overflow-x-hidden > div.flex.w-full > section.w-full.max-w-[1400px] > div.flex.w-full > h2.text-center.font-formadjrdisplay", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Leverage the industry's  most-trusted security tools", "selector": "div.relative.flex > main.flex.w-full > div.flex.w-full > section.w-full.max-w-[1400px] > h2.text-center.font-formadjrdisplay", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 4, "text": "OpenVAS", "selector": "section.w-full.max-w-[1400px] > div.mt-10.grid > div.flex.size-full > div.relative.flex > h4.font-formadjrdisplay.text-2xl", "hasProperHierarchy": false, "isSkipped": true, "isEmpty": false, "issues": ["Heading level skipped (h2 to h4)"]}, {"level": 4, "text": "Nmap", "selector": "section.w-full.max-w-[1400px] > div.mt-10.grid > div.flex.size-full > div.relative.flex > h4.font-formadjrdisplay.text-2xl", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 4, "text": "OWASP ZAP", "selector": "section.w-full.max-w-[1400px] > div.mt-10.grid > div.flex.size-full > div.relative.flex > h4.font-formadjrdisplay.text-2xl", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 4, "text": "Sslyze", "selector": "section.w-full.max-w-[1400px] > div.mt-10.grid > div.flex.size-full > div.relative.flex > h4.font-formadjrdisplay.text-2xl", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 4, "text": "<PERSON><PERSON><PERSON>", "selector": "section.w-full.max-w-[1400px] > div.mt-10.grid > div.flex.size-full > div.relative.flex > h4.font-formadjrdisplay.text-2xl", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 4, "text": "OpenAPI (Swagger)", "selector": "section.w-full.max-w-[1400px] > div.mt-10.grid > div.flex.size-full > div.relative.flex > h4.font-formadjrdisplay.text-2xl", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 4, "text": "Snyk", "selector": "section.w-full.max-w-[1400px] > div.mt-10.grid > div.flex.size-full > div.relative.flex > h4.font-formadjrdisplay.text-2xl", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 4, "text": "Import via API", "selector": "section.w-full.max-w-[1400px] > div.mt-10.grid > div.flex.size-full > div.relative.flex > h4.font-formadjrdisplay.text-2xl", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 2, "text": "Dive into HostedScan's Knowledge Hub", "selector": "main.flex.w-full > div.flex.w-full > section.w-full.max-w-[1400px] > div.mb-14.mt-4 > h2.text-center.font-formadjrdisplay", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 4, "text": "What’s new at HostedScan - June 2025", "selector": "div.slider-wrapper.axis-horizontal > ul.slider.animated > li.slide.selected > a.relative.flex > h4.z-10.text-left", "hasProperHierarchy": false, "isSkipped": true, "isEmpty": false, "issues": ["Heading level skipped (h2 to h4)"]}, {"level": 4, "text": "Our Favorite Open-Source Vulnerability Scanning Tools", "selector": "div.slider-wrapper.axis-horizontal > ul.slider.animated > li.slide > a.relative.flex > h4.z-10.text-left", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 4, "text": "Vulnerability Management SLAs: A Guide", "selector": "div.slider-wrapper.axis-horizontal > ul.slider.animated > li.slide > a.relative.flex > h4.z-10.text-left", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 4, "text": "Upcoming Improvements for Q2 2024", "selector": "div.slider-wrapper.axis-horizontal > ul.slider.animated > li.slide > a.relative.flex > h4.z-10.text-left", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 4, "text": "Introducing HostedScan's Knowledge Hub and a couple of tips", "selector": "div.slider-wrapper.axis-horizontal > ul.slider.animated > li.slide > a.relative.flex > h4.z-10.text-left", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 5, "text": "Become a more secure company today", "selector": "div.relative.flex > main.flex.w-full > div.relative.flex > section.w-full.max-w-[1400px] > h5.z-10.font-formadjrdisplay", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Product", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Comparisons", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Scanners", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Use Cases", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Use Cases", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": true, "issues": ["Heading is empty"]}, {"level": 1, "text": "Compliance Use Cases", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Partners", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Cybersecurity Tools", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Get in touch", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.flex-col > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Address", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.flex-col > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Socials", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.flex-col > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Get in touch", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-5 > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Address", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-5 > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Socials", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-5 > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Socials", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.h-[27.75rem] > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Get in touch", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.h-[27.75rem] > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Address", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.h-[27.75rem] > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Product", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Comparisons", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Scanners", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Use Cases", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": true, "issues": ["Heading is empty"]}, {"level": 1, "text": "", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": true, "issues": ["Heading is empty"]}, {"level": 1, "text": "Compliance Use Cases", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Cybersecurity Tools", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Partners", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Get in touch", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.flex-col > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Address", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.flex-col > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Socials", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.flex-col > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Get in touch", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-5 > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Address", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-5 > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Socials", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-5 > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Socials", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.h-[27.75rem] > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Get in touch", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.h-[27.75rem] > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Address", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.h-[27.75rem] > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Use Cases", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Compliance Use Cases", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Comparisons", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Scanners", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Cybersecurity Tools", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Partners", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-2 > div > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Get in touch", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.flex-col > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Address", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.flex-col > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Socials", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.flex-col > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Get in touch", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-5 > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Address", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-5 > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Socials", "selector": "div.hidden.w-full > div.flex.w-full > section.grid.grid-cols-5 > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Socials", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.h-[27.75rem] > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Get in touch", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.h-[27.75rem] > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}, {"level": 1, "text": "Address", "selector": "div.hidden.w-full > div.flex.w-full > section.flex.h-[27.75rem] > div.flex.flex-col > h1.text-base.font-semibold", "hasProperHierarchy": true, "isSkipped": false, "isEmpty": false, "issues": []}], "hasH1": true, "hasProperHierarchy": false, "skippedLevels": [2, 3], "emptyHeadings": 3, "issues": ["Heading levels skipped", "Empty headings found"]}, "aria": {"total": 6, "valid": 6, "patterns": [{"pattern": "button", "element": "button", "selector": "#headlessui-menu-button-:Rspmm:", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "button", "selector": "#headlessui-menu-button-:Ru9mm:", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "button", "selector": "#headlessui-menu-button-:Rfdmm:", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "button", "selector": "label.flex.w-full > div.flex.w-full > div.flex.w-full > div.relative.flex > button.right-1.flex", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "button", "selector": "section.w-full.max-w-[1400px] > div.w-full > div.mt-12.flex > div.flex.flex-row > button.rounded-full.bg-light-gray", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}, {"pattern": "button", "element": "button", "selector": "section.w-full.max-w-[1400px] > div.w-full > div.mt-12.flex > div.flex.flex-row > button.rounded-full.bg-light-gray", "isValid": true, "requiredAttributes": [], "missingAttributes": [], "invalidValues": [], "issues": [], "recommendations": []}], "commonIssues": [], "invalidRoles": [], "missingLabels": []}, "semanticElements": {"total": 5, "used": ["header", "nav", "main", "section", "footer"], "missing": [], "misused": ["section", "section", "section"]}, "overallScore": 30, "criticalIssues": [], "recommendations": ["Fix heading hierarchy - avoid skipping levels"]}, "timestamp": 1752233807198, "hash": "5fac39decb175e6074f824ee495fb803", "accessCount": 1, "lastAccessed": 1752233807198, "size": 26530}
{"data": {"ruleId": "WCAG-049", "ruleName": "Link Context", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0611, "automated": true, "evidence": [{"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Physician <PERSON>g\" -> https://schedule.tigerconnect.com/", "selector": "a:nth-of-type(6)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(6)", "Physician", "Scheduling", "https", "schedule.tigerconnect.com"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.117Z"}}}, {"type": "code", "description": "Link with unclear purpose: ", "value": "\"Home\" -> https://tigerconnect.com/", "selector": "a:nth-of-type(10)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(10)", "Home", "https", "tigerconnect.com"], "severity": "info", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.117Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Nurses\" -> https://tigerconnect.com/healthcare-professionals/nurses/", "selector": "a:nth-of-type(19)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(19)", "Nurses", "https", "tigerconnect.com", "healthcare-professionals", "nurses"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.117Z"}}}, {"type": "code", "description": "Link with unclear purpose: Very short link text, Duplicate link text with different destinations", "value": "\"IT\" -> https://tigerconnect.com/healthcare-professionals/it/", "selector": "a:nth-of-type(21)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(21)", "IT", "https", "tigerconnect.com", "healthcare-professionals", "it"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.117Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources/", "selector": "a:nth-of-type(22)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(22)", "Resources", "https", "tigerconnect.com", "resources"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.117Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Physician Sc<PERSON>uling\" -> https://tigerconnect.com/products/physician-scheduling/", "selector": "a:nth-of-type(37)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(37)", "Physician", "Scheduling", "https", "tigerconnect.com", "products", "physician-scheduling"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.117Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Clinical Collaboration\" -> https://tigerconnect.com/products/clinical-collaboration-platform/", "selector": "a:nth-of-type(38)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(38)", "Clinical", "Collaboration", "https", "tigerconnect.com", "products", "clinical-collaboration-platform"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.117Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Alarm Management & Event Notification\" -> https://tigerconnect.com/products/alarm-management-event-notification-software/", "selector": "a:nth-of-type(39)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(39)", "Alarm", "Management", "Event", "Notification", "https", "tigerconnect.com", "products", "alarm-management-event-notification-software"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.117Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Patient Engagement\" -> https://tigerconnect.com/products/patient-engagement", "selector": "a:nth-of-type(40)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(40)", "Patient", "Engagement", "https", "tigerconnect.com", "products", "patient-engagement"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources/", "selector": "a:nth-of-type(43)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(43)", "Resources", "https", "tigerconnect.com", "resources"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources", "selector": "a:nth-of-type(57)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(57)", "Resources", "https", "tigerconnect.com", "resources"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: ", "value": "\"Blog\" -> https://tigerconnect.com/resources/blog-articles/", "selector": "a:nth-of-type(60)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(60)", "Blog", "https", "tigerconnect.com", "resources", "blog-articles"], "severity": "info", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Case Studies\" -> https://tigerconnect.com/resources/case-studies/", "selector": "a:nth-of-type(61)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(61)", "Case", "Studies", "https", "tigerconnect.com", "resources", "case-studies"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Demo Tours\" -> https://tigerconnect.com/resources/demo-tours/", "selector": "a:nth-of-type(64)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(64)", "Demo", "Tours", "https", "tigerconnect.com", "resources", "demo-tours"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources/", "selector": "a:nth-of-type(65)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(65)", "Resources", "https", "tigerconnect.com", "resources"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources/", "selector": "a:nth-of-type(66)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(66)", "Resources", "https", "tigerconnect.com", "resources"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Webinars\" -> https://tigerconnect.com/resources/webinars/", "selector": "a:nth-of-type(74)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(74)", "Webinars", "https", "tigerconnect.com", "resources", "webinars"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources/", "selector": "a:nth-of-type(79)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(79)", "Resources", "https", "tigerconnect.com", "resources"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Case Studies\" -> https://tigerconnect.com/resources/case-studies/", "selector": "a:nth-of-type(80)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(80)", "Case", "Studies", "https", "tigerconnect.com", "resources", "case-studies"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources/", "selector": "a:nth-of-type(82)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(82)", "Resources", "https", "tigerconnect.com", "resources"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Clinical Collaboration\" -> https://tigerconnect.com/products/physician-scheduling/", "selector": "a:nth-of-type(99)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(99)", "Clinical", "Collaboration", "https", "tigerconnect.com", "products", "physician-scheduling"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Alarm Management & Event Notification\" -> https://tigerconnect.com/products/alarm-management-event-notification-software/", "selector": "a:nth-of-type(103)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(103)", "Alarm", "Management", "Event", "Notification", "https", "tigerconnect.com", "products", "alarm-management-event-notification-software"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Clinical Collaboration\" -> https://tigerconnect.com/products/physician-scheduling/", "selector": "a:nth-of-type(109)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(109)", "Clinical", "Collaboration", "https", "tigerconnect.com", "products", "physician-scheduling"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Alarm Management & Event Notification\" -> https://tigerconnect.com/products/alarm-management-event-notification-software/", "selector": "a:nth-of-type(113)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(113)", "Alarm", "Management", "Event", "Notification", "https", "tigerconnect.com", "products", "alarm-management-event-notification-software"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Clinical Collaboration\" -> https://tigerconnect.com/products/physician-scheduling/", "selector": "a:nth-of-type(119)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(119)", "Clinical", "Collaboration", "https", "tigerconnect.com", "products", "physician-scheduling"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Alarm Management & Event Notification\" -> https://tigerconnect.com/products/alarm-management-event-notification-software/", "selector": "a:nth-of-type(123)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(123)", "Alarm", "Management", "Event", "Notification", "https", "tigerconnect.com", "products", "alarm-management-event-notification-software"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Clinical Collaboration\" -> https://tigerconnect.com/products/physician-scheduling/", "selector": "a:nth-of-type(129)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(129)", "Clinical", "Collaboration", "https", "tigerconnect.com", "products", "physician-scheduling"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Generic link text, Duplicate link text with different destinations", "value": "\"Learn More\" -> https://tigerconnect.com/workflows/critical-response-workflows/", "selector": "a:nth-of-type(136)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(136)", "Learn", "More", "https", "tigerconnect.com", "workflows", "critical-response-workflows"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: ", "value": "\"Play\" -> https://youtu.be/Q1bpy2rezTE", "selector": "a:nth-of-type(137)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(137)", "Play", "https", "youtu.be", "Q1bpy2rezTE"], "severity": "info", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/infographics/ed-to-inpatient-transfers-workflow-lp/", "selector": "a:nth-of-type(138)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(138)", "See", "How", "https", "tigerconnect.com", "resources", "infographics", "ed-to-inpatient-transfers-workflow-lp"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.118Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/case-studies/case-study-innovation-care-partners-lp/", "selector": "a:nth-of-type(139)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(139)", "See", "How", "https", "tigerconnect.com", "resources", "case-studies", "case-study-innovation-care-partners-lp"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/case-studies/baylor-st-lukes-medical-center-lp/", "selector": "a:nth-of-type(140)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(140)", "See", "How", "https", "tigerconnect.com", "resources", "case-studies", "baylor-st-lukes-medical-center-lp"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/case-studies/video-testimonial-tufts-medical-center-lp/", "selector": "a:nth-of-type(141)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(141)", "See", "How", "https", "tigerconnect.com", "resources", "case-studies", "video-testimonial-tufts-medical-center-lp"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/podcasts/time-is-tissue-streamlining-clinical-communication-at-umms/", "selector": "a:nth-of-type(142)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(142)", "See", "How", "https", "tigerconnect.com", "resources", "podcasts", "time-is-tissue-streamlining-clinical-communication-at-umms"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/webinars/break-through-bottlenecks-how-sharp-memorial-is-improving-ed-workflows/", "selector": "a:nth-of-type(143)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(143)", "See", "How", "https", "tigerconnect.com", "resources", "webinars", "break-through-bottlenecks-how-sharp-memorial-is-improving-ed-workflows"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/case-studies/providence-saint-johns-health-center-lp/", "selector": "a:nth-of-type(144)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(144)", "See", "How", "https", "tigerconnect.com", "resources", "case-studies", "providence-saint-johns-health-center-lp"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"See How\" -> https://tigerconnect.com/resources/case-studies/case-study-trinity-health-system-lp/", "selector": "a:nth-of-type(145)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(145)", "See", "How", "https", "tigerconnect.com", "resources", "case-studies", "case-study-trinity-health-system-lp"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Generic link text, Duplicate link text with different destinations", "value": "\"Read More\" -> https://tigerconnect.com/healthcare-professionals/physicians/", "selector": "a:nth-of-type(151)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(151)", "Read", "More", "https", "tigerconnect.com", "healthcare-professionals", "physicians"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Generic link text, Duplicate link text with different destinations", "value": "\"Read More\" -> https://tigerconnect.com/healthcare-professionals/it/", "selector": "a:nth-of-type(152)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(152)", "Read", "More", "https", "tigerconnect.com", "healthcare-professionals", "it"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Generic link text, Duplicate link text with different destinations", "value": "\"Read More\" -> https://tigerconnect.com/healthcare-professionals/nurses/", "selector": "a:nth-of-type(153)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(153)", "Read", "More", "https", "tigerconnect.com", "healthcare-professionals", "nurses"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Generic link text, Duplicate link text with different destinations", "value": "\"Read More\" -> https://tigerconnect.com/healthcare-professionals/execs/", "selector": "a:nth-of-type(154)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(154)", "Read", "More", "https", "tigerconnect.com", "healthcare-professionals", "execs"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Webinars\" -> https://tigerconnect.com/resources/case-studies/", "selector": "a:nth-of-type(159)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(159)", "Webinars", "https", "tigerconnect.com", "resources", "case-studies"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Case Studies\" -> https://tigerconnect.com/resources/case-studies/", "selector": "a:nth-of-type(161)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(161)", "Case", "Studies", "https", "tigerconnect.com", "resources", "case-studies"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: ", "value": "\"Blogs\" -> https://tigerconnect.com/resources/blog-articles/", "selector": "a:nth-of-type(163)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(163)", "Blogs", "https", "tigerconnect.com", "resources", "blog-articles"], "severity": "info", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Email\" -> https://tigerconnect.com/about/contact-us/#tab-contactsales", "selector": "a:nth-of-type(169)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(169)", "Email", "https", "tigerconnect.com", "about", "contact-us", "tab-contactsales"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Email\" -> https://tigerconnect.com/about/contact-us/#tab-contactsupport", "selector": "a:nth-of-type(170)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(170)", "Email", "https", "tigerconnect.com", "about", "contact-us", "tab-contactsupport"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Clinical Collaboration\" -> https://tigerconnect.com/products/clinical-collaboration/", "selector": "a:nth-of-type(172)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(172)", "Clinical", "Collaboration", "https", "tigerconnect.com", "products", "clinical-collaboration"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Patient Engagement\" -> https://tigerconnect.com/products/patient-engagement-software/", "selector": "a:nth-of-type(174)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(174)", "Patient", "Engagement", "https", "tigerconnect.com", "products", "patient-engagement-software"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Physician Sc<PERSON>uling\" -> https://tigerconnect.com/products/physician-scheduling/", "selector": "a:nth-of-type(175)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(175)", "Physician", "Scheduling", "https", "tigerconnect.com", "products", "physician-scheduling"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Nurses\" -> https://tigerconnect.com/healthcare-professionals/nurses/", "selector": "a:nth-of-type(182)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(182)", "Nurses", "https", "tigerconnect.com", "healthcare-professionals", "nurses"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Very short link text, Duplicate link text with different destinations", "value": "\"IT\" -> https://tigerconnect.com/healthcare-professionals/it/", "selector": "a:nth-of-type(184)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(184)", "IT", "https", "tigerconnect.com", "healthcare-professionals", "it"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Leadership\" -> https://tigerconnect.com/about/leadership/", "selector": "a:nth-of-type(188)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(188)", "Leadership", "https", "tigerconnect.com", "about", "leadership"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resource-center/", "selector": "a:nth-of-type(192)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(192)", "Resources", "https", "tigerconnect.com", "resource-center"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Case Studies\" -> https://tigerconnect.com/resource-center/case-studies/", "selector": "a:nth-of-type(195)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(195)", "Case", "Studies", "https", "tigerconnect.com", "resource-center", "case-studies"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Demo Tours\" -> https://tigerconnect.com/resource-center/demo-tours/", "selector": "a:nth-of-type(196)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(196)", "Demo", "Tours", "https", "tigerconnect.com", "resource-center", "demo-tours"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Webinars\" -> https://tigerconnect.com/resource-center/webinars/", "selector": "a:nth-of-type(198)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(198)", "Webinars", "https", "tigerconnect.com", "resource-center", "webinars"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: ", "value": "\"Legal\" -> https://tigerconnect.com/legal/", "selector": "a:nth-of-type(201)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(201)", "Legal", "https", "tigerconnect.com", "legal"], "severity": "info", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Save My Seat\" -> https://tigerconnect.com/resources/webinars/simplifying-system-integrations-leveraging-ehr-data-to-accelerate-speed-to-care-lp/", "selector": "a:nth-of-type(210)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(210)", "Save", "My", "<PERSON><PERSON>", "https", "tigerconnect.com", "resources", "webinars", "simplifying-system-integrations-leveraging-ehr-data-to-accelerate-speed-to-care-lp"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Generic link text, Duplicate link text with different destinations", "value": "\"Learn More\" -> https://tigerconnect.com/products/careconduit/", "selector": "a:nth-of-type(211)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(211)", "Learn", "More", "https", "tigerconnect.com", "products", "careconduit"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Save My Seat\" -> https://tigerconnect.com/resources/webinars/thriving-in-cerner-environments-with-tigerconnect-lp/", "selector": "a:nth-of-type(212)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(212)", "Save", "My", "<PERSON><PERSON>", "https", "tigerconnect.com", "resources", "webinars", "thriving-in-cerner-environments-with-tigerconnect-lp"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Save My Seat\" -> https://tigerconnect.com/resources/webinars/simplifying-system-integrations-leveraging-ehr-data-to-accelerate-speed-to-care-lp/", "selector": "a:nth-of-type(213)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(213)", "Save", "My", "<PERSON><PERSON>", "https", "tigerconnect.com", "resources", "webinars", "simplifying-system-integrations-leveraging-ehr-data-to-accelerate-speed-to-care-lp"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Generic link text, Duplicate link text with different destinations", "value": "\"Learn More\" -> https://tigerconnect.com/products/careconduit/", "selector": "a:nth-of-type(214)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(214)", "Learn", "More", "https", "tigerconnect.com", "products", "careconduit"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: ", "value": "\"Home\" -> https://tigerconnect.com/", "selector": "a:nth-of-type(215)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(215)", "Home", "https", "tigerconnect.com"], "severity": "info", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Physician Sc<PERSON>uling\" -> https://tigerconnect.com/products/physician-scheduling/", "selector": "a:nth-of-type(218)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(218)", "Physician", "Scheduling", "https", "tigerconnect.com", "products", "physician-scheduling"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Clinical Collaboration\" -> https://tigerconnect.com/products/clinical-collaboration-platform/", "selector": "a:nth-of-type(219)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(219)", "Clinical", "Collaboration", "https", "tigerconnect.com", "products", "clinical-collaboration-platform"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 64, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Alarm Management & Event Notification\" -> https://tigerconnect.com/products/alarm-management-event-notification-softwarev2/", "selector": "a:nth-of-type(220)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(220)", "Alarm", "Management", "Event", "Notification", "https", "tigerconnect.com", "products", "alarm-management-event-notification-softwarev2"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 65, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Patient Engagement\" -> https://tigerconnect.com/products/patient-engagement", "selector": "a:nth-of-type(221)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(221)", "Patient", "Engagement", "https", "tigerconnect.com", "products", "patient-engagement"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 66, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Nurses\" -> https://tigerconnect.com/?page_id=2124", "selector": "a:nth-of-type(239)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(239)", "Nurses", "https", "tigerconnect.com", "page_id"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 67, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Very short link text, Duplicate link text with different destinations", "value": "\"IT\" -> https://tigerconnect.com/?page_id=2240", "selector": "a:nth-of-type(241)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(241)", "IT", "https", "tigerconnect.com", "page_id"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 68, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Resources\" -> https://tigerconnect.com/resources/", "selector": "a:nth-of-type(242)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(242)", "Resources", "https", "tigerconnect.com", "resources"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 69, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: ", "value": "\"Blog\" -> https://tigerconnect.com/resources/blog-articles/", "selector": "a:nth-of-type(244)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(244)", "Blog", "https", "tigerconnect.com", "resources", "blog-articles"], "severity": "info", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 70, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Case Studies\" -> https://tigerconnect.com/resources/case-studies/", "selector": "a:nth-of-type(245)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(245)", "Case", "Studies", "https", "tigerconnect.com", "resources", "case-studies"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 71, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.119Z"}}}, {"type": "code", "description": "Link with unclear purpose: Duplicate link text with different destinations", "value": "\"Leadership\" -> https://tigerconnect.com/about/leadership-team/", "selector": "a:nth-of-type(255)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(255)", "Leadership", "https", "tigerconnect.com", "about", "leadership-team"], "severity": "error", "metadata": {"scanDuration": 573, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 72, "ruleId": "WCAG-049", "ruleName": "Link Context", "timestamp": "2025-07-11T10:49:04.120Z"}}}], "recommendations": ["Use descriptive link text that explains the link purpose", "Avoid generic phrases like \"click here\" or \"read more\"", "Ensure duplicate link text leads to the same destination", "Provide context through aria-label or surrounding content", "Make link purpose clear from the link text alone when possible"], "executionTime": 72, "originalScore": 0}, "timestamp": 1752230944120, "hash": "21347f893d9a33cd606cc27f1000c9f7", "accessCount": 1, "lastAccessed": 1752230944120, "size": 45654}
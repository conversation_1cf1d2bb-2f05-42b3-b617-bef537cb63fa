{"data": [{"type": "warning", "description": "Found 21 potential unmarked abbreviations", "value": "Unmarked count: 21, Examples: GTM, IT, ER, ED, EMS", "element": "text content", "elementCount": 1, "affectedSelectors": ["Unmarked", "count", "Examples", "GTM", "IT", "ER", "ED", "EMS"], "metadata": {"scanDuration": 66, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.6, "checkType": "pronunciation-meaning-analysis", "manualReviewRequired": false, "contextualMeaningAnalysis": true, "pronunciationAccuracyValidation": true, "evidenceIndex": 0, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T18:46:00.421Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "info", "description": "No marked abbreviations or acronyms found", "value": "Marked abbreviations found: false", "element": "page", "elementCount": 1, "affectedSelectors": ["Marked", "abbreviations", "found", "false"], "metadata": {"scanDuration": 66, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.6, "checkType": "pronunciation-meaning-analysis", "manualReviewRequired": false, "contextualMeaningAnalysis": true, "pronunciationAccuracyValidation": true, "evidenceIndex": 1, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T18:46:00.421Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "warning", "description": "Found 55 potential technical terms", "value": "Technical terms count: 55, Examples: ajax_url, siteurl, _prepareurl, plyr_css, ajaxurl, protocol, visibility, community, specificity, isolation", "element": "text content", "elementCount": 1, "affectedSelectors": ["Technical", "terms", "count", "Examples", "ajax_url", "<PERSON><PERSON>l", "<PERSON><PERSON><PERSON>", "plyr_css", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "protocol", "visibility", "community", "specificity", "isolation"], "metadata": {"scanDuration": 66, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.6, "checkType": "pronunciation-meaning-analysis", "manualReviewRequired": false, "contextualMeaningAnalysis": true, "pronunciationAccuracyValidation": true, "evidenceIndex": 2, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T18:46:00.421Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "info", "description": "Foreign language content marked with lang=\"en-US\"", "value": "Language: en-US, Text: if(navigator.userAgent.match(/MSIE|Internet Explorer/i)||navigator.userAgent.match(/Trident\\/7\\..*?r, Is marked: true", "element": "html", "elementCount": 1, "affectedSelectors": ["Language", "en-US", "Text", "if", "navigator.userAgent.match", "MSIE", "Internet", "Explorer", "i", "Trident", "r", "Is", "marked", "true"], "metadata": {"scanDuration": 66, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.6, "checkType": "pronunciation-meaning-analysis", "manualReviewRequired": false, "contextualMeaningAnalysis": true, "pronunciationAccuracyValidation": true, "evidenceIndex": 3, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T18:46:00.421Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "info", "description": "Pronunciation guides found", "value": "Pronunciation elements: 0, Phonetic notations: 84", "element": "pronunciation elements", "elementCount": 1, "affectedSelectors": ["Pronunciation", "elements", "Phonetic", "notations"], "metadata": {"scanDuration": 66, "elementsAnalyzed": 5, "checkSpecificData": {"automationRate": 0.6, "checkType": "pronunciation-meaning-analysis", "manualReviewRequired": false, "contextualMeaningAnalysis": true, "pronunciationAccuracyValidation": true, "evidenceIndex": 4, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T18:46:00.421Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259560421, "hash": "1cd9883a37bf911aa740c3b06491feb6", "accessCount": 1, "lastAccessed": 1752259560421, "size": 4267, "metadata": {"originalKey": "WCAG-065:WCAG-065:d2FybmluZzpGb3VuZCAyMSBwb3RlbnRp", "normalizedKey": "wcag-065_wcag-065_d2fybmluzzpgb3vuzcaymsbwb3rlbnrp", "savedAt": 1752259560422, "version": "1.1", "keyHash": "35911828a7203adeb3dc36ca3957a7e2"}}
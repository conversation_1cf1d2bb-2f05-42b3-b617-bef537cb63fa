{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "metadata": {"scanDuration": 19, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "abbreviation-analysis", "abbreviationDetection": true, "expansionValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T18:03:34.518Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752257014518, "hash": "73244f575d53adca6fac70368e801504", "accessCount": 1, "lastAccessed": 1752257014518, "size": 815, "metadata": {"originalKey": "WCAG-061:WCAG-061:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-061_wcag-061_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752257014519, "version": "1.1", "keyHash": "f3b47762bb782a5c614290e54bb6cf66"}}
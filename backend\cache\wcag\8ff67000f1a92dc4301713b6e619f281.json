{"data": [{"type": "info", "description": "No motion-triggered functionality detected", "value": "<PERSON> does not appear to use device motion for triggering functions", "severity": "info", "metadata": {"scanDuration": 14, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-actuation-analysis", "motionEventDetection": true, "alternativeControlValidation": true, "advancedMotionDetection": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-056", "ruleName": "Motion Actuation", "timestamp": "2025-07-11T20:08:20.008Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["Page", "does", "not", "appear", "to", "use", "device", "motion", "for", "triggering", "functions"]}], "timestamp": 1752264500008, "hash": "53d5013738c66a67446744d59f70da12", "accessCount": 1, "lastAccessed": 1752264500008, "size": 848, "metadata": {"originalKey": "WCAG-056:WCAG-056", "normalizedKey": "wcag-056_wcag-056", "savedAt": 1752264500008, "version": "1.1", "keyHash": "1acf038a280dc3d3664b2e2cccd8a05c"}}
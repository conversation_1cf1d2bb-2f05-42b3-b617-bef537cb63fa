{"data": [{"type": "code", "description": "Input modality detection: high risk", "value": "Touch: 2, <PERSON>: 0, Keyboard: 0, Pointer: 38", "severity": "error", "elementCount": 1, "affectedSelectors": ["Touch", "Mouse", "Keyboard", "Pointer"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Detected input restriction types", "value": "pointer-disabled, css-pointer-disabled, css-touch-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["pointer-disabled", "css-pointer-disabled", "css-touch-disabled"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 1, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Restriction analysis: high severity restrictions", "value": "Methods: pointer-events-none, user-select-none, javascript-prevention, Affected elements: 604, Bypass mechanisms: 0", "severity": "error", "elementCount": 1, "affectedSelectors": ["Methods", "pointer-events-none", "user-select-none", "javascript-prevention", "Affected", "elements", "Bypass", "mechanisms"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 2, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Multi-modal interaction testing: poor rating", "value": "Touch: false, Mouse: true, Keyboard: true, Pointer: true, Concurrent: false", "severity": "error", "elementCount": 1, "affectedSelectors": ["Touch", "false", "Mouse", "true", "Keyboard", "Pointer", "Concurrent"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 3, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 1 needs improvement", "value": "button:nth-of-type(1) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 4, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 2 needs improvement", "value": "a:nth-of-type(2) - supported methods: 0, accessible: false", "severity": "warning", "elementCount": 1, "affectedSelectors": ["a", "nth-of-type", "supported", "methods", "accessible", "false"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 5, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 3 needs improvement", "value": "input:nth-of-type(3) - supported methods: 1, accessible: false", "severity": "warning", "elementCount": 1, "affectedSelectors": ["input", "nth-of-type", "supported", "methods", "accessible", "false"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 6, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 4 needs improvement", "value": "button:nth-of-type(4) - supported methods: 1, accessible: Search Button", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "Search", "<PERSON><PERSON>"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 7, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 5 needs improvement", "value": "input:nth-of-type(5) - supported methods: 1, accessible: false", "severity": "warning", "elementCount": 1, "affectedSelectors": ["input", "nth-of-type", "supported", "methods", "accessible", "false"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 8, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 6 needs improvement", "value": "button:nth-of-type(6) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 9, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 7 needs improvement", "value": "button:nth-of-type(7) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 10, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 8 needs improvement", "value": "button:nth-of-type(8) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 11, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 9 needs improvement", "value": "button:nth-of-type(9) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 12, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 10 needs improvement", "value": "button:nth-of-type(10) - supported methods: 1, accessible: \n\t\t\t\t\t\tToggle Menu\n\t\t\t\t\n\t\t", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "Toggle", "<PERSON><PERSON>"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 13, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 11 needs improvement", "value": "button:nth-of-type(11) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 14, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 12 needs improvement", "value": "button:nth-of-type(12) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 15, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 13 needs improvement", "value": "button:nth-of-type(13) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 16, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 14 needs improvement", "value": "button:nth-of-type(14) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 17, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 15 needs improvement", "value": "button:nth-of-type(15) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 18, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 16 needs improvement", "value": "button:nth-of-type(16) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 19, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 17 needs improvement", "value": "button:nth-of-type(17) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 20, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 18 needs improvement", "value": "button:nth-of-type(18) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 21, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 19 needs improvement", "value": "a:nth-of-type(19) - supported methods: 0, accessible: false", "severity": "warning", "elementCount": 1, "affectedSelectors": ["a", "nth-of-type", "supported", "methods", "accessible", "false"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 22, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 20 needs improvement", "value": "button:nth-of-type(20) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 23, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Input element 21 needs improvement", "value": "button:nth-of-type(21) - supported methods: 1, accessible: true", "severity": "warning", "elementCount": 1, "affectedSelectors": ["button", "nth-of-type", "supported", "methods", "accessible", "true"], "metadata": {"scanDuration": 148, "elementsAnalyzed": 41, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 24, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T20:08:22.691Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752264502691, "hash": "8bcc61fc9bfa8feca4e92dfcf68a7cb4", "accessCount": 1, "lastAccessed": 1752264502691, "size": 20601, "metadata": {"originalKey": "WCAG-059:WCAG-059", "normalizedKey": "wcag-059_wcag-059", "savedAt": 1752264502692, "version": "1.1", "keyHash": "a138bba9cfbf4225aa892f65b9d7e5e6"}}
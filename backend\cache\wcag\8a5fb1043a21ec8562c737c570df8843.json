{"data": [{"type": "info", "description": "Advanced text resize analysis with layout adaptation and contrast preservation", "element": "text-elements", "value": "{\"resizeAnalysis\":{\"textScaling\":{\"scalingFactors\":[1.25,1.5,1.75,2],\"issues\":[\"At 125% scaling: 298 text overlaps detected\",\"At 150% scaling: 298 text overlaps detected\",\"At 175% scaling: 298 text overlaps detected\",\"At 200% scaling: 298 text overlaps detected\"],\"recommendations\":[\"Implement responsive typography with relative units\",\"Test layout at 200% text scaling\"],\"complianceScore\":40},\"contentReflow\":{\"viewportSizes\":[{\"width\":320,\"height\":568,\"hasHorizontalScroll\":false,\"contentOverflow\":true,\"score\":80},{\"width\":768,\"height\":1024,\"hasHorizontalScroll\":false,\"contentOverflow\":true,\"score\":80},{\"width\":1024,\"height\":768,\"hasHorizontalScroll\":false,\"contentOverflow\":true,\"score\":80}],\"overallReflowScore\":80,\"criticalIssues\":[]},\"adaptiveFeatures\":{\"hasResponsiveImages\":true,\"hasFlexibleLayouts\":true,\"hasAdaptiveTypography\":false,\"hasAccessibleBreakpoints\":true,\"score\":75}},\"contrastAnalysis\":{\"wideGamutElements\":0,\"colorSpaceDistribution\":{\"srgb\":3112,\"p3\":0,\"rec2020\":0,\"oklch\":0,\"lch\":0,\"unknown\":0},\"p3Coverage\":0,\"rec2020Coverage\":0}}", "severity": "error", "elementCount": 1, "affectedSelectors": ["resizeAnalysis", "textScaling", "scalingFactors", "issues", "At", "scaling", "text", "overlaps", "detected", "recommendations", "Implement", "responsive", "typography", "with", "relative", "units", "Test", "layout", "at", "complianceScore", "contentReflow", "viewportSizes", "width", "height", "hasHorizontalScroll", "false", "contentOverflow", "true", "score", "overallReflowScore", "criticalIssues", "adaptiveFeatures", "hasResponsiveImages", "hasFlexibleLayouts", "hasAdaptiveTypography", "hasAccessibleBreakpoints", "contrastAnalysis", "wideGamutElements", "colorSpaceDistribution", "srgb", "p3", "rec2020", "oklch", "lch", "unknown", "p3Coverage", "rec2020Coverage"], "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.987Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses overflow:hidden which may clip resized text, Absolute/fixed positioning may cause resize issues", "value": "Font: 18px, Text: \"Skip to content\"", "selector": "a:nth-of-type(34)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(34)", "Font", "px", "Text", "<PERSON><PERSON>", "to", "content"], "severity": "info", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.987Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 12px, Text: \"Contact Sales: (800) 572-0470\"", "selector": "a:nth-of-type(35)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(35)", "Font", "px", "Text", "Contact", "Sales"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 12px, Text: \"Contact Support\"", "selector": "a:nth-of-type(36)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(36)", "Font", "px", "Text", "Contact", "Support"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 12px, Text: \"LoginExpand\"", "selector": "span:nth-of-type(37)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(37)", "Font", "px", "Text", "LoginExpand"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10.8px, Text: \"Expand\"", "selector": "title:nth-of-type(38)", "elementCount": 1, "affectedSelectors": ["title:nth-of-type(38)", "Font", "px", "Text", "Expand"], "severity": "error", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 12px, Text: \"TigerConnect\"", "selector": "a:nth-of-type(39)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(39)", "Font", "px", "Text", "TigerConnect"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 6, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 12px, Text: \"Physician Scheduling\"", "selector": "a:nth-of-type(40)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(40)", "Font", "px", "Text", "Physician", "Scheduling"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 7, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 12px, Text: \"TigerConnect Community\"", "selector": "a:nth-of-type(41)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(41)", "Font", "px", "Text", "TigerConnect", "Community"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 8, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses overflow:hidden which may clip resized text, Absolute/fixed positioning may cause resize issues", "value": "Font: 18px, Text: \"Search for:\"", "selector": "span:nth-of-type(42)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(42)", "Font", "px", "Text", "Search", "for"], "severity": "info", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 9, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses overflow:hidden which may clip resized text, Absolute/fixed positioning may cause resize issues", "value": "Font: 18px, Text: \"<PERSON> Button\"", "selector": "span:nth-of-type(43)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(43)", "Font", "px", "Text", "Search", "<PERSON><PERSON>"], "severity": "info", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 10, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Expand\"", "selector": "title:nth-of-type(46)", "elementCount": 1, "affectedSelectors": ["title:nth-of-type(46)", "Font", "px", "Text", "Expand"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 11, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Learn More\"", "selector": "span:nth-of-type(57)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(57)", "Font", "px", "Text", "Learn", "More"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 12, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Expand\"", "selector": "title:nth-of-type(106)", "elementCount": 1, "affectedSelectors": ["title:nth-of-type(106)", "Font", "px", "Text", "Expand"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 13, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Learn More\"", "selector": "span:nth-of-type(117)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(117)", "Font", "px", "Text", "Learn", "More"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 14, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Expand\"", "selector": "title:nth-of-type(161)", "elementCount": 1, "affectedSelectors": ["title:nth-of-type(161)", "Font", "px", "Text", "Expand"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 15, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Learn More\"", "selector": "span:nth-of-type(172)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(172)", "Font", "px", "Text", "Learn", "More"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 16, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Why TigerConnect?Expand\"", "selector": "span:nth-of-type(228)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(228)", "Font", "px", "Text", "Why", "TigerConnect", "Expand"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 17, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Expand\"", "selector": "title:nth-of-type(229)", "elementCount": 1, "affectedSelectors": ["title:nth-of-type(229)", "Font", "px", "Text", "Expand"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 18, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Learn More\"", "selector": "span:nth-of-type(240)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(240)", "Font", "px", "Text", "Learn", "More"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 19, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Unified Healthcare Communication\"", "selector": "p:nth-of-type(276)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(276)", "Font", "px", "Text", "Unified", "Healthcare", "Communication"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 20, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 56px, Text: \"One Platform to Unify Communications\"", "selector": "h1:nth-of-type(277)", "elementCount": 1, "affectedSelectors": ["h1:nth-of-type(277)", "Font", "px", "Text", "One", "Platform", "to", "Unify", "Communications"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 21, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 20px, Text: \"Discover how leading hospitals and health systems rely on TigerConnect to enhance patient throughput\"", "selector": "p:nth-of-type(278)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(278)", "Font", "px", "Text", "Discover", "how", "leading", "hospitals", "and", "health", "systems", "rely", "on", "TigerConnect", "to", "enhance", "patient", "throughput"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 22, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Watch Video\"", "selector": "span:nth-of-type(279)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(279)", "Font", "px", "Text", "Watch", "Video"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 23, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Take a Tour\"", "selector": "span:nth-of-type(280)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(280)", "Font", "px", "Text", "Take", "a", "Tour"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 24, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Clinical Collaboration\"", "selector": "h2:nth-of-type(284)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(284)", "Font", "px", "Text", "Clinical", "Collaboration"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 25, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Alarm Management & Event Notification\"", "selector": "h2:nth-of-type(286)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(286)", "Font", "px", "Text", "Alarm", "Management", "Event", "Notification"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 26, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Clinical Collaboration\"", "selector": "h2:nth-of-type(290)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(290)", "Font", "px", "Text", "Clinical", "Collaboration"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 27, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Alarm Management & Event Notification\"", "selector": "h2:nth-of-type(292)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(292)", "Font", "px", "Text", "Alarm", "Management", "Event", "Notification"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 28, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.988Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Clinical Collaboration\"", "selector": "h2:nth-of-type(296)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(296)", "Font", "px", "Text", "Clinical", "Collaboration"], "severity": "warning", "metadata": {"scanDuration": 56375, "elementsAnalyzed": 186, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 29, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T18:02:58.989Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752256978991, "hash": "efcfedc691c4858aab1374a6f3111614", "accessCount": 1, "lastAccessed": 1752256978991, "size": 27777, "metadata": {"originalKey": "WCAG-037:WCAG-037:aW5mbzpBZHZhbmNlZCB0ZXh0IHJlc2l6", "normalizedKey": "wcag-037_wcag-037_aw5mbzpbzhzhbmnlzcb0zxh0ihjlc2l6", "savedAt": 1752256978992, "version": "1.1", "keyHash": "05d284d60ca8ee8a5fbaed95b6507c08"}}
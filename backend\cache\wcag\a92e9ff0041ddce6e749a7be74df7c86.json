{"data": [{"type": "warning", "description": "Found 29 potential unmarked abbreviations", "value": "Unmarked count: 29, Examples: PROVEN, SOC, ISO, GDPR, CCPA", "element": "text content", "elementCount": 1, "affectedSelectors": ["Unmarked", "count", "Examples", "PROVEN", "SOC", "ISO", "GDPR", "CCPA"], "metadata": {"scanDuration": 12, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.6, "checkType": "pronunciation-meaning-analysis", "manualReviewRequired": false, "contextualMeaningAnalysis": true, "pronunciationAccuracyValidation": true, "evidenceIndex": 0, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T11:38:11.259Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "info", "description": "No marked abbreviations or acronyms found", "value": "Marked abbreviations found: false", "element": "page", "elementCount": 1, "affectedSelectors": ["Marked", "abbreviations", "found", "false"], "metadata": {"scanDuration": 12, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.6, "checkType": "pronunciation-meaning-analysis", "manualReviewRequired": false, "contextualMeaningAnalysis": true, "pronunciationAccuracyValidation": true, "evidenceIndex": 1, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T11:38:11.259Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "warning", "description": "Found 26 potential technical terms", "value": "Technical terms count: 26, Examples: openapi, vulnerability, cybersecurity, requirementsvulnerability, security, remediation, business, customisation, protection, solution", "element": "text content", "elementCount": 1, "affectedSelectors": ["Technical", "terms", "count", "Examples", "openapi", "vulnerability", "cybersecurity", "requirementsvulnerability", "security", "remediation", "business", "customisation", "protection", "solution"], "metadata": {"scanDuration": 12, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.6, "checkType": "pronunciation-meaning-analysis", "manualReviewRequired": false, "contextualMeaningAnalysis": true, "pronunciationAccuracyValidation": true, "evidenceIndex": 2, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T11:38:11.260Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "info", "description": "Foreign language content marked with lang=\"en\"", "value": "Language: en, Text: A better vulnerability scanner\n              @font-face {\n                font-family:\"forma-djr-dis, Is marked: true", "element": "html", "elementCount": 1, "affectedSelectors": ["Language", "en", "Text", "A", "better", "vulnerability", "scanner", "font-face", "font-family", "forma-djr-dis", "Is", "marked", "true"], "metadata": {"scanDuration": 12, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.6, "checkType": "pronunciation-meaning-analysis", "manualReviewRequired": false, "contextualMeaningAnalysis": true, "pronunciationAccuracyValidation": true, "evidenceIndex": 3, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T11:38:11.260Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "info", "description": "Found 1 common foreign phrases", "value": "Foreign phrases count: 1, Phrases: etc.", "element": "text content", "elementCount": 1, "affectedSelectors": ["Foreign", "phrases", "count", "Phrases", "etc"], "metadata": {"scanDuration": 12, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.6, "checkType": "pronunciation-meaning-analysis", "manualReviewRequired": false, "contextualMeaningAnalysis": true, "pronunciationAccuracyValidation": true, "evidenceIndex": 4, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T11:38:11.260Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "info", "description": "Pronunciation guides found", "value": "Pronunciation elements: 0, Phonetic notations: 24", "element": "pronunciation elements", "elementCount": 1, "affectedSelectors": ["Pronunciation", "elements", "Phonetic", "notations"], "metadata": {"scanDuration": 12, "elementsAnalyzed": 6, "checkSpecificData": {"automationRate": 0.6, "checkType": "pronunciation-meaning-analysis", "manualReviewRequired": false, "contextualMeaningAnalysis": true, "pronunciationAccuracyValidation": true, "evidenceIndex": 5, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T11:38:11.260Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752233891260, "hash": "17efe35b0bf182d4078734f415e964d0", "accessCount": 1, "lastAccessed": 1752233891260, "size": 5091}
{"data": {"ruleId": "WCAG-037", "ruleName": "Resize Text", "category": "perceivable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0535, "automated": true, "evidence": [{"type": "info", "description": "Advanced text resize analysis with layout adaptation and contrast preservation", "element": "text-elements", "value": "{\"resizeAnalysis\":{\"textScaling\":{\"scalingFactors\":[1.25,1.5,1.75,2],\"issues\":[\"At 125% scaling: 298 text overlaps detected\",\"At 150% scaling: 298 text overlaps detected\",\"At 175% scaling: 298 text overlaps detected\",\"At 200% scaling: 298 text overlaps detected\"],\"recommendations\":[\"Implement responsive typography with relative units\",\"Test layout at 200% text scaling\"],\"complianceScore\":40},\"contentReflow\":{\"viewportSizes\":[{\"width\":320,\"height\":568,\"hasHorizontalScroll\":false,\"contentOverflow\":true,\"score\":80},{\"width\":768,\"height\":1024,\"hasHorizontalScroll\":false,\"contentOverflow\":true,\"score\":80},{\"width\":1024,\"height\":768,\"hasHorizontalScroll\":false,\"contentOverflow\":true,\"score\":80}],\"overallReflowScore\":80,\"criticalIssues\":[]},\"adaptiveFeatures\":{\"hasResponsiveImages\":true,\"hasFlexibleLayouts\":true,\"hasAdaptiveTypography\":false,\"hasAccessibleBreakpoints\":true,\"score\":75}},\"contrastAnalysis\":{\"wideGamutElements\":0,\"colorSpaceDistribution\":{\"srgb\":3111,\"p3\":0,\"rec2020\":0,\"oklch\":0,\"lch\":0,\"unknown\":0},\"p3Coverage\":0,\"rec2020Coverage\":0}}", "severity": "error", "elementCount": 1, "affectedSelectors": ["resizeAnalysis", "textScaling", "scalingFactors", "issues", "At", "scaling", "text", "overlaps", "detected", "recommendations", "Implement", "responsive", "typography", "with", "relative", "units", "Test", "layout", "at", "complianceScore", "contentReflow", "viewportSizes", "width", "height", "hasHorizontalScroll", "false", "contentOverflow", "true", "score", "overallReflowScore", "criticalIssues", "adaptiveFeatures", "hasResponsiveImages", "hasFlexibleLayouts", "hasAdaptiveTypography", "hasAccessibleBreakpoints", "contrastAnalysis", "wideGamutElements", "colorSpaceDistribution", "srgb", "p3", "rec2020", "oklch", "lch", "unknown", "p3Coverage", "rec2020Coverage"], "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.111Z"}}}, {"type": "code", "description": "Text resize issue: Uses overflow:hidden which may clip resized text, Absolute/fixed positioning may cause resize issues", "value": "Font: 18px, Text: \"Skip to content\"", "selector": "a:nth-of-type(34)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(34)", "Font", "px", "Text", "<PERSON><PERSON>", "to", "content"], "severity": "info", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.111Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 12px, Text: \"Contact Sales: (800) 572-0470\"", "selector": "a:nth-of-type(35)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(35)", "Font", "px", "Text", "Contact", "Sales"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.111Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 12px, Text: \"Contact Support\"", "selector": "a:nth-of-type(36)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(36)", "Font", "px", "Text", "Contact", "Support"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.111Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 12px, Text: \"LoginExpand\"", "selector": "span:nth-of-type(37)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(37)", "Font", "px", "Text", "LoginExpand"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.111Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10.8px, Text: \"Expand\"", "selector": "title:nth-of-type(38)", "elementCount": 1, "affectedSelectors": ["title:nth-of-type(38)", "Font", "px", "Text", "Expand"], "severity": "error", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.111Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 12px, Text: \"TigerConnect\"", "selector": "a:nth-of-type(39)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(39)", "Font", "px", "Text", "TigerConnect"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.111Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 12px, Text: \"Physician Scheduling\"", "selector": "a:nth-of-type(40)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(40)", "Font", "px", "Text", "Physician", "Scheduling"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.111Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 12px, Text: \"TigerConnect Community\"", "selector": "a:nth-of-type(41)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(41)", "Font", "px", "Text", "TigerConnect", "Community"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.111Z"}}}, {"type": "code", "description": "Text resize issue: Uses overflow:hidden which may clip resized text, Absolute/fixed positioning may cause resize issues", "value": "Font: 18px, Text: \"Search for:\"", "selector": "span:nth-of-type(42)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(42)", "Font", "px", "Text", "Search", "for"], "severity": "info", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.111Z"}}}, {"type": "code", "description": "Text resize issue: Uses overflow:hidden which may clip resized text, Absolute/fixed positioning may cause resize issues", "value": "Font: 18px, Text: \"<PERSON> Button\"", "selector": "span:nth-of-type(43)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(43)", "Font", "px", "Text", "Search", "<PERSON><PERSON>"], "severity": "info", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.111Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Expand\"", "selector": "title:nth-of-type(46)", "elementCount": 1, "affectedSelectors": ["title:nth-of-type(46)", "Font", "px", "Text", "Expand"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.111Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Learn More\"", "selector": "span:nth-of-type(57)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(57)", "Font", "px", "Text", "Learn", "More"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.111Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Expand\"", "selector": "title:nth-of-type(106)", "elementCount": 1, "affectedSelectors": ["title:nth-of-type(106)", "Font", "px", "Text", "Expand"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Learn More\"", "selector": "span:nth-of-type(117)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(117)", "Font", "px", "Text", "Learn", "More"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Expand\"", "selector": "title:nth-of-type(161)", "elementCount": 1, "affectedSelectors": ["title:nth-of-type(161)", "Font", "px", "Text", "Expand"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Learn More\"", "selector": "span:nth-of-type(172)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(172)", "Font", "px", "Text", "Learn", "More"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Why TigerConnect?Expand\"", "selector": "span:nth-of-type(228)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(228)", "Font", "px", "Text", "Why", "TigerConnect", "Expand"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Expand\"", "selector": "title:nth-of-type(229)", "elementCount": 1, "affectedSelectors": ["title:nth-of-type(229)", "Font", "px", "Text", "Expand"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Learn More\"", "selector": "span:nth-of-type(240)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(240)", "Font", "px", "Text", "Learn", "More"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Unified Healthcare Communication\"", "selector": "p:nth-of-type(276)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(276)", "Font", "px", "Text", "Unified", "Healthcare", "Communication"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 56px, Text: \"One Platform to Unify Communications\"", "selector": "h1:nth-of-type(277)", "elementCount": 1, "affectedSelectors": ["h1:nth-of-type(277)", "Font", "px", "Text", "One", "Platform", "to", "Unify", "Communications"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 20px, Text: \"Discover how leading hospitals and health systems rely on TigerConnect to enhance patient throughput\"", "selector": "p:nth-of-type(278)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(278)", "Font", "px", "Text", "Discover", "how", "leading", "hospitals", "and", "health", "systems", "rely", "on", "TigerConnect", "to", "enhance", "patient", "throughput"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Watch Video\"", "selector": "span:nth-of-type(279)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(279)", "Font", "px", "Text", "Watch", "Video"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Take a Tour\"", "selector": "span:nth-of-type(280)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(280)", "Font", "px", "Text", "Take", "a", "Tour"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Clinical Collaboration\"", "selector": "h2:nth-of-type(284)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(284)", "Font", "px", "Text", "Clinical", "Collaboration"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Alarm Management & Event Notification\"", "selector": "h2:nth-of-type(286)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(286)", "Font", "px", "Text", "Alarm", "Management", "Event", "Notification"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Clinical Collaboration\"", "selector": "h2:nth-of-type(290)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(290)", "Font", "px", "Text", "Clinical", "Collaboration"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Alarm Management & Event Notification\"", "selector": "h2:nth-of-type(292)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(292)", "Font", "px", "Text", "Alarm", "Management", "Event", "Notification"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Clinical Collaboration\"", "selector": "h2:nth-of-type(296)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(296)", "Font", "px", "Text", "Clinical", "Collaboration"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Alarm Management & Event Notification\"", "selector": "h2:nth-of-type(298)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(298)", "Font", "px", "Text", "Alarm", "Management", "Event", "Notification"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Clinical Collaboration\"", "selector": "h2:nth-of-type(302)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(302)", "Font", "px", "Text", "Clinical", "Collaboration"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 34px, Text: \"Streamline Workflows Across the Care Continuum\"", "selector": "h2:nth-of-type(303)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(303)", "Font", "px", "Text", "Streamline", "Workflows", "Across", "the", "Care", "Continuum"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"TigerConnect solutions make healthcare communication easy by putting information, data, and alerts i\"", "selector": "p:nth-of-type(304)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(304)", "Font", "px", "Text", "TigerConnect", "solutions", "make", "healthcare", "communication", "easy", "by", "putting", "information", "data", "and", "alerts", "i"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 20px, Text: \"Post Acute/Ambulatory\"", "selector": "h5:nth-of-type(309)", "elementCount": 1, "affectedSelectors": ["h5:nth-of-type(309)", "Font", "px", "Text", "Post", "Acute", "Ambulatory"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 38px, Text: \"The Future of Unified Healthcare Communications\"", "selector": "h2:nth-of-type(311)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(311)", "Font", "px", "Text", "The", "Future", "of", "Unified", "Healthcare", "Communications"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"TigerConnect’s vision for the future is that care teams have the information they need at their fing\"", "selector": "p:nth-of-type(312)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(312)", "Font", "px", "Text", "TigerConnect", "s", "vision", "for", "the", "future", "is", "that", "care", "teams", "have", "information", "they", "need", "at", "their", "fing"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 44px, Text: \"Improve Collaboration. <PERSON><PERSON><PERSON> Outcomes.\"", "selector": "h2:nth-of-type(314)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(314)", "Font", "px", "Text", "Improve", "Collaboration", "<PERSON><PERSON>ce", "Patient", "Outcomes"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"in ER capacity from faster transfers\"", "selector": "p:nth-of-type(316)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(316)", "Font", "px", "Text", "in", "ER", "capacity", "from", "faster", "transfers"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"See How\"", "selector": "span:nth-of-type(317)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(317)", "Font", "px", "Text", "See", "How"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"in patient readmissions\"", "selector": "p:nth-of-type(319)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(319)", "Font", "px", "Text", "in", "patient", "readmissions"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"See How\"", "selector": "span:nth-of-type(320)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(320)", "Font", "px", "Text", "See", "How"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"See How\"", "selector": "span:nth-of-type(323)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(323)", "Font", "px", "Text", "See", "How"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"in first case on-time starts\"", "selector": "p:nth-of-type(325)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(325)", "Font", "px", "Text", "in", "first", "case", "on-time", "starts"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"See How\"", "selector": "span:nth-of-type(326)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(326)", "Font", "px", "Text", "See", "How"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 28px, Text: \"Improve the cost, quality, and overall experience of care.\"", "selector": "h3:nth-of-type(327)", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(327)", "Font", "px", "Text", "Improve", "the", "cost", "quality", "and", "overall", "experience", "of", "care"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"in code blue response time\"", "selector": "p:nth-of-type(329)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(329)", "Font", "px", "Text", "in", "code", "blue", "response", "time"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"See How\"", "selector": "span:nth-of-type(330)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(330)", "Font", "px", "Text", "See", "How"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"in ED consult response times\"", "selector": "p:nth-of-type(332)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(332)", "Font", "px", "Text", "in", "ED", "consult", "response", "times"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"See How\"", "selector": "span:nth-of-type(333)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(333)", "Font", "px", "Text", "See", "How"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"See How\"", "selector": "span:nth-of-type(336)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(336)", "Font", "px", "Text", "See", "How"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"in sepsis bundle compliance\"", "selector": "p:nth-of-type(338)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(338)", "Font", "px", "Text", "in", "sepsis", "bundle", "compliance"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"See How\"", "selector": "span:nth-of-type(339)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(339)", "Font", "px", "Text", "See", "How"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 34px, Text: \"Solve Communication Challenges with a Single Platform\"", "selector": "h2:nth-of-type(340)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(340)", "Font", "px", "Text", "Solve", "Communication", "Challenges", "with", "a", "Single", "Platform"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"Connect EMS to hospital teams to accelerate time to treatment.\"", "selector": "span:nth-of-type(342)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(342)", "Font", "px", "Text", "Connect", "EMS", "to", "hospital", "teams", "accelerate", "time", "treatment"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"Simplify provider scheduling with an intuitive, rules-based solution.\"", "selector": "span:nth-of-type(344)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(344)", "Font", "px", "Text", "Simplify", "provider", "scheduling", "with", "an", "intuitive", "rules-based", "solution"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 28px, Text: \"Clinical Collaboration\"", "selector": "span:nth-of-type(345)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(345)", "Font", "px", "Text", "Clinical", "Collaboration"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"Coordinate care seamlessly across the care continuum.\"", "selector": "span:nth-of-type(346)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(346)", "Font", "px", "Text", "Coordinate", "care", "seamlessly", "across", "the", "continuum"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 28px, Text: \"Alarm Management & Event Notification\"", "selector": "span:nth-of-type(347)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(347)", "Font", "px", "Text", "Alarm", "Management", "Event", "Notification"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"Stay on top of patient needs with instant, intelligently routed clinical system alerts.\"", "selector": "span:nth-of-type(348)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(348)", "Font", "px", "Text", "Stay", "on", "top", "of", "patient", "needs", "with", "instant", "intelligently", "routed", "clinical", "system", "alerts"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"Effortlessly engage with patients and families before, during, and after care.\"", "selector": "span:nth-of-type(350)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(350)", "Font", "px", "Text", "Effortlessly", "engage", "with", "patients", "and", "families", "before", "during", "after", "care"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 44px, Text: \"Built With Your Care Teams in Mind\"", "selector": "h2:nth-of-type(351)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(351)", "Font", "px", "Text", "Built", "With", "Your", "Care", "Teams", "in", "Mind"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.112Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"Empower Collaboration\"", "selector": "h4:nth-of-type(353)", "elementCount": 1, "affectedSelectors": ["h4:nth-of-type(353)", "Font", "px", "Text", "Empower", "Collaboration"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Read More\"", "selector": "span:nth-of-type(354)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(354)", "Font", "px", "Text", "Read", "More"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"Consolidate Your Technology\"", "selector": "h4:nth-of-type(356)", "elementCount": 1, "affectedSelectors": ["h4:nth-of-type(356)", "Font", "px", "Text", "Consolidate", "Your", "Technology"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 64, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Read More\"", "selector": "span:nth-of-type(357)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(357)", "Font", "px", "Text", "Read", "More"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 65, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"Minimize Administrative Tasks\"", "selector": "h4:nth-of-type(359)", "elementCount": 1, "affectedSelectors": ["h4:nth-of-type(359)", "Font", "px", "Text", "Minimize", "Administrative", "Tasks"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 66, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Read More\"", "selector": "span:nth-of-type(360)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(360)", "Font", "px", "Text", "Read", "More"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 67, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14.4px, Text: \"Read More\"", "selector": "span:nth-of-type(363)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(363)", "Font", "px", "Text", "Read", "More"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 68, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 44px, Text: \"Trusted Partner in Unified Healthcare Communication\"", "selector": "h2:nth-of-type(364)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(364)", "Font", "px", "Text", "Trusted", "Partner", "in", "Unified", "Healthcare", "Communication"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 69, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 20px, Text: \"“TigerConnect Clinical Collaboration Platform in general is very easy to use. […] Going from what we\"", "selector": "h2:nth-of-type(367)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(367)", "Font", "px", "Text", "TigerConnect", "Clinical", "Collaboration", "Platform", "in", "general", "is", "very", "easy", "to", "use", "Going", "from", "what", "we"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 70, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"KLAS Research, October 2024\"", "selector": "h2:nth-of-type(369)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(369)", "Font", "px", "Text", "KLAS", "Research", "October"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 71, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 20px, Text: \"“TigerConnect provides metrics so that we can monitor our utilization, and that is highly valuable. \"", "selector": "h2:nth-of-type(370)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(370)", "Font", "px", "Text", "TigerConnect", "provides", "metrics", "so", "that", "we", "can", "monitor", "our", "utilization", "and", "is", "highly", "valuable"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 72, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"KLAS Research, September 2024\"", "selector": "h2:nth-of-type(372)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(372)", "Font", "px", "Text", "KLAS", "Research", "September"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 73, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 20px, Text: \"“One of the reasons TigerConnect was selected was that they have an ecosystem for middleware, alarm \"", "selector": "h2:nth-of-type(373)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(373)", "Font", "px", "Text", "One", "of", "the", "reasons", "TigerConnect", "was", "selected", "that", "they", "have", "an", "ecosystem", "for", "middleware", "alarm"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 74, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"KLAS Research, October 2024\"", "selector": "h2:nth-of-type(375)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(375)", "Font", "px", "Text", "KLAS", "Research", "October"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 75, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 20px, Text: \"“I am an extremely happy TigerConnect customer. I would give the ease of use a rating above the scal\"", "selector": "h2:nth-of-type(376)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(376)", "Font", "px", "Text", "I", "am", "an", "extremely", "happy", "TigerConnect", "customer", "would", "give", "the", "ease", "of", "use", "a", "rating", "above", "scal"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 76, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"KLAS Research, October 2024\"", "selector": "h2:nth-of-type(378)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(378)", "Font", "px", "Text", "KLAS", "Research", "October"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 77, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 28px, Text: \"Clinical Communication & Collaboration Solution\"", "selector": "h3:nth-of-type(380)", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(380)", "Font", "px", "Text", "Clinical", "Communication", "Collaboration", "Solution"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 78, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"for Clinical Communications in Ambulatory/Post-Acute Care​\"", "selector": "p:nth-of-type(382)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(382)", "Font", "px", "Text", "for", "Clinical", "Communications", "in", "Ambulatory", "Post-Acute", "Care"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 79, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 15px, Text: \"Read Verified Reviews >\"", "selector": "span:nth-of-type(383)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(383)", "Font", "px", "Text", "Read", "Verified", "Reviews"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 80, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"for HIPAA-Compliant Messaging, Clinical Communication & Collaboration, and Medical Staff Scheduling​\"", "selector": "p:nth-of-type(385)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(385)", "Font", "px", "Text", "for", "HIPAA-Compliant", "Messaging", "Clinical", "Communication", "Collaboration", "and", "Medical", "Staff", "Scheduling"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 81, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 15px, Text: \"Read Verified Reviews >\"", "selector": "span:nth-of-type(386)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(386)", "Font", "px", "Text", "Read", "Verified", "Reviews"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 82, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"in the 2024 Gartner® Magic Quadrant™ for Clinical Communication and Collaboration\"", "selector": "p:nth-of-type(388)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(388)", "Font", "px", "Text", "in", "the", "<PERSON><PERSON><PERSON>", "Magic", "Quadrant", "for", "Clinical", "Communication", "and", "Collaboration"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 83, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Read the Report >\"", "selector": "span:nth-of-type(389)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(389)", "Font", "px", "Text", "Read", "the", "Report"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 84, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"RESOURCES\"", "selector": "p:nth-of-type(391)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(391)", "Font", "px", "Text", "RESOURCES"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 85, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 32px, Text: \"Explore Industry Insights & Reports\"", "selector": "h2:nth-of-type(392)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(392)", "Font", "px", "Text", "Explore", "Industry", "Insights", "Reports"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 86, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"Explore All Resources\"", "selector": "span:nth-of-type(399)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(399)", "Font", "px", "Text", "Explore", "All", "Resources"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 87, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 26px, Text: \"State of Clinical Communications & Workflows\"", "selector": "h2:nth-of-type(400)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(400)", "Font", "px", "Text", "State", "of", "Clinical", "Communications", "Workflows"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 88, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Read the Full Report\"", "selector": "span:nth-of-type(401)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(401)", "Font", "px", "Text", "Read", "the", "Full", "Report"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 89, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 26px, Text: \"2024 Gartner Magic Quadrant\"", "selector": "h2:nth-of-type(402)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(402)", "Font", "px", "Text", "<PERSON><PERSON><PERSON>", "Magic", "Quadrant"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 90, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"Clinical Communication & Collaboration\"", "selector": "p:nth-of-type(403)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(403)", "Font", "px", "Text", "Clinical", "Communication", "Collaboration"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 91, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Read the Full Report\"", "selector": "span:nth-of-type(404)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(404)", "Font", "px", "Text", "Read", "the", "Full", "Report"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 92, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Sales:\"", "selector": "h2:nth-of-type(405)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(405)", "Font", "px", "Text", "Sales"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 93, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"(800) 572-0470\"", "selector": "h2:nth-of-type(406)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(406)", "Font", "px", "Text"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 94, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Email\"", "selector": "h2:nth-of-type(407)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(407)", "Font", "px", "Text", "Email"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 95, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Support:\"", "selector": "h2:nth-of-type(408)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(408)", "Font", "px", "Text", "Support"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 96, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Email\"", "selector": "h2:nth-of-type(409)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(409)", "Font", "px", "Text", "Email"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 97, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"Since 2010 TigerConnect has been transforming healthcare communications by enabling our customers to\"", "selector": "p:nth-of-type(410)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(410)", "Font", "px", "Text", "Since", "TigerConnect", "has", "been", "transforming", "healthcare", "communications", "by", "enabling", "our", "customers", "to"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 98, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"Clinical Collaboration\"", "selector": "span:nth-of-type(412)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(412)", "Font", "px", "Text", "Clinical", "Collaboration"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 99, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"Alarm Management and Event Notification\"", "selector": "span:nth-of-type(413)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(413)", "Font", "px", "Text", "Alarm", "Management", "and", "Event", "Notification"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 100, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Patient Engagement\"", "selector": "span:nth-of-type(414)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(414)", "Font", "px", "Text", "Patient", "Engagement"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 101, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Physician Scheduling\"", "selector": "span:nth-of-type(415)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(415)", "Font", "px", "Text", "Physician", "Scheduling"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 102, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"CareCond<PERSON>\"", "selector": "span:nth-of-type(416)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(416)", "Font", "px", "Text", "CareConduit"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 103, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Workflows\"", "selector": "span:nth-of-type(417)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(417)", "Font", "px", "Text", "Workflows"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 104, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"Healthcare Organizations\"", "selector": "span:nth-of-type(419)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(419)", "Font", "px", "Text", "Healthcare", "Organizations"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 105, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"Healthcare Professionals\"", "selector": "span:nth-of-type(420)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(420)", "Font", "px", "Text", "Healthcare", "Professionals"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 106, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Physicians\"", "selector": "span:nth-of-type(421)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(421)", "Font", "px", "Text", "Physicians"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 107, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Nurse<PERSON>\"", "selector": "span:nth-of-type(422)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(422)", "Font", "px", "Text", "Nurses"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 108, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Executives\"", "selector": "span:nth-of-type(423)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(423)", "Font", "px", "Text", "Executives"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 109, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Why TigerConnect?\"", "selector": "span:nth-of-type(426)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(426)", "Font", "px", "Text", "Why", "TigerConnect"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 110, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Careers\"", "selector": "span:nth-of-type(427)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(427)", "Font", "px", "Text", "Careers"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 111, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Leadership\"", "selector": "span:nth-of-type(428)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(428)", "Font", "px", "Text", "Leadership"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 112, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.113Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Media Coverage\"", "selector": "span:nth-of-type(429)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(429)", "Font", "px", "Text", "Media", "Coverage"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 113, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Newsroom\"", "selector": "span:nth-of-type(430)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(430)", "Font", "px", "Text", "Newsroom"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 114, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Partners\"", "selector": "span:nth-of-type(431)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(431)", "Font", "px", "Text", "Partners"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 115, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"App Download\"", "selector": "span:nth-of-type(433)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(433)", "Font", "px", "Text", "App", "Download"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 116, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Blog Articles\"", "selector": "span:nth-of-type(434)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(434)", "Font", "px", "Text", "Blog", "Articles"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 117, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Case Studies\"", "selector": "span:nth-of-type(435)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(435)", "Font", "px", "Text", "Case", "Studies"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 118, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Demo Tours\"", "selector": "span:nth-of-type(436)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(436)", "Font", "px", "Text", "Demo", "Tours"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 119, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Ebooks\"", "selector": "span:nth-of-type(437)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(437)", "Font", "px", "Text", "Ebooks"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 120, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 14px, Text: \"Webinars\"", "selector": "span:nth-of-type(438)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(438)", "Font", "px", "Text", "Webinars"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 121, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Fixed dimensions may prevent text resizing", "value": "Font: 14px, Text: \"©2025 TigerConnect. All Rights Reserved.\"", "selector": "p:nth-of-type(439)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(439)", "Font", "px", "Text", "TigerConnect", "All", "Rights", "Reserved"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 122, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 12px, Text: \"Sitemap\"", "selector": "span:nth-of-type(440)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(440)", "Font", "px", "Text", "Sitemap"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 123, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 12px, Text: \"Accessibility\"", "selector": "span:nth-of-type(441)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(441)", "Font", "px", "Text", "Accessibility"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 124, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 12px, Text: \"Legal\"", "selector": "span:nth-of-type(442)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(442)", "Font", "px", "Text", "Legal"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 125, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 12px, Text: \"Privacy\"", "selector": "span:nth-of-type(443)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(443)", "Font", "px", "Text", "Privacy"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 126, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"07/29/2025 | Webinar | Simplifying System Integrations: Leveraging EHR Data to Accelerate Speed to C\"", "selector": "h2:nth-of-type(445)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(445)", "Font", "px", "Text", "Webinar", "Simplifying", "System", "Integrations", "Leveraging", "EHR", "Data", "to", "Accelerate", "Speed", "C"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 127, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Save My Seat\"", "selector": "span:nth-of-type(447)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(447)", "Font", "px", "Text", "Save", "My", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 128, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Learn More\"", "selector": "span:nth-of-type(449)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(449)", "Font", "px", "Text", "Learn", "More"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 129, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"07/16/2025 | Webinar |  Thriving in a Cerner Environment with TigerConnect\"", "selector": "h2:nth-of-type(450)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(450)", "Font", "px", "Text", "Webinar", "Thriving", "in", "a", "<PERSON><PERSON>", "Environment", "with", "TigerConnect"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 130, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Save My Seat\"", "selector": "span:nth-of-type(452)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(452)", "Font", "px", "Text", "Save", "My", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 131, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"07/29/2025 | Webinar | Simplifying System Integrations: Leveraging EHR Data to Accelerate Speed to C\"", "selector": "h2:nth-of-type(453)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(453)", "Font", "px", "Text", "Webinar", "Simplifying", "System", "Integrations", "Leveraging", "EHR", "Data", "to", "Accelerate", "Speed", "C"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 132, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Save My Seat\"", "selector": "span:nth-of-type(455)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(455)", "Font", "px", "Text", "Save", "My", "<PERSON><PERSON>"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 133, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Learn More\"", "selector": "span:nth-of-type(457)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(457)", "Font", "px", "Text", "Learn", "More"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 134, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Home\"", "selector": "a:nth-of-type(461)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(461)", "Font", "px", "Text", "Home"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 135, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Solutions\"", "selector": "a:nth-of-type(462)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(462)", "Font", "px", "Text", "Solutions"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 136, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses overflow:hidden which may clip resized text, Absolute/fixed positioning may cause resize issues", "value": "Font: 18px, Text: \"Toggle child menu\"", "selector": "span:nth-of-type(463)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(463)", "Font", "px", "Text", "Toggle", "child", "menu"], "severity": "info", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 137, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.114Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Pre-Hospital\"", "selector": "a:nth-of-type(465)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(465)", "Font", "px", "Text", "Pre-Hospital"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 138, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Physician Scheduling\"", "selector": "a:nth-of-type(466)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(466)", "Font", "px", "Text", "Physician", "Scheduling"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 139, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Clinical Collaboration\"", "selector": "a:nth-of-type(467)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(467)", "Font", "px", "Text", "Clinical", "Collaboration"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 140, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Alarm Management & Event Notification\"", "selector": "a:nth-of-type(468)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(468)", "Font", "px", "Text", "Alarm", "Management", "Event", "Notification"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 141, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Patient Engagement\"", "selector": "a:nth-of-type(469)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(469)", "Font", "px", "Text", "Patient", "Engagement"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 142, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"CareCond<PERSON>\"", "selector": "a:nth-of-type(470)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(470)", "Font", "px", "Text", "CareConduit"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 143, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Workflows\"", "selector": "a:nth-of-type(471)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(471)", "Font", "px", "Text", "Workflows"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 144, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses overflow:hidden which may clip resized text, Absolute/fixed positioning may cause resize issues", "value": "Font: 18px, Text: \"Toggle child menu\"", "selector": "span:nth-of-type(472)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(472)", "Font", "px", "Text", "Toggle", "child", "menu"], "severity": "info", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 145, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Critical Response\"", "selector": "a:nth-of-type(474)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(474)", "Font", "px", "Text", "Critical", "Response"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 146, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Emergency Department Workflows\"", "selector": "a:nth-of-type(475)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(475)", "Font", "px", "Text", "Emergency", "Department", "Workflows"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 147, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Inpatient Workflows\"", "selector": "a:nth-of-type(476)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(476)", "Font", "px", "Text", "Inpatient", "Workflows"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 148, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Operating Room Workflows\"", "selector": "a:nth-of-type(477)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(477)", "Font", "px", "Text", "Operating", "Room", "Workflows"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 149, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Post Acute & Ambulatory Workflows\"", "selector": "a:nth-of-type(478)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(478)", "Font", "px", "Text", "Post", "Acute", "Ambulatory", "Workflows"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 150, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Organizations\"", "selector": "a:nth-of-type(479)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(479)", "Font", "px", "Text", "Organizations"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 151, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses overflow:hidden which may clip resized text, Absolute/fixed positioning may cause resize issues", "value": "Font: 18px, Text: \"Toggle child menu\"", "selector": "span:nth-of-type(480)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(480)", "Font", "px", "Text", "Toggle", "child", "menu"], "severity": "info", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 152, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Ambulatory Surgery Centers\"", "selector": "a:nth-of-type(482)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(482)", "Font", "px", "Text", "Ambulatory", "Surgery", "Centers"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 153, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Behavioral Health\"", "selector": "a:nth-of-type(483)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(483)", "Font", "px", "Text", "Behavioral", "Health"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 154, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Health Systems\"", "selector": "a:nth-of-type(484)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(484)", "Font", "px", "Text", "Health", "Systems"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 155, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Home Health & Hospice\"", "selector": "a:nth-of-type(485)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(485)", "Font", "px", "Text", "Home", "Health", "Hospice"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 156, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Hospitals\"", "selector": "a:nth-of-type(486)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(486)", "Font", "px", "Text", "Hospitals"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 157, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Physician Groups\"", "selector": "a:nth-of-type(487)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(487)", "Font", "px", "Text", "Physician", "Groups"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 158, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Skilled Nursing Facilities\"", "selector": "a:nth-of-type(488)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(488)", "Font", "px", "Text", "Skilled", "Nursing", "Facilities"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 159, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Professionals\"", "selector": "a:nth-of-type(489)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(489)", "Font", "px", "Text", "Professionals"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 160, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses overflow:hidden which may clip resized text, Absolute/fixed positioning may cause resize issues", "value": "Font: 18px, Text: \"Toggle child menu\"", "selector": "span:nth-of-type(490)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(490)", "Font", "px", "Text", "Toggle", "child", "menu"], "severity": "info", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 161, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Physicians\"", "selector": "a:nth-of-type(492)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(492)", "Font", "px", "Text", "Physicians"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 162, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Nurses\"", "selector": "a:nth-of-type(493)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(493)", "Font", "px", "Text", "Nurses"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 163, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Executives\"", "selector": "a:nth-of-type(494)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(494)", "Font", "px", "Text", "Executives"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 164, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Resources\"", "selector": "a:nth-of-type(496)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(496)", "Font", "px", "Text", "Resources"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 165, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses overflow:hidden which may clip resized text, Absolute/fixed positioning may cause resize issues", "value": "Font: 18px, Text: \"Toggle child menu\"", "selector": "span:nth-of-type(497)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(497)", "Font", "px", "Text", "Toggle", "child", "menu"], "severity": "info", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 166, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Articles\"", "selector": "a:nth-of-type(499)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(499)", "Font", "px", "Text", "Articles"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 167, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Blog\"", "selector": "a:nth-of-type(500)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(500)", "Font", "px", "Text", "Blog"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 168, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Case Studies\"", "selector": "a:nth-of-type(501)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(501)", "Font", "px", "Text", "Case", "Studies"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 169, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Checklists\"", "selector": "a:nth-of-type(502)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(502)", "Font", "px", "Text", "Checklists"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 170, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Datasheets\"", "selector": "a:nth-of-type(503)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(503)", "Font", "px", "Text", "Datasheets"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 171, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"eBooks\"", "selector": "a:nth-of-type(504)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(504)", "Font", "px", "Text", "eBooks"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 172, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Events\"", "selector": "a:nth-of-type(505)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(505)", "Font", "px", "Text", "Events"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 173, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Guides\"", "selector": "a:nth-of-type(506)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(506)", "Font", "px", "Text", "Guides"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 174, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Infographics\"", "selector": "a:nth-of-type(507)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(507)", "Font", "px", "Text", "Infographics"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 175, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Why TigerConnect\"", "selector": "a:nth-of-type(508)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(508)", "Font", "px", "Text", "Why", "TigerConnect"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 176, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses overflow:hidden which may clip resized text, Absolute/fixed positioning may cause resize issues", "value": "Font: 18px, Text: \"Toggle child menu\"", "selector": "span:nth-of-type(509)", "elementCount": 1, "affectedSelectors": ["span:nth-of-type(509)", "Font", "px", "Text", "Toggle", "child", "menu"], "severity": "info", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 177, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Careers\"", "selector": "a:nth-of-type(511)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(511)", "Font", "px", "Text", "Careers"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 178, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Contact Us\"", "selector": "a:nth-of-type(512)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(512)", "Font", "px", "Text", "Contact", "Us"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 179, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Leadership\"", "selector": "a:nth-of-type(513)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(513)", "Font", "px", "Text", "Leadership"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 180, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.115Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Media Coverage\"", "selector": "a:nth-of-type(514)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(514)", "Font", "px", "Text", "Media", "Coverage"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 181, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.116Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Partners\"", "selector": "a:nth-of-type(515)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(515)", "Font", "px", "Text", "Partners"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 182, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.116Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Contact Us\"", "selector": "a:nth-of-type(516)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(516)", "Font", "px", "Text", "Contact", "Us"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 183, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.116Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Download Our App\"", "selector": "a:nth-of-type(517)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(517)", "Font", "px", "Text", "Download", "Our", "App"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 184, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.116Z"}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size", "value": "Font: 15px, Text: \"Get a Demo\"", "selector": "a:nth-of-type(518)", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(518)", "Font", "px", "Text", "Get", "a", "Demo"], "severity": "warning", "metadata": {"scanDuration": 9509, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 185, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T20:07:52.116Z"}}}], "recommendations": ["Implement responsive typography with relative units", "Test layout at 200% text scaling", "Use relative units (em, rem, %) for font sizes", "Ensure text can be resized up to 200% without loss of functionality", "Avoid fixed pixel dimensions for text containers", "Test text resizing with browser zoom and font size controls", "Remove viewport restrictions that prevent user scaling"], "executionTime": 9133, "originalScore": 0, "thresholdApplied": 75, "scoringDetails": "0.0% (threshold: 75%) - FAILED"}, "timestamp": 1752264472116, "hash": "a473b5b8a58499aa6edd12f794356fc4", "accessCount": 1, "lastAccessed": 1752264472116, "size": 104941, "metadata": {"originalKey": "WCAG-037:053b13d2:add92319", "normalizedKey": "wcag-037_053b13d2_add92319", "savedAt": 1752264472119, "version": "1.1", "keyHash": "dafb02b064419a32274662bff2ebae34"}}
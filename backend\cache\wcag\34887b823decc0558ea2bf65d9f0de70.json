{"data": {"ruleId": "WCAG-035", "ruleName": "Multiple Ways", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AA", "status": "passed", "score": 100, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "info", "description": "Advanced navigation structure analysis for multiple ways validation", "element": "navigation-elements", "value": "{\"overallScore\":80,\"criticalIssues\":[],\"recommendations\":[\"Increase target sizes to meet WCAG minimum requirements\"],\"performanceMetrics\":{\"analysisTime\":1136,\"elementsAnalyzed\":1000,\"breakpointsTested\":5}}", "severity": "info", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "recommendations", "Increase", "target", "sizes", "to", "meet", "WCAG", "minimum", "requirements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "breakpointsTested"], "metadata": {"scanDuration": 1612, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-035", "ruleName": "Multiple Ways", "timestamp": "2025-07-11T10:49:20.525Z"}}}, {"type": "info", "description": "Process step page exempt from multiple ways requirement", "value": "Page appears to be part of a process or sequence, exempt from WCAG 2.4.5", "selector": "body", "elementCount": 1, "affectedSelectors": ["body", "Page", "appears", "to", "be", "part", "of", "a", "process", "or", "sequence", "exempt", "from", "WCAG"], "severity": "info", "metadata": {"scanDuration": 1612, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-035", "ruleName": "Multiple Ways", "timestamp": "2025-07-11T10:49:20.525Z"}}}], "recommendations": ["Continue ensuring process steps are clearly marked and accessible"], "executionTime": 1233, "originalScore": 100}, "timestamp": 1752230960525, "hash": "2dc49ab134de50e447af5f1179e14706", "accessCount": 1, "lastAccessed": 1752230960525, "size": 1784}
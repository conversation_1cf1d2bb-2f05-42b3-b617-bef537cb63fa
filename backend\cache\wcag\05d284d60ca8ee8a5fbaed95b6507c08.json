{"data": [{"type": "info", "description": "Advanced text resize analysis with layout adaptation and contrast preservation", "element": "text-elements", "value": "{\"resizeAnalysis\":{\"textScaling\":{\"scalingFactors\":[1.25,1.5,1.75,2],\"issues\":[\"At 125% scaling: 158 text overlaps detected\",\"At 150% scaling: 144 text overlaps detected\",\"At 175% scaling: 138 text overlaps detected\",\"At 200% scaling: 131 text overlaps detected\"],\"recommendations\":[\"Implement responsive typography with relative units\",\"Test layout at 200% text scaling\"],\"complianceScore\":40},\"contentReflow\":{\"viewportSizes\":[{\"width\":320,\"height\":568,\"hasHorizontalScroll\":false,\"contentOverflow\":true,\"score\":80},{\"width\":768,\"height\":1024,\"hasHorizontalScroll\":false,\"contentOverflow\":true,\"score\":80},{\"width\":1024,\"height\":768,\"hasHorizontalScroll\":false,\"contentOverflow\":true,\"score\":80}],\"overallReflowScore\":80,\"criticalIssues\":[]},\"adaptiveFeatures\":{\"hasResponsiveImages\":false,\"hasFlexibleLayouts\":true,\"hasAdaptiveTypography\":false,\"hasAccessibleBreakpoints\":true,\"score\":50}},\"contrastAnalysis\":{\"wideGamutElements\":0,\"colorSpaceDistribution\":{\"srgb\":1792,\"p3\":0,\"rec2020\":0,\"oklch\":0,\"lch\":0,\"unknown\":0},\"p3Coverage\":0,\"rec2020Coverage\":0}}", "severity": "error", "elementCount": 1, "affectedSelectors": ["resizeAnalysis", "textScaling", "scalingFactors", "issues", "At", "scaling", "text", "overlaps", "detected", "recommendations", "Implement", "responsive", "typography", "with", "relative", "units", "Test", "layout", "at", "complianceScore", "contentReflow", "viewportSizes", "width", "height", "hasHorizontalScroll", "false", "contentOverflow", "true", "score", "overallReflowScore", "criticalIssues", "adaptiveFeatures", "hasResponsiveImages", "hasFlexibleLayouts", "hasAdaptiveTypography", "hasAccessibleBreakpoints", "contrastAnalysis", "wideGamutElements", "colorSpaceDistribution", "srgb", "p3", "rec2020", "oklch", "lch", "unknown", "p3Coverage", "rec2020Coverage"], "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.9999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"Scan your websites, servers, networks, and APIs.\"", "selector": "p:nth-of-type(14)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(14)", "Font", "px", "Text", "<PERSON><PERSON>", "your", "websites", "servers", "networks", "and", "APIs"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"View dashboards, get threat alerts, and generate audit-ready reports.\"", "selector": "p:nth-of-type(15)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(15)", "Font", "px", "Text", "View", "dashboards", "get", "threat", "alerts", "and", "generate", "audit-ready", "reports"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Absolute/fixed positioning may cause resize issues", "value": "Font: 16px, Text: \"<PERSON>an now\"", "selector": "button:nth-of-type(17)", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(17)", "Font", "px", "Text", "<PERSON><PERSON>", "now"], "severity": "info", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"OUR CUSTOMERS\"", "selector": "p:nth-of-type(18)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(18)", "Font", "px", "Text", "OUR", "CUSTOMERS"], "severity": "error", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"What we do\"", "selector": "p:nth-of-type(21)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(21)", "Font", "px", "Text", "What", "we", "do"], "severity": "error", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"HostedScan is simple and effective. Run a wide set of industry-leading tools to uncover vulnerabilit\"", "selector": "p:nth-of-type(22)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(22)", "Font", "px", "Text", "HostedScan", "is", "simple", "and", "effective", "Run", "a", "wide", "set", "of", "industry-leading", "tools", "to", "uncover", "vulnerabilit"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 6, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"PROVEN TOOLS\"", "selector": "p:nth-of-type(23)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(23)", "Font", "px", "Text", "PROVEN", "TOOLS"], "severity": "error", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 7, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 48px, Text: \"Level up your company's cybersecurity\"", "selector": "h2:nth-of-type(24)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(24)", "Font", "px", "Text", "Level", "up", "your", "company", "s", "cybersecurity"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 8, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 28px, Text: \"Meet compliance requirements\"", "selector": "h3:nth-of-type(26)", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(26)", "Font", "px", "Text", "Meet", "compliance", "requirements"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 9, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Vulnerability scanning is essential for your compliance with SOC 2, ISO 27001, cyber insurance, and \"", "selector": "p:nth-of-type(27)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(27)", "Font", "px", "Text", "Vulnerability", "scanning", "is", "essential", "for", "your", "compliance", "with", "SOC", "ISO", "cyber", "insurance", "and"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 10, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"With regulations such as GDPR and CCPA, failure to maintain reasonable security procedures is ground\"", "selector": "p:nth-of-type(30)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(30)", "Font", "px", "Text", "With", "regulations", "such", "as", "GDPR", "and", "CCPA", "failure", "to", "maintain", "reasonable", "security", "procedures", "is", "ground"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 11, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 28px, Text: \"Mitigate security vulnerabilities\"", "selector": "h3:nth-of-type(32)", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(32)", "Font", "px", "Text", "Mitigate", "security", "vulnerabilities"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 12, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Identify CVEs and OWASP Top 10 issues in your systems. Prioritize remediation using industry-standar\"", "selector": "p:nth-of-type(33)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(33)", "Font", "px", "Text", "Identify", "CVEs", "and", "OWASP", "Top", "issues", "in", "your", "systems", "Prioritize", "remediation", "using", "industry-standar"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 13, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 28px, Text: \"Detect misconfigurations\"", "selector": "h3:nth-of-type(35)", "elementCount": 1, "affectedSelectors": ["h3:nth-of-type(35)", "Font", "px", "Text", "Detect", "misconfigurations"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 14, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Many breaches and hacks happen not through highly sophisticated attacks, but by exploiting simple mi\"", "selector": "p:nth-of-type(36)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(36)", "Font", "px", "Text", "Many", "breaches", "and", "hacks", "happen", "not", "through", "highly", "sophisticated", "attacks", "but", "by", "exploiting", "simple", "mi"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 15, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px, Fixed dimensions may prevent text resizing", "value": "Font: 10px, Text: \"CUSTOM REPORTING ENGINE\"", "selector": "p:nth-of-type(40)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(40)", "Font", "px", "Text", "CUSTOM", "REPORTING", "ENGINE"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 16, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 48px, Text: \"Comprehensive reports, that always look good\"", "selector": "h2:nth-of-type(41)", "elementCount": 1, "affectedSelectors": ["h2:nth-of-type(41)", "Font", "px", "Text", "Comprehensive", "reports", "that", "always", "look", "good"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 17, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 18px, Text: \"Generate polished, branded reports for your executives, clients, or auditors\"", "selector": "p:nth-of-type(42)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(42)", "Font", "px", "Text", "Generate", "polished", "branded", "reports", "for", "your", "executives", "clients", "or", "auditors"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 18, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"Communicate vulnerability risks\"", "selector": "p:nth-of-type(43)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(43)", "Font", "px", "Text", "Communicate", "vulnerability", "risks"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 19, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Get an executive PDF to share. See at a glance the vulnerabilities detected across all your targets \"", "selector": "p:nth-of-type(44)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(44)", "Font", "px", "Text", "Get", "an", "executive", "PDF", "to", "share", "See", "at", "a", "glance", "the", "vulnerabilities", "detected", "across", "all", "your", "targets"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 20, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"Export for your business intelligence\"", "selector": "p:nth-of-type(45)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(45)", "Font", "px", "Text", "Export", "for", "your", "business", "intelligence"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 21, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Use our built in reporting or export as CSV, JSON and XML to take into your BI tools, for full custo\"", "selector": "p:nth-of-type(46)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(46)", "Font", "px", "Text", "Use", "our", "built", "in", "reporting", "or", "export", "as", "CSV", "JSON", "and", "XML", "to", "take", "into", "your", "BI", "tools", "for", "full", "custo"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 22, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 24px, Text: \"White label reporting\"", "selector": "p:nth-of-type(47)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(47)", "Font", "px", "Text", "White", "label", "reporting"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 23, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"Prepare custom white label reports for your clients. Make your logo and brand exclusive on the repor\"", "selector": "p:nth-of-type(48)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(48)", "Font", "px", "Text", "Prepare", "custom", "white", "label", "reports", "for", "your", "clients", "Make", "logo", "and", "brand", "exclusive", "on", "the", "repor"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 24, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px, Fixed dimensions may prevent text resizing", "value": "Font: 10px, Text: \"Scheduling & notifications\"", "selector": "p:nth-of-type(49)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(49)", "Font", "px", "Text", "Scheduling", "notifications"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 25, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 32px, Text: \"Always-on protection, without the noise\"", "selector": "p:nth-of-type(50)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(50)", "Font", "px", "Text", "Always-on", "protection", "without", "the", "noise"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 26, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 16px, Text: \"When a new port is open, or a new risk is detected, automatically alert your team. Cut out the noise\"", "selector": "p:nth-of-type(51)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(51)", "Font", "px", "Text", "When", "a", "new", "port", "is", "open", "or", "risk", "detected", "automatically", "alert", "your", "team", "Cut", "out", "the", "noise"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 27, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Uses small fixed pixel font size, Font size below 12px", "value": "Font: 10px, Text: \"REST API & WEBHOOKS\"", "selector": "p:nth-of-type(53)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(53)", "Font", "px", "Text", "REST", "API", "WEBHOOKS"], "severity": "error", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 28, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Text resize issue: Fixed dimensions may prevent text resizing", "value": "Font: 32px, Text: \"Developer APIs + Webhooks\"", "selector": "p:nth-of-type(54)", "elementCount": 1, "affectedSelectors": ["p:nth-of-type(54)", "Font", "px", "Text", "Developer", "APIs", "Webhooks"], "severity": "warning", "metadata": {"scanDuration": 2797, "elementsAnalyzed": 230, "checkSpecificData": {"automationRate": 0.9, "checkType": "text-resize-analysis", "zoomLevelTesting": true, "textScalabilityValidation": true, "functionalityPreservation": true, "layoutAnalysis": true, "enhancedColorAnalysis": true, "evidenceIndex": 29, "ruleId": "WCAG-037", "ruleName": "Resize Text", "timestamp": "2025-07-11T11:38:27.323Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752233907323, "hash": "800a5d4889361d3273598f1dbc9f14e3", "accessCount": 1, "lastAccessed": 1752233907323, "size": 29816}
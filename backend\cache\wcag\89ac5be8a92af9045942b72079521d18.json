{"data": {"ruleId": "WCAG-007", "ruleName": "Focus Visible", "category": "operable", "wcagVersion": "2.1", "successCriterion": "2.4.7", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.09, "automated": true, "evidence": [{"type": "text", "description": "Technical error during check execution", "value": "Cannot read properties of undefined (reading 'getElementSelector')\npptr:evaluate;FormAccessibilityAnalyzer.getForms%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cutils%5Cform-accessibility-analyzer.ts%3A434%3A27):7:44", "severity": "error", "elementCount": 1, "affectedSelectors": ["Cannot", "read", "properties", "of", "undefined", "reading", "getElementSelector", "pptr", "evaluate", "FormAccessibilityAnalyzer.getForms", "D", "A", "CWeb", "projects", "CCom<PERSON>ly", "Checker", "Cbackend", "Csrc", "Ccompliance", "Cwcag", "Cutils", "Cform-accessibility-analyzer.ts", "A434", "A27"], "fixExample": {"before": "<!-- Inaccessible element -->", "after": "button:focus { outline: 2px solid #005fcc; outline-offset: 2px; }", "description": "Ensure focus indicators are visible", "codeExample": "button:focus { outline: 2px solid #005fcc; outline-offset: 2px; }", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/focus-visible.html", "https://developer.mozilla.org/en-US/docs/Web/CSS/:focus"]}, "metadata": {"scanDuration": 1299, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-007", "ruleName": "Focus Visible", "timestamp": "2025-07-11T11:37:29.205Z"}}}], "recommendations": ["Check failed due to technical error - manual review recommended", "Error details: Cannot read properties of undefined (reading 'getElementSelector')\npptr:evaluate;FormAccessibilityAnalyzer.getForms%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cutils%5Cform-accessibility-analyzer.ts%3A434%3A27):7:44", "Check browser console and server logs for more information"], "executionTime": 8, "errorMessage": "Cannot read properties of undefined (reading 'getElementSelector')\npptr:evaluate;FormAccessibilityAnalyzer.getForms%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cutils%5Cform-accessibility-analyzer.ts%3A434%3A27):7:44"}, "timestamp": 1752233849205, "hash": "d2763a6bcf949da6305336a541317783", "accessCount": 1, "lastAccessed": 1752233849205, "size": 2231}
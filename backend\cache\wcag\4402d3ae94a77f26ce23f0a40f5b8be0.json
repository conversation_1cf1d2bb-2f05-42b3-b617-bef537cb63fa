{"data": [{"type": "text", "description": "Enhanced focus visibility analysis summary", "value": "36/75 focused elements are completely visible (AAA standard)", "severity": "error", "elementCount": 1, "affectedSelectors": ["focused", "elements", "are", "completely", "visible", "AAA", "standard"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 0, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "info", "description": "Enhanced focus obstruction analysis with strict AAA-level validation", "element": "focus-obstructions-enhanced", "value": "{\"overallScore\":17,\"criticalIssues\":[\"Focus indicator obscured for element: a.w-full\",\"Focus indicator obscured for element: #demo-account-login-button\",\"Focus indicator obscured for element: a.mt-10\",\"Focus indicator obscured for element: a.mt-10\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.w-full\",\"Focus indicator obscured for element: #demo-account-login-button\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Failed to analyze focus obstruction for #headlessui-menu-button-:Rspmm:: DOMException: SyntaxError: Failed to execute 'querySelector' on 'Document': '#headlessui-menu-button-:Rspmm:' is not a valid selector.\",\"Failed to analyze focus obstruction for #headlessui-menu-button-:Ru9mm:: DOMException: SyntaxError: Failed to execute 'querySelector' on 'Document': '#headlessui-menu-button-:Ru9mm:' is not a valid selector.\",\"Focus indicator obscured for element: button.right-1\",\"Focus indicator obscured for element: button.rounded-full\",\"Focus indicator obscured for element: button.rounded-full\",\"Focus indicator obscured for element: input.grow\"],\"recommendations\":[\"Ensure focus indicator for a.w-full is not obscured by other elements\",\"Ensure focus indicator for #demo-account-login-button is not obscured by other elements\",\"Ensure focus indicator for a.mt-10 is not obscured by other elements\",\"Ensure focus indicator for a.mt-10 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.w-full is not obscured by other elements\",\"Ensure focus indicator for #demo-account-login-button is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for button.right-1 is not obscured by other elements\",\"Ensure focus indicator for button.rounded-full is not obscured by other elements\",\"Ensure focus indicator for button.rounded-full is not obscured by other elements\",\"Ensure focus indicator for input.grow is not obscured by other elements\"],\"performanceMetrics\":{\"analysisTime\":1280,\"elementsAnalyzed\":75,\"obstructionsFound\":60}}", "severity": "error", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "Focus", "indicator", "obscured", "for", "element", "a.w-full", "#demo-account-login-button", "a.mt-10", "a.mt-4", "a.relative", "a.text-sm", "Failed", "to", "analyze", "focus", "obstruction", "#headlessui-menu-button-", "Rspmm", "DOMException", "SyntaxError", "execute", "querySelector", "on", "Document", "is", "not", "a", "valid", "selector", "Ru9mm", "button.right-1", "button.rounded-full", "input.grow", "recommendations", "Ensure", "by", "other", "elements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "obstructionsFound"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 1, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 2, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 3, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 4, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 5, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 6, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 7, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 8, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 9, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 10, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 11, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.w-full", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.w-full", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 12, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "#demo-account-login-button", "severity": "info", "elementCount": 1, "affectedSelectors": ["#demo-account-login-button", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 13, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-10", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-10", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 14, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-10", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-10", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 15, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.246Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 16, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.247Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 17, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.247Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 18, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.247Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 19, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.247Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 20, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.247Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 21, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.247Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 22, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.247Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 23, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.247Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 2807, "elementsAnalyzed": 75, "checkSpecificData": {"automationRate": 1, "checkType": "enhanced-focus-obstruction-analysis", "stricterObstructionChecks": true, "comprehensiveFocusTracking": true, "evidenceIndex": 24, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.247Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752233861247, "hash": "c918dfbb9587e5bb97fb1b17b96ea466", "accessCount": 1, "lastAccessed": 1752233861247, "size": 28614}
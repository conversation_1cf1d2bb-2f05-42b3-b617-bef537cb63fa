{"data": {"ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0611, "automated": true, "evidence": [{"type": "code", "description": "Moving content without user controls: JavaScript-based animation detected", "value": "Moving content detected", "selector": "script", "severity": "error", "elementCount": 1, "affectedSelectors": ["script", "Moving", "content", "detected"], "metadata": {"scanDuration": 416, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-11T10:45:51.358Z"}}}], "recommendations": ["Provide pause, stop, or hide controls for moving content", "Allow users to control auto-updating content", "Ensure moving content can be paused on user request", "Consider reducing or eliminating unnecessary animations"], "executionTime": 56, "originalScore": 0}, "timestamp": 1752230751358, "hash": "a1be4b3e41b8fc9bfccfa1f6e9a60ffb", "accessCount": 1, "lastAccessed": 1752230751358, "size": 993}
{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "metadata": {"scanDuration": 25, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.65, "checkType": "input-mechanism-analysis", "inputRestrictionDetection": true, "multiModalSupport": true, "advancedInputTracking": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-059", "ruleName": "Concurrent Input Mechanisms", "timestamp": "2025-07-11T18:03:31.029Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752257011029, "hash": "94b9a6531dab2e4eeb05493077cd2558", "accessCount": 1, "lastAccessed": 1752257011029, "size": 835, "metadata": {"originalKey": "WCAG-059:WCAG-059:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-059_wcag-059_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752257011030, "version": "1.1", "keyHash": "037f0f7b0bd320fb4fc9d6cf5046129a"}}
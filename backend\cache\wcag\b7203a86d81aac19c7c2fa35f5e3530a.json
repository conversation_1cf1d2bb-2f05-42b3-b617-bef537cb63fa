{"data": {"ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "category": "understandable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.03, "automated": true, "evidence": [{"type": "warning", "description": "Found 29 potential unmarked abbreviations", "value": "Unmarked count: 29, Examples: PROVEN, SOC, ISO, GDPR, CCPA", "element": "text content", "elementCount": 1, "affectedSelectors": ["Unmarked", "count", "Examples", "PROVEN", "SOC", "ISO", "GDPR", "CCPA"], "metadata": {"scanDuration": 1292, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T11:38:11.255Z"}}}, {"type": "info", "description": "No marked abbreviations or acronyms found", "value": "Marked abbreviations found: false", "element": "page", "elementCount": 1, "affectedSelectors": ["Marked", "abbreviations", "found", "false"], "metadata": {"scanDuration": 1292, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T11:38:11.255Z"}}}, {"type": "warning", "description": "Found 26 potential technical terms", "value": "Technical terms count: 26, Examples: openapi, vulnerability, cybersecurity, requirementsvulnerability, security, remediation, business, customisation, protection, solution", "element": "text content", "elementCount": 1, "affectedSelectors": ["Technical", "terms", "count", "Examples", "openapi", "vulnerability", "cybersecurity", "requirementsvulnerability", "security", "remediation", "business", "customisation", "protection", "solution"], "metadata": {"scanDuration": 1292, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T11:38:11.255Z"}}}, {"type": "info", "description": "Foreign language content marked with lang=\"en\"", "value": "Language: en, Text: A better vulnerability scanner\n              @font-face {\n                font-family:\"forma-djr-dis, Is marked: true", "element": "html", "elementCount": 1, "affectedSelectors": ["Language", "en", "Text", "A", "better", "vulnerability", "scanner", "font-face", "font-family", "forma-djr-dis", "Is", "marked", "true"], "metadata": {"scanDuration": 1292, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T11:38:11.255Z"}}}, {"type": "info", "description": "Found 1 common foreign phrases", "value": "Foreign phrases count: 1, Phrases: etc.", "element": "text content", "elementCount": 1, "affectedSelectors": ["Foreign", "phrases", "count", "Phrases", "etc"], "metadata": {"scanDuration": 1292, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T11:38:11.255Z"}}}, {"type": "info", "description": "Pronunciation guides found", "value": "Pronunciation elements: 0, Phonetic notations: 24", "element": "pronunciation elements", "elementCount": 1, "affectedSelectors": ["Pronunciation", "elements", "Phonetic", "notations"], "metadata": {"scanDuration": 1292, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-065", "ruleName": "Pronunciation & Meaning", "timestamp": "2025-07-11T11:38:11.255Z"}}}], "recommendations": [], "executionTime": 12, "originalScore": 75}, "timestamp": 1752233891255, "hash": "5d1c8db2ecca8762d752675593264ea4", "accessCount": 1, "lastAccessed": 1752233891255, "size": 3627}
{"data": {"ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "category": "operable", "wcagVersion": "2.2", "successCriterion": "2.4.12", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.04, "automated": true, "evidence": [{"type": "text", "description": "Enhanced focus visibility analysis summary", "value": "36/75 focused elements are completely visible (AAA standard)", "severity": "error", "elementCount": 1, "affectedSelectors": ["focused", "elements", "are", "completely", "visible", "AAA", "standard"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.228Z"}}}, {"type": "info", "description": "Enhanced focus obstruction analysis with strict AAA-level validation", "element": "focus-obstructions-enhanced", "value": "{\"overallScore\":17,\"criticalIssues\":[\"Focus indicator obscured for element: a.w-full\",\"Focus indicator obscured for element: #demo-account-login-button\",\"Focus indicator obscured for element: a.mt-10\",\"Focus indicator obscured for element: a.mt-10\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.mt-4\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.relative\",\"Focus indicator obscured for element: a.w-full\",\"Focus indicator obscured for element: #demo-account-login-button\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Focus indicator obscured for element: a.text-sm\",\"Failed to analyze focus obstruction for #headlessui-menu-button-:Rspmm:: DOMException: SyntaxError: Failed to execute 'querySelector' on 'Document': '#headlessui-menu-button-:Rspmm:' is not a valid selector.\",\"Failed to analyze focus obstruction for #headlessui-menu-button-:Ru9mm:: DOMException: SyntaxError: Failed to execute 'querySelector' on 'Document': '#headlessui-menu-button-:Ru9mm:' is not a valid selector.\",\"Focus indicator obscured for element: button.right-1\",\"Focus indicator obscured for element: button.rounded-full\",\"Focus indicator obscured for element: button.rounded-full\",\"Focus indicator obscured for element: input.grow\"],\"recommendations\":[\"Ensure focus indicator for a.w-full is not obscured by other elements\",\"Ensure focus indicator for #demo-account-login-button is not obscured by other elements\",\"Ensure focus indicator for a.mt-10 is not obscured by other elements\",\"Ensure focus indicator for a.mt-10 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.mt-4 is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.relative is not obscured by other elements\",\"Ensure focus indicator for a.w-full is not obscured by other elements\",\"Ensure focus indicator for #demo-account-login-button is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for a.text-sm is not obscured by other elements\",\"Ensure focus indicator for button.right-1 is not obscured by other elements\",\"Ensure focus indicator for button.rounded-full is not obscured by other elements\",\"Ensure focus indicator for button.rounded-full is not obscured by other elements\",\"Ensure focus indicator for input.grow is not obscured by other elements\"],\"performanceMetrics\":{\"analysisTime\":1280,\"elementsAnalyzed\":75,\"obstructionsFound\":60}}", "severity": "error", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "Focus", "indicator", "obscured", "for", "element", "a.w-full", "demo-account-login-button", "a.mt-10", "a.mt-4", "a.relative", "a.text-sm", "Failed", "to", "analyze", "focus", "obstruction", "headlessui-menu-button-", "Rspmm", "DOMException", "SyntaxError", "execute", "querySelector", "on", "Document", "is", "not", "a", "valid", "selector", "Ru9mm", "button.right-1", "button.rounded-full", "input.grow", "recommendations", "Ensure", "by", "other", "elements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "obstructionsFound"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.w-full", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.w-full", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "#demo-account-login-button", "severity": "info", "elementCount": 1, "affectedSelectors": ["#demo-account-login-button", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-10", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-10", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-10", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-10", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.mt-4", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.relative", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.relative", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.relative", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.relative", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.relative", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.w-full", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.w-full", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "#demo-account-login-button", "severity": "info", "elementCount": 1, "affectedSelectors": ["#demo-account-login-button", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.229Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "a.rounded-md", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 64, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.230Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 65, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.232Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 66, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.232Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 67, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.232Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 68, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.232Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 69, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.232Z"}}}, {"type": "measurement", "description": "Focused element has partial obstruction (fails enhanced requirement)", "value": "Point 1 obscured by header; Point 5 obscured by header", "selector": "a.text-sm", "severity": "error", "elementCount": 1, "affectedSelectors": ["a.text-sm", "Point", "obscured", "by", "header"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 70, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.232Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "button.right-1", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.right-1", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 71, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.232Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "button.rounded-full", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 72, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.232Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "button.rounded-full", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 73, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.232Z"}}}, {"type": "measurement", "description": "Focused element is completely visible (enhanced requirement)", "value": "Element is 100% visible when focused", "selector": "input.grow", "severity": "info", "elementCount": 1, "affectedSelectors": ["input.grow", "Element", "is", "visible", "when", "focused"], "metadata": {"scanDuration": 4174, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 74, "ruleId": "WCAG-011", "ruleName": "Focus Not Obscured (Enhanced)", "timestamp": "2025-07-11T11:37:41.232Z"}}}], "recommendations": ["Ensure no part of focused elements is ever hidden (AAA requirement)", "Ensure focus indicator for a.w-full is not obscured by other elements", "Ensure focus indicator for #demo-account-login-button is not obscured by other elements", "Ensure focus indicator for a.mt-10 is not obscured by other elements", "Ensure focus indicator for a.mt-10 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.mt-4 is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.relative is not obscured by other elements", "Ensure focus indicator for a.w-full is not obscured by other elements", "Ensure focus indicator for #demo-account-login-button is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for a.text-sm is not obscured by other elements", "Ensure focus indicator for button.right-1 is not obscured by other elements", "Ensure focus indicator for button.rounded-full is not obscured by other elements", "Ensure focus indicator for button.rounded-full is not obscured by other elements", "Ensure focus indicator for input.grow is not obscured by other elements", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Ensure a.text-sm is completely visible when focused (AAA requirement)", "Consider redesigning layout to avoid any focus obstruction"], "executionTime": 2807, "originalScore": 48}, "timestamp": 1752233861232, "hash": "7fcd098b86e5b714438fd800910da6fa", "accessCount": 1, "lastAccessed": 1752233861232, "size": 56863}
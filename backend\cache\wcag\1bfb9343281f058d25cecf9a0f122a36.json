{"data": {"ruleId": "WCAG-018", "ruleName": "Text and Wording", "category": "understandable", "wcagVersion": "2.2", "successCriterion": "0.0.0", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.08, "automated": false, "evidence": [{"type": "error", "description": "Error analyzing content text", "value": "Error: natural.SentenceTokenizer.tokenize is not a function", "element": "main content", "elementCount": 1, "affectedSelectors": ["Error", "natural.SentenceTokenizer.tokenize", "is", "not", "a", "function"], "metadata": {"scanDuration": 1201, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-018", "ruleName": "Text and Wording", "timestamp": "2025-07-11T10:45:43.532Z"}}}, {"type": "error", "description": "Error analyzing form text", "value": "Error: natural.SentenceTokenizer.tokenize is not a function", "element": "form", "elementCount": 1, "affectedSelectors": ["Error", "natural.SentenceTokenizer.tokenize", "is", "not", "a", "function"], "metadata": {"scanDuration": 1201, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-018", "ruleName": "Text and Wording", "timestamp": "2025-07-11T10:45:43.532Z"}}}, {"type": "info", "description": "No error messages found to analyze", "value": "Error messages found: false", "element": "page", "elementCount": 1, "affectedSelectors": ["Error", "messages", "found", "false"], "metadata": {"scanDuration": 1201, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-018", "ruleName": "Text and Wording", "timestamp": "2025-07-11T10:45:43.532Z"}}}, {"type": "warning", "description": "Found potentially vague UI text", "value": "Vague texts count: 8, Examples: <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>", "element": "buttons, links, navigation", "elementCount": 1, "affectedSelectors": ["Vague", "texts", "count", "Examples", "Search", "<PERSON><PERSON>", "Learn", "More", "Read"], "metadata": {"scanDuration": 1201, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-018", "ruleName": "Text and Wording", "timestamp": "2025-07-11T10:45:43.532Z"}}}], "recommendations": ["Check content readability manually", "Check form text manually", "Use more descriptive text for buttons and links"], "executionTime": 0, "errorMessage": "Failed to analyze content text; Failed to analyze form text; Found 8 potentially vague UI text elements", "manualReviewItems": []}, "timestamp": 1752230743532, "hash": "d35817db53ca6e1ba6370ca571c7f51a", "accessCount": 1, "lastAccessed": 1752230743532, "size": 2512}
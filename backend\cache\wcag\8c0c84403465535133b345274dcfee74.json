{"data": {"ruleId": "WCAG-013", "ruleName": "Dragging Movements", "category": "operable", "wcagVersion": "2.2", "successCriterion": "0.0.0", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.06, "automated": false, "evidence": [{"type": "text", "description": "No drag and drop elements found", "value": "Drag and drop elements found: false", "element": "page", "elementCount": 1, "affectedSelectors": ["Drag", "and", "drop", "elements", "found", "false"], "metadata": {"scanDuration": 416, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-013", "ruleName": "Dragging Movements", "timestamp": "2025-07-11T10:35:02.920Z"}}}, {"type": "text", "description": "No sliders found", "value": "page - slidersFound: false", "severity": "info", "elementCount": 1, "affectedSelectors": ["page", "slidersFound", "false"], "metadata": {"scanDuration": 416, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-013", "ruleName": "Dragging Movements", "timestamp": "2025-07-11T10:35:02.920Z"}}}, {"type": "info", "description": "No sortable lists found", "value": "Sortable lists found: false", "element": "page", "elementCount": 1, "affectedSelectors": ["Sortable", "lists", "found", "false"], "metadata": {"scanDuration": 416, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-013", "ruleName": "Dragging Movements", "timestamp": "2025-07-11T10:35:02.920Z"}}}, {"type": "info", "description": "No resizable elements found", "value": "Resizable elements found: false", "element": "page", "elementCount": 1, "affectedSelectors": ["Resizable", "elements", "found", "false"], "metadata": {"scanDuration": 416, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-013", "ruleName": "Dragging Movements", "timestamp": "2025-07-11T10:35:02.920Z"}}}, {"type": "error", "description": "Error analyzing gesture interactions", "value": "Error: element.className.includes is not a function\npptr:evaluate;DraggingMovementsCheck.analyzeGestureInteractions%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cchecks%5Cdragging-movements.ts%3A494%3A48):8:56", "element": "gesture elements", "elementCount": 1, "affectedSelectors": ["Error", "element.className.includes", "is", "not", "a", "function", "pptr", "evaluate", "DraggingMovementsCheck.analyzeGestureInteractions", "D", "A", "CWeb", "projects", "CCom<PERSON>ly", "Checker", "Cbackend", "Csrc", "Ccompliance", "Cwcag", "Cchecks", "Cdragging-movements.ts", "A494", "A48"], "metadata": {"scanDuration": 416, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-013", "ruleName": "Dragging Movements", "timestamp": "2025-07-11T10:35:02.920Z"}}}], "recommendations": ["Check gesture functionality manually"], "executionTime": 0, "errorMessage": "Failed to analyze gesture interactions", "manualReviewItems": []}, "timestamp": 1752230102920, "hash": "138fd8493129df4e99d7032525ad10ed", "accessCount": 1, "lastAccessed": 1752230102920, "size": 2999}
{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame 'A362B1B11B79F8E7E1503ABC8B8AFA25'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A362B1B11B79F8E7E1503ABC8B8AFA25"], "metadata": {"scanDuration": 1, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 1, "checkType": "page-title-analysis", "titleAnalysis": true, "metadataAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-013", "ruleName": "Page Titled", "timestamp": "2025-07-11T18:49:54.925Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752259794925, "hash": "88036754ebd4635e4afb2a34b5c0d0fa", "accessCount": 1, "lastAccessed": 1752259794925, "size": 740, "metadata": {"originalKey": "WCAG-013:WCAG-013:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-013_wcag-013_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752259794926, "version": "1.1", "keyHash": "beb1a58bff1b2807745153267e67962b"}}
{"data": {"ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "category": "robust", "wcagVersion": "2.2", "successCriterion": "0.0.0", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.11, "automated": false, "evidence": [{"type": "text", "description": "Name, role, value analysis summary", "value": "672/672 elements pass automated checks, 0 require manual review", "severity": "info", "elementCount": 672, "affectedSelectors": ["elements", "pass", "automated", "checks", "require", "manual", "review"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan\"", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Name", "HostedScan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.flex", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Use Cases\"", "selector": "#headlessui-menu-button-:Rspmm:", "severity": "info", "elementCount": 1, "affectedSelectors": ["#headlessui-menu-button-:Rspmm:", "Name", "Use", "Cases"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "#headlessui-menu-button-:Rspmm:", "severity": "info", "elementCount": 1, "affectedSelectors": ["#headlessui-menu-button-:Rspmm:", "Role", "button"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "#headlessui-menu-button-:Rspmm:", "severity": "info", "elementCount": 1, "affectedSelectors": ["#headlessui-menu-button-:Rspmm:", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Scanne<PERSON>\"", "selector": "a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "Name", "Scanners"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Pricing\"", "selector": "a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "Name", "Pricing"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Resources\"", "selector": "#headlessui-menu-button-:Ru9mm:", "severity": "info", "elementCount": 1, "affectedSelectors": ["#headlessui-menu-button-:Ru9mm:", "Name", "Resources"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "#headlessui-menu-button-:Ru9mm:", "severity": "info", "elementCount": 1, "affectedSelectors": ["#headlessui-menu-button-:Ru9mm:", "Role", "button"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "#headlessui-menu-button-:Ru9mm:", "severity": "info", "elementCount": 1, "affectedSelectors": ["#headlessui-menu-button-:Ru9mm:", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Blog\"", "selector": "a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "Name", "Blog"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.px-4.py-2.font-formadjrtext.tracking-[0.45px].hover:bg-black/30", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Log in\"", "selector": "a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.order-[0.5px].hidden.flex-none.rounded-md.border-white/50.bg-white.!text-dark-blue.sm:block.text-base.font-bold", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.order-[0.5px].hidden.flex-none.rounded-md.border-white/50.bg-white.!text-dark-blue.sm:block.text-base.font-bold", "Name", "Log", "in"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.order-[0.5px].hidden.flex-none.rounded-md.border-white/50.bg-white.!text-dark-blue.sm:block.text-base.font-bold", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.order-[0.5px].hidden.flex-none.rounded-md.border-white/50.bg-white.!text-dark-blue.sm:block.text-base.font-bold", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.order-[0.5px].hidden.flex-none.rounded-md.border-white/50.bg-white.!text-dark-blue.sm:block.text-base.font-bold", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.order-[0.5px].hidden.flex-none.rounded-md.border-white/50.bg-white.!text-dark-blue.sm:block.text-base.font-bold", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Sign up\"", "selector": "a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.hidden.flex-none.whitespace-nowrap.rounded-md.border-[0.5px].border-dark-blue/75.bg-dark-blue.font-formadjrtext.tracking-[0.45px].text-white.shadow-[0_4px_16px_0_rgba(0,0,0,0.25)].sm:block.text-base.font-bold", "severity": "info", "elementCount": 4, "affectedSelectors": ["a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.hidden.flex-none.whitespace-nowrap.rounded-md.border-[0.5px].border-dark-blue/75.bg-dark-blue.font-formadjrtext.tracking-[0.45px].text-white.shadow-[0_4px_16px_0_rgba(0", "0", "0.25)].sm:block.text-base.font-bold", "Name", "Sign", "up"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.hidden.flex-none.whitespace-nowrap.rounded-md.border-[0.5px].border-dark-blue/75.bg-dark-blue.font-formadjrtext.tracking-[0.45px].text-white.shadow-[0_4px_16px_0_rgba(0,0,0,0.25)].sm:block.text-base.font-bold", "severity": "info", "elementCount": 4, "affectedSelectors": ["a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.hidden.flex-none.whitespace-nowrap.rounded-md.border-[0.5px].border-dark-blue/75.bg-dark-blue.font-formadjrtext.tracking-[0.45px].text-white.shadow-[0_4px_16px_0_rgba(0", "0", "0.25)].sm:block.text-base.font-bold", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.hidden.flex-none.whitespace-nowrap.rounded-md.border-[0.5px].border-dark-blue/75.bg-dark-blue.font-formadjrtext.tracking-[0.45px].text-white.shadow-[0_4px_16px_0_rgba(0,0,0,0.25)].sm:block.text-base.font-bold", "severity": "info", "elementCount": 4, "affectedSelectors": ["a.rounded-md.border-[0.5px].px-5.py-2.5.leading-[19px].tracking-[0.4px].text-white.hidden.flex-none.whitespace-nowrap.rounded-md.border-[0.5px].border-dark-blue/75.bg-dark-blue.font-formadjrtext.tracking-[0.45px].text-white.shadow-[0_4px_16px_0_rgba(0", "0", "0.25)].sm:block.text-base.font-bold", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Site Menu\"", "selector": "#headlessui-menu-button-:Rfdmm:", "severity": "info", "elementCount": 1, "affectedSelectors": ["#headlessui-menu-button-:Rfdmm:", "Name", "Site", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "#headlessui-menu-button-:Rfdmm:", "severity": "info", "elementCount": 1, "affectedSelectors": ["#headlessui-menu-button-:Rfdmm:", "Role", "button"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "#headlessui-menu-button-:Rfdmm:", "severity": "info", "elementCount": 1, "affectedSelectors": ["#headlessui-menu-button-:Rfdmm:", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Run a free scanScan now\"", "selector": "input.grow.rounded-lg.border-[0.5px].border-black-v2-50.bg-white.px-6.py-3.5.font-formadjrtext.text-lg.leading-7.text-dark-blue.placeholder:text-[#bcbec4].sm:pr-44", "severity": "info", "elementCount": 1, "affectedSelectors": ["input.grow.rounded-lg.border-[0.5px].border-black-v2-50.bg-white.px-6.py-3.5.font-formadjrtext.text-lg.leading-7.text-dark-blue.placeholder:text-[#bcbec4].sm:pr-44", "Name", "Run", "a", "free", "scanScan", "now"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: textbox", "selector": "input.grow.rounded-lg.border-[0.5px].border-black-v2-50.bg-white.px-6.py-3.5.font-formadjrtext.text-lg.leading-7.text-dark-blue.placeholder:text-[#bcbec4].sm:pr-44", "severity": "info", "elementCount": 1, "affectedSelectors": ["input.grow.rounded-lg.border-[0.5px].border-black-v2-50.bg-white.px-6.py-3.5.font-formadjrtext.text-lg.leading-7.text-dark-blue.placeholder:text-[#bcbec4].sm:pr-44", "Role", "textbox"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "input.grow.rounded-lg.border-[0.5px].border-black-v2-50.bg-white.px-6.py-3.5.font-formadjrtext.text-lg.leading-7.text-dark-blue.placeholder:text-[#bcbec4].sm:pr-44", "severity": "info", "elementCount": 1, "affectedSelectors": ["input.grow.rounded-lg.border-[0.5px].border-black-v2-50.bg-white.px-6.py-3.5.font-formadjrtext.text-lg.leading-7.text-dark-blue.placeholder:text-[#bcbec4].sm:pr-44", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Run a free scanScan now\"", "selector": "button.right-1.flex.w-full.flex-row.items-center.justify-center.whitespace-nowrap.rounded-lg.border-[0.5px].border-green-highlight-dark.bg-green-highlight.px-4.py-3.font-formadjrtext.text-base.font-bold.leading-5.tracking-[0.4px].text-dark-blue.sm:absolute.sm:w-auto.sm:rounded", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.right-1.flex.w-full.flex-row.items-center.justify-center.whitespace-nowrap.rounded-lg.border-[0.5px].border-green-highlight-dark.bg-green-highlight.px-4.py-3.font-formadjrtext.text-base.font-bold.leading-5.tracking-[0.4px].text-dark-blue.sm:absolute.sm:w-auto.sm:rounded", "Name", "Run", "a", "free", "scanScan", "now"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "button.right-1.flex.w-full.flex-row.items-center.justify-center.whitespace-nowrap.rounded-lg.border-[0.5px].border-green-highlight-dark.bg-green-highlight.px-4.py-3.font-formadjrtext.text-base.font-bold.leading-5.tracking-[0.4px].text-dark-blue.sm:absolute.sm:w-auto.sm:rounded", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.right-1.flex.w-full.flex-row.items-center.justify-center.whitespace-nowrap.rounded-lg.border-[0.5px].border-green-highlight-dark.bg-green-highlight.px-4.py-3.font-formadjrtext.text-base.font-bold.leading-5.tracking-[0.4px].text-dark-blue.sm:absolute.sm:w-auto.sm:rounded", "Role", "button"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "button.right-1.flex.w-full.flex-row.items-center.justify-center.whitespace-nowrap.rounded-lg.border-[0.5px].border-green-highlight-dark.bg-green-highlight.px-4.py-3.font-formadjrtext.text-base.font-bold.leading-5.tracking-[0.4px].text-dark-blue.sm:absolute.sm:w-auto.sm:rounded", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.right-1.flex.w-full.flex-row.items-center.justify-center.whitespace-nowrap.rounded-lg.border-[0.5px].border-green-highlight-dark.bg-green-highlight.px-4.py-3.font-formadjrtext.text-base.font-bold.leading-5.tracking-[0.4px].text-dark-blue.sm:absolute.sm:w-auto.sm:rounded", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Learn more\"", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Name", "Learn", "more"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Get started\"", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Name", "Get", "started"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Learn more\"", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Name", "Learn", "more"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Get started\"", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Name", "Get", "started"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.tracking-[0.24px].text-dark-blue.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Run a free scan\"", "selector": "a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px].!bg-dark-blue.!text-white", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px].!bg-dark-blue.!text-white", "Name", "Run", "a", "free", "scan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px].!bg-dark-blue.!text-white", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px].!bg-dark-blue.!text-white", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px].!bg-dark-blue.!text-white", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px].!bg-dark-blue.!text-white", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Demo Account\"", "selector": "#demo-account-login-button", "severity": "info", "elementCount": 1, "affectedSelectors": ["#demo-account-login-button", "Name", "Demo", "Account"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "#demo-account-login-button", "severity": "info", "elementCount": 1, "affectedSelectors": ["#demo-account-login-button", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "#demo-account-login-button", "severity": "info", "elementCount": 1, "affectedSelectors": ["#demo-account-login-button", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Get started\"", "selector": "a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "Name", "Get", "started"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Read our API Docs\"", "selector": "a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "Name", "Read", "our", "API", "Docs"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-10.flex.flex-row.items-center.font-medium.tracking-[0.24px].hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"previous slide / item\"", "selector": "button.control-arrow.control-prev.control-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.control-arrow.control-prev.control-disabled", "Name", "previous", "slide", "item"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "button.control-arrow.control-prev.control-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.control-arrow.control-prev.control-disabled", "Role", "button"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "button.control-arrow.control-prev.control-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.control-arrow.control-prev.control-disabled", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.836Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"next slide / item\"", "selector": "button.control-arrow.control-next.control-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.control-arrow.control-next.control-disabled", "Name", "next", "slide", "item"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "button.control-arrow.control-next.control-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.control-arrow.control-next.control-disabled", "Role", "button"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "button.control-arrow.control-next.control-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.control-arrow.control-next.control-disabled", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Previous Item\"", "selector": "button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0", "Name", "Previous", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 64, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0", "Role", "button"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 65, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 66, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Next Item\"", "selector": "button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "Name", "Next", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 67, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "Role", "button"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 68, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 69, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Learn about OpenVAS\"", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Name", "Learn", "about", "OpenVAS"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 70, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 71, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 72, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Learn about Nmap\"", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Name", "Learn", "about", "Nmap"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 73, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 74, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 75, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Learn about OWASP Zap\"", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Name", "Learn", "about", "OWASP", "Zap"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 76, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 77, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 78, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Learn about S<PERSON>ly<PERSON>\"", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Name", "Learn", "about", "Sslyze"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 79, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 80, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 81, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Get started\"", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Name", "Get", "started"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 82, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 83, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 84, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"How to scan your API\"", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Name", "How", "to", "scan", "your", "API"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 85, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 86, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 87, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Get started\"", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Name", "Get", "started"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 88, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 89, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 90, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Read our API Docs\"", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Name", "Read", "our", "API", "Docs"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 91, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 92, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.mt-4.flex.items-center.text-base.font-medium.tracking-[0.4px].hover:underline.sm:mt-6", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 93, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"View all\"", "selector": "a.flex.flex-row.items-center.font-medium.leading-6.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.leading-6.hover:underline", "Name", "View", "all"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 94, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.flex.flex-row.items-center.font-medium.leading-6.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.leading-6.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 95, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.flex.flex-row.items-center.font-medium.leading-6.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.flex.flex-row.items-center.font-medium.leading-6.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 96, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"previous slide / item\"", "selector": "button.control-arrow.control-prev.control-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.control-arrow.control-prev.control-disabled", "Name", "previous", "slide", "item"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 97, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "button.control-arrow.control-prev.control-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.control-arrow.control-prev.control-disabled", "Role", "button"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 98, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "button.control-arrow.control-prev.control-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.control-arrow.control-prev.control-disabled", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 99, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Updates<PERSON><PERSON>’s new at HostedScan - June 2025\"", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Name", "UpdatesWhat", "s", "new", "at", "HostedScan", "June"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 100, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 101, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 102, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"ScannersOur Favorite Open-Source Vulnerability Scanning Tools\"", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Name", "ScannersOur", "Favorite", "Open-Source", "Vulnerability", "Scanning", "Tools"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 103, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 104, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 105, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"CybersecurityVulnerability Management SLAs: A Guide\"", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Name", "CybersecurityVulnerability", "Management", "SLAs", "A", "Guide"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 106, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 107, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 108, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.837Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"UpdatesUpcoming Improvements for Q2 2024\"", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Name", "UpdatesUpcoming", "Improvements", "for", "Q2"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 109, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 110, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 111, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"CybersecurityIntroducing HostedScan's Knowledge Hub and a couple of tips\"", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Name", "CybersecurityIntroducing", "HostedScan", "s", "Knowledge", "<PERSON><PERSON>", "and", "a", "couple", "of", "tips"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 112, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 113, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.relative.flex.h-[350px].w-[300px].flex-col.justify-end.gap-2.rounded-xl.p-6.transition-transform.duration-300.hover:scale-105.xl:w-[320px]", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 114, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"next slide / item\"", "selector": "button.control-arrow.control-next.control-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.control-arrow.control-next.control-disabled", "Name", "next", "slide", "item"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 115, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "button.control-arrow.control-next.control-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.control-arrow.control-next.control-disabled", "Role", "button"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 116, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "button.control-arrow.control-next.control-disabled", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.control-arrow.control-next.control-disabled", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 117, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Previous Item\"", "selector": "button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0", "Name", "Previous", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 118, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0", "Role", "button"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 119, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15.opacity-0", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 120, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Next Item\"", "selector": "button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "Name", "Next", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 121, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: button", "selector": "button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "Role", "button"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 122, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "severity": "info", "elementCount": 1, "affectedSelectors": ["button.rounded-full.bg-light-gray.p-3.hover:bg-black-v2-15", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 123, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Run a free scan\"", "selector": "a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px]", "Name", "Run", "a", "free", "scan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 124, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px]", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 125, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px]", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.w-full.rounded.border-[0.5px].border-white/20.bg-white/10.py-4.text-center.font-formadjrtext.font-bold.leading-4.text-light-gray.sm:max-w-[192px]", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 126, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Demo Account\"", "selector": "#demo-account-login-button", "severity": "info", "elementCount": 1, "affectedSelectors": ["#demo-account-login-button", "Name", "Demo", "Account"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 127, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "#demo-account-login-button", "severity": "info", "elementCount": 1, "affectedSelectors": ["#demo-account-login-button", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 128, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "#demo-account-login-button", "severity": "info", "elementCount": 1, "affectedSelectors": ["#demo-account-login-button", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 129, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Our Scanners\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Our", "Scanners"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 130, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 131, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 132, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Pricing\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Pricing"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 133, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 134, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 135, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Documentation\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Documentation"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 136, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 137, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 138, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Qualys vs HostedScan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Qualys", "vs", "HostedScan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 139, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 140, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 141, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"<PERSON><PERSON><PERSON> Tenable vs HostedScan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "<PERSON><PERSON><PERSON>", "Tenable", "vs", "HostedScan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 142, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 143, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 144, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Netsparker vs HostedScan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Netsparker", "vs", "HostedScan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 145, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 146, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 147, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Detectify vs HostedScan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Detectify", "vs", "HostedScan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 148, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 149, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 150, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"OpenVAS - Network Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "OpenVAS", "Network", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 151, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 152, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 153, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Nmap - Port Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Nmap", "Port", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 154, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 155, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 156, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"OWASP ZAP - Web Applications\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "OWASP", "ZAP", "Web", "Applications"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 157, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 158, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 159, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"OWASP ZAP - API Security Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "OWASP", "ZAP", "API", "Security", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 160, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 161, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 162, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"SSLyze - TLS & SSL\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "SSLyze", "TLS", "SSL"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 163, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 164, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 165, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Attack Surface Management (EASM)\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Attack", "Surface", "Management", "EASM"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 166, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 167, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 168, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Automated Penetration Testing\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Automated", "Penetration", "Testing"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 169, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 170, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 171, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Authenticated Web App Scanning\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Authenticated", "Web", "App", "Scanning"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 172, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 173, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 174, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"External Vulnerability Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "External", "Vulnerability", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 175, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 176, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 177, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Internal Vulnerability Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Internal", "Vulnerability", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 178, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 179, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 180, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Wordpress Vulnerability Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Wordpress", "Vulnerability", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 181, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 182, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 183, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.838Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Cloud Security\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Cloud", "Security"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 184, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 185, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 186, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Vulnerability Management\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Vulnerability", "Management"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 187, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 188, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 189, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"DAST Scanner\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "DAST", "Scanner"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 190, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 191, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 192, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Online Port Scanner\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Online", "Port", "Scanner"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 193, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 194, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 195, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Online Vulnerability Scanner\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Online", "Vulnerability", "Scanner"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 196, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 197, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 198, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Continuous Security Monitoring\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Continuous", "Security", "Monitoring"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 199, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 200, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 201, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"DevSecOps Scanning\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "DevSecOps", "Scanning"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 202, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 203, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 204, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Enterprise & Reseller\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Enterprise", "Reseller"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 205, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 206, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 207, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"MSPs & MSSPs\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "MSPs", "MSSPs"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 208, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 209, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 210, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"ISO 27001\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "ISO"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 211, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 212, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 213, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"SOC 2\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "SOC"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 214, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.839Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 215, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 216, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"GDPR\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "GDPR"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 217, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 218, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 219, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"TPN\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "TPN"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 220, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 221, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 222, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Affiliate Referral Program\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Affiliate", "Referral", "Program"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 223, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 224, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 225, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"SPF DKIM DMARC Security Tool\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "SPF", "DKIM", "DMARC", "Security", "Tool"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 226, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 227, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 228, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Subdomain Discovery Tool\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Subdomain", "Discovery", "Tool"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 229, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 230, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 231, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"About\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "About"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 232, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 233, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 234, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Careers\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Careers"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 235, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 236, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 237, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Terms & Policies\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Terms", "Policies"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 238, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 239, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 240, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Status\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Status"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 241, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 242, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 243, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"<EMAIL>\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "hello", "hostedscan.com"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 244, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 245, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 246, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"2212 Queen Anne Ave N Suite #521Seattle, WA 98109\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Queen", "<PERSON>", "Ave", "N", "Suite", "Seattle", "WA"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 247, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 248, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 249, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan Twitter Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "Twitter", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 250, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 251, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 252, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan LinkedIn Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "LinkedIn", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 253, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 254, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 255, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"<EMAIL>\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "hello", "hostedscan.com"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 256, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 257, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 258, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.840Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"2212 Queen Anne Ave N Suite #521Seattle, WA 98109\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Queen", "<PERSON>", "Ave", "N", "Suite", "Seattle", "WA"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 259, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 260, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 261, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan Twitter Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "Twitter", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 262, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 263, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 264, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan LinkedIn Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "LinkedIn", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 265, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 266, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 267, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"About\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "About"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 268, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 269, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 270, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Careers\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Careers"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 271, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 272, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 273, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Terms & Policies\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Terms", "Policies"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 274, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 275, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 276, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Status\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Status"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 277, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 278, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 279, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan Twitter Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "Twitter", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 280, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 281, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 282, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan LinkedIn Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "LinkedIn", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 283, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 284, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 285, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"About\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "About"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 286, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 287, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 288, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Careers\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Careers"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 289, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 290, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 291, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Terms & Policies\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Terms", "Policies"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 292, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 293, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 294, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Status\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Status"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 295, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 296, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 297, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"<EMAIL>\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "hello", "hostedscan.com"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 298, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 299, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 300, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"2212 Queen Anne Ave N Suite #521Seattle, WA 98109\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Queen", "<PERSON>", "Ave", "N", "Suite", "Seattle", "WA"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 301, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 302, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 303, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Our Scanners\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Our", "Scanners"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 304, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 305, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 306, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Pricing\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Pricing"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 307, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 308, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.841Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 309, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Documentation\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Documentation"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 310, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 311, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 312, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Qualys vs HostedScan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Qualys", "vs", "HostedScan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 313, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 314, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 315, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"<PERSON><PERSON><PERSON> Tenable vs HostedScan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "<PERSON><PERSON><PERSON>", "Tenable", "vs", "HostedScan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 316, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 317, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 318, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Netsparker vs HostedScan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Netsparker", "vs", "HostedScan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 319, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 320, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 321, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Detectify vs HostedScan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Detectify", "vs", "HostedScan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 322, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 323, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 324, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"OpenVAS - Network Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "OpenVAS", "Network", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 325, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 326, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 327, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Nmap - Port Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Nmap", "Port", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 328, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 329, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 330, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"OWASP ZAP - Web Applications\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "OWASP", "ZAP", "Web", "Applications"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 331, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 332, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 333, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"OWASP ZAP - API Security Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "OWASP", "ZAP", "API", "Security", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 334, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 335, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 336, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"SSLyze - TLS & SSL\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "SSLyze", "TLS", "SSL"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 337, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 338, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 339, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Attack Surface Management (EASM)\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Attack", "Surface", "Management", "EASM"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 340, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 341, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 342, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Automated Penetration Testing\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Automated", "Penetration", "Testing"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 343, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 344, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.842Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 345, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Authenticated Web App Scanning\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Authenticated", "Web", "App", "Scanning"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 346, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 347, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 348, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"External Vulnerability Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "External", "Vulnerability", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 349, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 350, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 351, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Internal Vulnerability Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Internal", "Vulnerability", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 352, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 353, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 354, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Wordpress Vulnerability Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Wordpress", "Vulnerability", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 355, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 356, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 357, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Cloud Security\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Cloud", "Security"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 358, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 359, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 360, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Vulnerability Management\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Vulnerability", "Management"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 361, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 362, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 363, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"DAST Scanner\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "DAST", "Scanner"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 364, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 365, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 366, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Online Port Scanner\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Online", "Port", "Scanner"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 367, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 368, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 369, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Online Vulnerability Scanner\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Online", "Vulnerability", "Scanner"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 370, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 371, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 372, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Continuous Security Monitoring\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Continuous", "Security", "Monitoring"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 373, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 374, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 375, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"DevSecOps Scanning\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "DevSecOps", "Scanning"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 376, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 377, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 378, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Enterprise & Reseller\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Enterprise", "Reseller"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 379, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 380, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 381, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"MSPs & MSSPs\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "MSPs", "MSSPs"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 382, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 383, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 384, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"ISO 27001\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "ISO"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 385, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 386, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 387, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"SOC 2\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "SOC"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 388, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 389, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 390, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"GDPR\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "GDPR"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 391, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 392, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 393, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"TPN\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "TPN"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 394, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 395, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 396, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"SPF DKIM DMARC Security Tool\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "SPF", "DKIM", "DMARC", "Security", "Tool"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 397, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 398, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 399, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Subdomain Discovery Tool\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Subdomain", "Discovery", "Tool"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 400, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 401, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 402, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Affiliate Referral Program\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Affiliate", "Referral", "Program"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 403, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 404, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 405, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"About\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "About"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 406, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 407, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 408, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Careers\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Careers"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 409, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 410, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 411, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Terms & Policies\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Terms", "Policies"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 412, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 413, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 414, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Status\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Status"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 415, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 416, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 417, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.843Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"<EMAIL>\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "hello", "hostedscan.com"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 418, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 419, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 420, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"2212 Queen Anne Ave N Suite #521Seattle, WA 98109\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Queen", "<PERSON>", "Ave", "N", "Suite", "Seattle", "WA"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 421, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 422, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 423, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan Twitter Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "Twitter", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 424, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 425, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 426, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan LinkedIn Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "LinkedIn", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 427, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 428, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 429, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"<EMAIL>\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "hello", "hostedscan.com"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 430, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 431, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 432, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"2212 Queen Anne Ave N Suite #521Seattle, WA 98109\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Queen", "<PERSON>", "Ave", "N", "Suite", "Seattle", "WA"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 433, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 434, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 435, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan Twitter Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "Twitter", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 436, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 437, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 438, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan LinkedIn Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "LinkedIn", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 439, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 440, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 441, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"About\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "About"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 442, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 443, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 444, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Careers\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Careers"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 445, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 446, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 447, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Terms & Policies\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Terms", "Policies"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 448, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 449, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 450, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Status\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Status"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 451, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 452, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 453, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan Twitter Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "Twitter", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 454, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 455, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 456, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan LinkedIn Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "LinkedIn", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 457, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 458, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 459, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"About\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "About"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 460, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 461, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 462, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Careers\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Careers"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 463, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 464, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 465, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Terms & Policies\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Terms", "Policies"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 466, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 467, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 468, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Status\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Status"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 469, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 470, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 471, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"<EMAIL>\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "hello", "hostedscan.com"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 472, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 473, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 474, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"2212 Queen Anne Ave N Suite #521Seattle, WA 98109\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Queen", "<PERSON>", "Ave", "N", "Suite", "Seattle", "WA"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 475, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 476, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 477, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.844Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Attack Surface Management (EASM)\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Attack", "Surface", "Management", "EASM"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 478, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 479, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 480, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Automated Penetration Testing\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Automated", "Penetration", "Testing"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 481, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 482, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 483, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Authenticated Web App Scanning\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Authenticated", "Web", "App", "Scanning"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 484, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 485, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 486, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"External Vulnerability Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "External", "Vulnerability", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 487, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 488, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 489, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Internal Vulnerability Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Internal", "Vulnerability", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 490, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 491, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 492, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Wordpress Vulnerability Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Wordpress", "Vulnerability", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 493, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 494, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 495, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Cloud Security\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Cloud", "Security"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 496, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 497, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 498, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Vulnerability Management\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Vulnerability", "Management"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 499, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 500, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 501, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"DAST Scanner\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "DAST", "Scanner"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 502, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 503, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 504, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Online Port Scanner\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Online", "Port", "Scanner"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 505, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 506, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 507, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Online Vulnerability Scanner\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Online", "Vulnerability", "Scanner"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 508, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 509, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 510, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Continuous Security Monitoring\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Continuous", "Security", "Monitoring"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 511, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 512, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 513, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"DevSecOps Scanning\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "DevSecOps", "Scanning"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 514, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 515, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 516, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Enterprise & Reseller\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Enterprise", "Reseller"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 517, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 518, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 519, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"MSPs & MSSPs\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "MSPs", "MSSPs"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 520, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 521, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 522, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"ISO 27001\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "ISO"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 523, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 524, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 525, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"SOC 2\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "SOC"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 526, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 527, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 528, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"GDPR\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "GDPR"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 529, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 530, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 531, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"TPN\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "TPN"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 532, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 533, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 534, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Qualys vs HostedScan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Qualys", "vs", "HostedScan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 535, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.845Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 536, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 537, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"<PERSON><PERSON><PERSON> Tenable vs HostedScan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "<PERSON><PERSON><PERSON>", "Tenable", "vs", "HostedScan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 538, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 539, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 540, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Netsparker vs HostedScan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Netsparker", "vs", "HostedScan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 541, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 542, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 543, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Detectify vs HostedScan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Detectify", "vs", "HostedScan"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 544, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 545, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 546, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"OpenVAS - Network Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "OpenVAS", "Network", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 547, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 548, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 549, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Nmap - Port Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Nmap", "Port", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 550, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 551, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 552, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"OWASP ZAP - Web Applications\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "OWASP", "ZAP", "Web", "Applications"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 553, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 554, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 555, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"OWASP ZAP - API Security Scan\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "OWASP", "ZAP", "API", "Security", "<PERSON><PERSON>"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 556, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 557, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 558, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"SSLyze - TLS & SSL\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "SSLyze", "TLS", "SSL"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 559, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 560, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 561, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"SPF DKIM DMARC Security Tool\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "SPF", "DKIM", "DMARC", "Security", "Tool"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 562, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 563, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 564, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Subdomain Discovery Tool\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Subdomain", "Discovery", "Tool"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 565, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 566, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 567, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Affiliate Referral Program\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Affiliate", "Referral", "Program"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 568, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 569, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 570, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"About\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "About"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 571, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 572, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 573, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Careers\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Careers"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 574, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 575, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 576, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Terms & Policies\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Terms", "Policies"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 577, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 578, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 579, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Status\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Status"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 580, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 581, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 582, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"<EMAIL>\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "hello", "hostedscan.com"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 583, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 584, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 585, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"2212 Queen Anne Ave N Suite #521Seattle, WA 98109\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Queen", "<PERSON>", "Ave", "N", "Suite", "Seattle", "WA"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 586, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 587, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 588, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan Twitter Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "Twitter", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 589, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 590, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 591, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan LinkedIn Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "LinkedIn", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 592, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 593, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 594, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"<EMAIL>\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "hello", "hostedscan.com"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 595, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 596, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 597, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"2212 Queen Anne Ave N Suite #521Seattle, WA 98109\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Queen", "<PERSON>", "Ave", "N", "Suite", "Seattle", "WA"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 598, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.846Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 599, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 600, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan Twitter Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "Twitter", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 601, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 602, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 603, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan LinkedIn Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "LinkedIn", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 604, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 605, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 606, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"About\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "About"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 607, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 608, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 609, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Careers\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Careers"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 610, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 611, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 612, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Terms & Policies\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Terms", "Policies"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 613, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 614, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 615, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Status\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Status"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 616, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 617, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 618, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan Twitter Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "Twitter", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 619, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 620, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 621, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"HostedScan LinkedIn Profile\"", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Name", "HostedScan", "LinkedIn", "Profile"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 622, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 623, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.rounded-md.border-[0.75px].border-white/[.15].bg-white/10.p-2.sm:p-1.5", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 624, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"About\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "About"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 625, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 626, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 627, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Careers\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Careers"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 628, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 629, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 630, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Terms & Policies\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Terms", "Policies"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 631, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 632, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 633, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"Status\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Status"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 634, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 635, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 636, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"<EMAIL>\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "hello", "hostedscan.com"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 637, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 638, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 639, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has accessible name", "value": "Name: \"2212 Queen Anne Ave N Suite #521Seattle, WA 98109\"", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Name", "Queen", "<PERSON>", "Ave", "N", "Suite", "Seattle", "WA"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 640, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element has defined role", "value": "Role: link", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "Role", "link"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 641, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Element state is properly defined", "value": "State/value information available", "selector": "a.text-sm.leading-5.!text-white.hover:underline", "severity": "info", "elementCount": 1, "affectedSelectors": ["a.text-sm.leading-5.!text-white.hover:underline", "State", "value", "information", "available"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 642, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}, {"type": "text", "description": "Valid <PERSON> role used", "value": "Role: alert", "selector": "#__next-route-announcer__", "severity": "info", "elementCount": 1, "affectedSelectors": ["#__next-route-announcer__", "Role", "alert"], "metadata": {"scanDuration": 96, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 643, "ruleId": "WCAG-009", "ruleName": "Name, Role, Value", "timestamp": "2025-07-11T11:37:30.847Z"}}}], "recommendations": [], "executionTime": 0, "errorMessage": "", "manualReviewItems": []}, "timestamp": 1752233850847, "hash": "3b038f0f4a91a8deb8d3fc74c21a6157", "accessCount": 1, "lastAccessed": 1752233850847, "size": 357331}
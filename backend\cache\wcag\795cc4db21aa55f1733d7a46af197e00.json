{"data": {"ruleId": "WCAG-035", "ruleName": "Multiple Ways", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "info", "description": "Advanced navigation structure analysis for multiple ways validation", "element": "navigation-elements", "value": "{\"overallScore\":80,\"criticalIssues\":[],\"recommendations\":[\"Increase target sizes to meet WCAG minimum requirements\"],\"performanceMetrics\":{\"analysisTime\":693,\"elementsAnalyzed\":1000,\"breakpointsTested\":5}}", "severity": "info", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "recommendations", "Increase", "target", "sizes", "to", "meet", "WCAG", "minimum", "requirements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "breakpointsTested"], "metadata": {"scanDuration": 1973, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-035", "ruleName": "Multiple Ways", "timestamp": "2025-07-11T11:39:47.677Z"}}}, {"type": "error", "description": "Insufficient navigation methods available", "value": "Found 1 method(s), need at least 2 ways to locate pages", "selector": "body", "elementCount": 1, "affectedSelectors": ["body", "Found", "method", "s", "need", "at", "least", "ways", "to", "locate", "pages"], "severity": "error", "metadata": {"scanDuration": 1973, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-035", "ruleName": "Multiple Ways", "timestamp": "2025-07-11T11:39:47.677Z"}}}, {"type": "info", "description": "Navigation method found: navigation", "value": "Navigation menu or links - Quality: good", "selector": "nav, [role=\"navigation\"]", "elementCount": 2, "affectedSelectors": ["nav", "[role=\"navigation\"]", "Navigation", "menu", "or", "links", "Quality", "good"], "severity": "info", "metadata": {"scanDuration": 1973, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-035", "ruleName": "Multiple Ways", "timestamp": "2025-07-11T11:39:47.677Z"}}}, {"type": "warning", "description": "Multiple ways navigation analysis summary", "value": "1 navigation methods found: navigation", "selector": "nav, form, a", "elementCount": 3, "affectedSelectors": ["nav", "form", "a", "navigation", "methods", "found"], "severity": "warning", "metadata": {"scanDuration": 1973, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-035", "ruleName": "Multiple Ways", "timestamp": "2025-07-11T11:39:47.677Z"}}}], "recommendations": ["Implement at least 2 different ways to locate and navigate to pages", "Consider adding a search function for large sites", "Provide a sitemap or site index page", "Use breadcrumb navigation for hierarchical content", "Consider adding search functionality for better user experience", "Consider providing a sitemap for comprehensive site overview"], "executionTime": 711, "originalScore": 50}, "timestamp": 1752233987677, "hash": "f0441c4eb70458c25eb3a85b9436aad4", "accessCount": 1, "lastAccessed": 1752233987677, "size": 3027}
{"data": {"ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "category": "perceivable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AA", "status": "passed", "score": 100, "maxScore": 100, "weight": 0.0535, "automated": true, "evidence": [{"type": "info", "description": "Advanced hover/focus triggered content analysis with layout validation", "element": "hover-focus-content", "value": "{\"overallScore\":80,\"criticalIssues\":[],\"recommendations\":[\"Increase target sizes to meet WCAG minimum requirements\"],\"performanceMetrics\":{\"analysisTime\":676,\"elementsAnalyzed\":1000,\"breakpointsTested\":5}}", "severity": "info", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "recommendations", "Increase", "target", "sizes", "to", "meet", "WCAG", "minimum", "requirements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "breakpointsTested"], "metadata": {"scanDuration": 1981, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-043", "ruleName": "Content on Hover or Focus", "timestamp": "2025-07-11T11:38:44.966Z"}}}], "recommendations": ["Ensure hover/focus content is dismissible (ESC key or close button)", "Make hover/focus content hoverable (pointer-events: auto)", "Keep hover/focus content persistent until dismissed", "Replace title attributes with accessible custom tooltips", "Provide keyboard alternatives for hover-only interactions"], "executionTime": 686, "originalScore": 100}, "timestamp": 1752233924966, "hash": "6e6188d52df9d60790f18f690ba224e4", "accessCount": 1, "lastAccessed": 1752233924966, "size": 1468}
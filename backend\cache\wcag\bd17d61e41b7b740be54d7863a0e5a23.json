{"data": {"ruleId": "WCAG-057", "ruleName": "Status Messages", "category": "robust", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AA", "status": "passed", "score": 100, "maxScore": 100, "weight": 0.0458, "automated": true, "evidence": [{"type": "interaction", "description": "Status messages accessibility analysis", "value": "Found 3 status elements and 0 potential status messages", "elementCount": 1, "affectedSelectors": ["Found", "status", "elements", "and", "potential", "messages"], "severity": "info", "metadata": {"scanDuration": 37, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-057", "ruleName": "Status Messages", "timestamp": "2025-07-11T11:39:16.436Z"}}}], "recommendations": ["Add role=\"alert\" and aria-live=\"assertive\" to error messages", "Add role=\"status\" and aria-live=\"polite\" to success and info messages", "Use aria-atomic=\"true\" for messages that should be read completely", "Ensure status messages are programmatically determinable"], "executionTime": 7, "originalScore": 100}, "timestamp": 1752233956436, "hash": "950abdc35a0cde5a9d9af77a0cf5054b", "accessCount": 1, "lastAccessed": 1752233956436, "size": 1046}
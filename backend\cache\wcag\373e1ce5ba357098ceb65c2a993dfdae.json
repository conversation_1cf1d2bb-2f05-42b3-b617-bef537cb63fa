{"data": [{"type": "text", "description": "Abbreviations without expanded forms", "value": "Found 36 abbreviations that need expanded forms for AAA compliance", "elementCount": 1, "affectedSelectors": ["Found", "abbreviations", "that", "need", "expanded", "forms", "for", "AAA", "compliance"], "severity": "warning", "metadata": {"scanDuration": 863, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.75, "checkType": "abbreviation-analysis", "abbreviationDetection": true, "expansionValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.199Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Abbreviation without expansion: \"proven\"", "value": "Context: \"vulnerabilities and secure your company.PROVEN TOOLSLevel up your company's cybersecur\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 863, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.75, "checkType": "abbreviation-analysis", "abbreviationDetection": true, "expansionValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.199Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "vulnerabilities", "and", "secure", "your", "company.PROVEN", "TOOLSLevel", "up", "company", "s", "cybersecur"]}, {"type": "text", "description": "Abbreviation without expansion: \"soc\"", "value": "Context: \"g is essential for your compliance with SOC 2, ISO 27001, cyber insurance, and more\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 863, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.75, "checkType": "abbreviation-analysis", "abbreviationDetection": true, "expansionValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.199Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "g", "is", "essential", "for", "your", "compliance", "with", "SOC", "ISO", "cyber", "insurance", "and", "more"]}, {"type": "text", "description": "Abbreviation without expansion: \"iso\"", "value": "Context: \"sential for your compliance with SOC 2, ISO 27001, cyber insurance, and moreLearn m\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 863, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.75, "checkType": "abbreviation-analysis", "abbreviationDetection": true, "expansionValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.199Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "sential", "for", "your", "compliance", "with", "SOC", "ISO", "cyber", "insurance", "and", "<PERSON><PERSON><PERSON><PERSON>", "m"]}, {"type": "text", "description": "Abbreviation without expansion: \"gdpr\"", "value": "Context: \"educe liabilityWith regulations such as GDPR and CCPA, failure to maintain reasonabl\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 863, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.75, "checkType": "abbreviation-analysis", "abbreviationDetection": true, "expansionValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.199Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "educe", "liabilityWith", "regulations", "such", "as", "GDPR", "and", "CCPA", "failure", "to", "maintain", "<PERSON><PERSON><PERSON>"]}, {"type": "text", "description": "Abbreviation without expansion: \"ccpa\"", "value": "Context: \"bilityWith regulations such as GDPR and CCPA, failure to maintain reasonable securit\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 863, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.75, "checkType": "abbreviation-analysis", "abbreviationDetection": true, "expansionValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.199Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "bilityWith", "regulations", "such", "as", "GDPR", "and", "CCPA", "failure", "to", "maintain", "reasonable", "securit"]}, {"type": "text", "description": "Abbreviation without expansion: \"owasp\"", "value": "Context: \"curity vulnerabilitiesIdentify CVEs and OWASP Top 10 issues in your systems. Prioriti\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 863, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.75, "checkType": "abbreviation-analysis", "abbreviationDetection": true, "expansionValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 6, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.199Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "curity", "vulnerabilitiesIdentify", "CVEs", "and", "OWASP", "Top", "issues", "in", "your", "systems", "Prioriti"]}, {"type": "text", "description": "Abbreviation without expansion: \"reporting\"", "value": "Context: \"arted Run a free scanDemo AccountCUSTOM REPORTING ENGINEComprehensive reports, that alway\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 863, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.75, "checkType": "abbreviation-analysis", "abbreviationDetection": true, "expansionValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 7, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.200Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "arted", "Run", "a", "free", "scanDemo", "AccountCUSTOM", "REPORTING", "ENGINEComprehensive", "reports", "that", "alway"]}, {"type": "text", "description": "Abbreviation without expansion: \"pdf\"", "value": "Context: \"ate vulnerability risksGet an executive PDF to share. See at a glance the vulnerabi\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 863, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.75, "checkType": "abbreviation-analysis", "abbreviationDetection": true, "expansionValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 8, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.200Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "ate", "vulnerability", "risksGet", "an", "executive", "PDF", "to", "share", "See", "at", "a", "glance", "the", "vulnerabi"]}, {"type": "text", "description": "Abbreviation without expansion: \"csv\"", "value": "Context: \"Use our built in reporting or export as CSV, JSON and XML to take into your BI tool\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 863, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.75, "checkType": "abbreviation-analysis", "abbreviationDetection": true, "expansionValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 9, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.200Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "Use", "our", "built", "in", "reporting", "or", "export", "as", "CSV", "JSON", "and", "XML", "to", "take", "into", "your", "BI", "tool"]}, {"type": "text", "description": "Abbreviation without expansion: \"json\"", "value": "Context: \"ur built in reporting or export as CSV, JSON and XML to take into your BI tools, for\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 863, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.75, "checkType": "abbreviation-analysis", "abbreviationDetection": true, "expansionValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 10, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-11T11:39:09.200Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.****************, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "ur", "built", "in", "reporting", "or", "export", "as", "CSV", "JSON", "and", "XML", "to", "take", "into", "your", "BI", "tools", "for"]}], "timestamp": 1752233949200, "hash": "15f6aa941c7ccf183633cb4fa8af5cee", "accessCount": 1, "lastAccessed": 1752233949200, "size": 10039}
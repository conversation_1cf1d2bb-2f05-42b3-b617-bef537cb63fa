{"data": {"ruleId": "WCAG-001", "ruleName": "Non-text Content", "category": "perceivable", "wcagVersion": "2.2", "successCriterion": "0.0.0", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.08, "automated": false, "evidence": [{"type": "text", "description": "Non-text content analysis summary", "value": "0/72 elements pass automated checks, 61 require manual review", "severity": "error", "elementCount": 0, "affectedSelectors": ["elements", "pass", "automated", "checks", "require", "manual", "review"], "fixExample": {"before": "<!-- Inaccessible element -->", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"BbAmericas\" - accuracy verification needed", "selector": "img:nth-of-type(1)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(1)", "Alt", "text", "present", "BbAmericas", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Porsche\" - accuracy verification needed", "selector": "img:nth-of-type(2)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(2)", "Alt", "text", "present", "Porsche", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"ExpediaGroup\" - accuracy verification needed", "selector": "img:nth-of-type(3)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(3)", "Alt", "text", "present", "ExpediaGroup", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WeMakeApps\" - accuracy verification needed", "selector": "img:nth-of-type(4)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(4)", "Alt", "text", "present", "WeMakeApps", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"SibylSoft\" - accuracy verification needed", "selector": "img:nth-of-type(5)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(5)", "Alt", "text", "present", "SibylSoft", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Luminary\" - accuracy verification needed", "selector": "img:nth-of-type(6)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(6)", "Alt", "text", "present", "Luminary", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"CoinMe\" - accuracy verification needed", "selector": "img:nth-of-type(7)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(7)", "Alt", "text", "present", "CoinMe", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Appetize\" - accuracy verification needed", "selector": "img:nth-of-type(8)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(8)", "Alt", "text", "present", "Appetize", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WonderProxy\" - accuracy verification needed", "selector": "img:nth-of-type(9)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(9)", "Alt", "text", "present", "WonderProxy", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Median\" - accuracy verification needed", "selector": "img:nth-of-type(10)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(10)", "Alt", "text", "present", "Median", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TaxiCaller\" - accuracy verification needed", "selector": "img:nth-of-type(11)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(11)", "Alt", "text", "present", "TaxiCaller", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Yamaha\" - accuracy verification needed", "selector": "img:nth-of-type(12)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(12)", "Alt", "text", "present", "Yamaha", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"UniversityOfOxford\" - accuracy verification needed", "selector": "img:nth-of-type(13)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(13)", "Alt", "text", "present", "UniversityOfOxford", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"BbAmericas\" - accuracy verification needed", "selector": "img:nth-of-type(14)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(14)", "Alt", "text", "present", "BbAmericas", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Porsche\" - accuracy verification needed", "selector": "img:nth-of-type(15)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(15)", "Alt", "text", "present", "Porsche", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.309Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"ExpediaGroup\" - accuracy verification needed", "selector": "img:nth-of-type(16)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(16)", "Alt", "text", "present", "ExpediaGroup", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WeMakeApps\" - accuracy verification needed", "selector": "img:nth-of-type(17)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(17)", "Alt", "text", "present", "WeMakeApps", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"SibylSoft\" - accuracy verification needed", "selector": "img:nth-of-type(18)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(18)", "Alt", "text", "present", "SibylSoft", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Luminary\" - accuracy verification needed", "selector": "img:nth-of-type(19)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(19)", "Alt", "text", "present", "Luminary", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"CoinMe\" - accuracy verification needed", "selector": "img:nth-of-type(20)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(20)", "Alt", "text", "present", "CoinMe", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Appetize\" - accuracy verification needed", "selector": "img:nth-of-type(21)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(21)", "Alt", "text", "present", "Appetize", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WonderProxy\" - accuracy verification needed", "selector": "img:nth-of-type(22)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(22)", "Alt", "text", "present", "WonderProxy", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Median\" - accuracy verification needed", "selector": "img:nth-of-type(23)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(23)", "Alt", "text", "present", "Median", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TaxiCaller\" - accuracy verification needed", "selector": "img:nth-of-type(24)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(24)", "Alt", "text", "present", "TaxiCaller", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Yamaha\" - accuracy verification needed", "selector": "img:nth-of-type(25)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(25)", "Alt", "text", "present", "Yamaha", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"UniversityOfOxford\" - accuracy verification needed", "selector": "img:nth-of-type(26)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(26)", "Alt", "text", "present", "UniversityOfOxford", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"UniversityOfOxford\" - accuracy verification needed", "selector": "img:nth-of-type(27)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(27)", "Alt", "text", "present", "UniversityOfOxford", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Yamaha\" - accuracy verification needed", "selector": "img:nth-of-type(28)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(28)", "Alt", "text", "present", "Yamaha", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TaxiCaller\" - accuracy verification needed", "selector": "img:nth-of-type(29)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(29)", "Alt", "text", "present", "TaxiCaller", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Median\" - accuracy verification needed", "selector": "img:nth-of-type(30)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(30)", "Alt", "text", "present", "Median", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WonderProxy\" - accuracy verification needed", "selector": "img:nth-of-type(31)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(31)", "Alt", "text", "present", "WonderProxy", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Appetize\" - accuracy verification needed", "selector": "img:nth-of-type(32)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(32)", "Alt", "text", "present", "Appetize", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"CoinMe\" - accuracy verification needed", "selector": "img:nth-of-type(33)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(33)", "Alt", "text", "present", "CoinMe", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Luminary\" - accuracy verification needed", "selector": "img:nth-of-type(34)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(34)", "Alt", "text", "present", "Luminary", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"SibylSoft\" - accuracy verification needed", "selector": "img:nth-of-type(35)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(35)", "Alt", "text", "present", "SibylSoft", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WeMakeApps\" - accuracy verification needed", "selector": "img:nth-of-type(36)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(36)", "Alt", "text", "present", "WeMakeApps", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"ExpediaGroup\" - accuracy verification needed", "selector": "img:nth-of-type(37)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(37)", "Alt", "text", "present", "ExpediaGroup", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Porsche\" - accuracy verification needed", "selector": "img:nth-of-type(38)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(38)", "Alt", "text", "present", "Porsche", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"BbAmericas\" - accuracy verification needed", "selector": "img:nth-of-type(39)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(39)", "Alt", "text", "present", "BbAmericas", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"UniversityOfOxford\" - accuracy verification needed", "selector": "img:nth-of-type(40)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(40)", "Alt", "text", "present", "UniversityOfOxford", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Yamaha\" - accuracy verification needed", "selector": "img:nth-of-type(41)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(41)", "Alt", "text", "present", "Yamaha", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"TaxiCaller\" - accuracy verification needed", "selector": "img:nth-of-type(42)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(42)", "Alt", "text", "present", "TaxiCaller", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Median\" - accuracy verification needed", "selector": "img:nth-of-type(43)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(43)", "Alt", "text", "present", "Median", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WonderProxy\" - accuracy verification needed", "selector": "img:nth-of-type(44)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(44)", "Alt", "text", "present", "WonderProxy", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.310Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Appetize\" - accuracy verification needed", "selector": "img:nth-of-type(45)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(45)", "Alt", "text", "present", "Appetize", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.311Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"CoinMe\" - accuracy verification needed", "selector": "img:nth-of-type(46)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(46)", "Alt", "text", "present", "CoinMe", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.311Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Luminary\" - accuracy verification needed", "selector": "img:nth-of-type(47)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(47)", "Alt", "text", "present", "Luminary", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.311Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"SibylSoft\" - accuracy verification needed", "selector": "img:nth-of-type(48)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(48)", "Alt", "text", "present", "SibylSoft", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.311Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"WeMakeApps\" - accuracy verification needed", "selector": "img:nth-of-type(49)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(49)", "Alt", "text", "present", "WeMakeApps", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.311Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"ExpediaGroup\" - accuracy verification needed", "selector": "img:nth-of-type(50)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(50)", "Alt", "text", "present", "ExpediaGroup", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.311Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Porsche\" - accuracy verification needed", "selector": "img:nth-of-type(51)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(51)", "Alt", "text", "present", "Porsche", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.311Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"BbAmericas\" - accuracy verification needed", "selector": "img:nth-of-type(52)", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img:nth-of-type(52)", "Alt", "text", "present", "BbAmericas", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.311Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "img.absolute.!h-[645px].!w-[1400px].object-contain", "severity": "error", "elementCount": 1, "affectedSelectors": ["img.absolute.!h-[645px].!w-[1400px].object-contain", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.311Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "img.absolute.object-contain", "severity": "error", "elementCount": 1, "affectedSelectors": ["img.absolute.object-contain", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.311Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "Decorative element should have empty alt=\"\" or aria-hidden=\"true\"", "selector": "img.absolute.object-contain", "severity": "error", "elementCount": 1, "affectedSelectors": ["img.absolute.object-contain", "Decorative", "element", "should", "have", "empty", "alt", "or", "aria-hidden", "true"], "fixExample": {"before": "<img><!-- Missing accessibility attributes --></img>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"<PERSON> Los<PERSON>\" - accuracy verification needed", "selector": "img.object-contain", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.object-contain", "Alt", "text", "present", "<PERSON>", "<PERSON><PERSON>", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"Alex\" - accuracy verification needed", "selector": "img.object-contain", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.object-contain", "Alt", "text", "present", "<PERSON>", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"<PERSON>\" - accuracy verification needed", "selector": "img.object-contain", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.object-contain", "Alt", "text", "present", "<PERSON>", "<PERSON>", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"<PERSON>\" - accuracy verification needed", "selector": "img.object-contain", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.object-contain", "Alt", "text", "present", "<PERSON>", "<PERSON>", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"blog entry\" - accuracy verification needed", "selector": "img.absolute.rounded-xl.object-cover.object-[40%_50%]", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.absolute.rounded-xl.object-cover.object-[40%_50%]", "Alt", "text", "present", "blog", "entry", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"blog entry\" - accuracy verification needed", "selector": "img.absolute.rounded-xl.object-cover.object-[40%_50%]", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.absolute.rounded-xl.object-cover.object-[40%_50%]", "Alt", "text", "present", "blog", "entry", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"blog entry\" - accuracy verification needed", "selector": "img.absolute.rounded-xl.object-cover.object-[40%_50%]", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.absolute.rounded-xl.object-cover.object-[40%_50%]", "Alt", "text", "present", "blog", "entry", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"blog entry\" - accuracy verification needed", "selector": "img.absolute.rounded-xl.object-cover.object-[40%_50%]", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.absolute.rounded-xl.object-cover.object-[40%_50%]", "Alt", "text", "present", "blog", "entry", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content requires manual review", "value": "Alt text present: \"blog entry\" - accuracy verification needed", "selector": "img.absolute.rounded-xl.object-cover.object-[40%_50%]", "severity": "warning", "elementCount": 1, "affectedSelectors": ["img.absolute.rounded-xl.object-cover.object-[40%_50%]", "Alt", "text", "present", "blog", "entry", "accuracy", "verification", "needed"], "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 64, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 65, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 66, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 67, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 68, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 69, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 70, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 71, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 72, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 73, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 74, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 75, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 76, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.312Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 77, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 78, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 79, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 80, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 81, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 82, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 83, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 84, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 85, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 86, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 87, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 88, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 89, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 90, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 91, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 92, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 93, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 94, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 95, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 96, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 97, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 98, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.313Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 99, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 100, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 101, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 102, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 103, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 104, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 105, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 106, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 107, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 108, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 109, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 110, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 111, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 112, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 113, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 114, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 115, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 116, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 117, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 118, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 119, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 120, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.314Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 121, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.315Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 122, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.315Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 123, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.315Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 124, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.315Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 125, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.315Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 126, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.315Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 127, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.315Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 128, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.315Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 129, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.315Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 130, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.315Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 131, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.315Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 132, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.315Z"}}}, {"type": "text", "description": "Non-text content lacks appropriate alternative", "value": "No alternative text provided", "selector": "svg.[object.SVGAnimatedString]", "severity": "error", "elementCount": 1, "affectedSelectors": ["svg.[object.SVGAnimatedString]", "No", "alternative", "text", "provided"], "fixExample": {"before": "<svg><!-- Missing accessibility attributes --></svg>", "after": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "description": "Add descriptive alternative text to images", "codeExample": "<img src=\"chart.png\" alt=\"Sales increased 25% from Q1 to Q2\">", "resources": ["https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html", "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt"]}, "metadata": {"scanDuration": 383, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 133, "ruleId": "WCAG-001", "ruleName": "Non-text Content", "timestamp": "2025-07-11T11:36:47.315Z"}}}], "recommendations": ["Add appropriate alternative text for all non-text content", "img.absolute.!h-[645px].!w-[1400px].object-contain: Add alt=\"\" for decorative images", "img.absolute.object-contain: Add alt=\"\" for decorative images", "img.absolute.object-contain: Add alt=\"\" for decorative images", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "svg.[object.SVGAnimatedString]: Add descriptive alt text, aria-label, or other text alternative", "Use empty alt=\"\" for decorative images", "Provide descriptive alternatives that convey the same information"], "executionTime": 0, "errorMessage": "Missing or inadequate alternative text for img.absolute.!h-[645px].!w-[1400px].object-contain; Missing or inadequate alternative text for img.absolute.object-contain; Missing or inadequate alternative text for img.absolute.object-contain; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]; Missing or inadequate alternative text for svg.[object.SVGAnimatedString]", "manualReviewItems": []}, "timestamp": 1752233807315, "hash": "66142fd9dc152a729c41fb94e2dc6e05", "accessCount": 1, "lastAccessed": 1752233807315, "size": 119115}
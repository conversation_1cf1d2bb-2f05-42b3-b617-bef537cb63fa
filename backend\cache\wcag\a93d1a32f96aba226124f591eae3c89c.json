{"data": {"ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0611, "automated": true, "evidence": [{"type": "info", "description": "Advanced layout analysis for skip links context", "element": "skip-links", "value": "{\"overallScore\":80,\"criticalIssues\":[],\"recommendations\":[\"Increase target sizes to meet WCAG minimum requirements\"],\"performanceMetrics\":{\"analysisTime\":1106,\"elementsAnalyzed\":1000,\"breakpointsTested\":5}}", "severity": "info", "elementCount": 1, "affectedSelectors": ["overallScore", "criticalIssues", "recommendations", "Increase", "target", "sizes", "to", "meet", "WCAG", "minimum", "requirements", "performanceMetrics", "analysisTime", "elementsAnalyzed", "breakpointsTested"], "metadata": {"scanDuration": 1441, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T19:26:50.533Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(2)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(2)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1441, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T19:26:50.533Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(3)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(3)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1441, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T19:26:50.533Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(4)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(4)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1441, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T19:26:50.533Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(5)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(5)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1441, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T19:26:50.533Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(6)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(6)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1441, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T19:26:50.533Z"}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(7)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(7)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 1441, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-11T19:26:50.533Z"}}}], "recommendations": ["Ensure skip links are visible and functional", "Verify skip link targets exist and are properly labeled", "Test skip links with keyboard navigation"], "executionTime": 1124, "originalScore": 10, "thresholdApplied": 75, "scoringDetails": "10.0% (threshold: 75%) - FAILED"}, "timestamp": 1752262010533, "hash": "1f7765d9e8e48b63068ab0e2ffba3556", "accessCount": 1, "lastAccessed": 1752262010533, "size": 4193, "metadata": {"originalKey": "rule:WCAG-047:053b13d2:add92319", "normalizedKey": "rule_wcag-047_053b13d2_add92319", "savedAt": 1752262010534, "version": "1.1", "keyHash": "c31cef4f1f70bf605883e423b32fb68b"}}
{"data": [{"type": "info", "description": "No motion-triggered functionality detected", "value": "<PERSON> does not appear to use device motion for triggering functions", "severity": "info", "metadata": {"scanDuration": 10, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-actuation-analysis", "motionEventDetection": true, "alternativeControlValidation": true, "advancedMotionDetection": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-056", "ruleName": "Motion Actuation", "timestamp": "2025-07-11T18:46:58.253Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["Page", "does", "not", "appear", "to", "use", "device", "motion", "for", "triggering", "functions"]}], "timestamp": 1752259618253, "hash": "fcd8d2b9e6c635be16dd81b4e0695d2d", "accessCount": 1, "lastAccessed": 1752259618253, "size": 848, "metadata": {"originalKey": "WCAG-056:WCAG-056:aW5mbzpObyBtb3Rpb24tdHJpZ2dlcmVk", "normalizedKey": "wcag-056_wcag-056_aw5mbzpobybtb3rpb24tdhjpz2dlcmvk", "savedAt": 1752259618253, "version": "1.1", "keyHash": "82f62fc9355eb0cab679e870e72f4b83"}}
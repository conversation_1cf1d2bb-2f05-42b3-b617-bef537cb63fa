{"data": [{"type": "text", "description": "Unusual words without definitions", "value": "Found 34 unusual words that may need definitions for AAA compliance", "elementCount": 1, "affectedSelectors": ["Found", "unusual", "words", "that", "may", "need", "definitions", "for", "AAA", "compliance"], "severity": "warning", "metadata": {"scanDuration": 65, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.244Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Unusual word without definition: \"json\"", "value": "Context: \"n reporting or export as CSV, JSON and XML to take into your BI\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 65, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.244Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "n", "reporting", "or", "export", "as", "CSV", "JSON", "and", "XML", "to", "take", "into", "your", "BI"]}, {"type": "text", "description": "Unusual word without definition: \"xml\"", "value": "Context: \"ng or export as CSV, JSON and XML to take into your BI tools, f\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 65, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.244Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "ng", "or", "export", "as", "CSV", "JSON", "and", "XML", "to", "take", "into", "your", "BI", "tools", "f"]}, {"type": "text", "description": "Unusual word without definition: \"rest\"", "value": "Context: \"risks are alerted.Get started REST API & WEBHOOKSDeveloper APIs\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 65, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.244Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "risks", "are", "alerted.Get", "started", "REST", "API", "WEBHOOKSDeveloper", "APIs"]}, {"type": "text", "description": "Unusual word without definition: \"ssl\"", "value": "Context: \"nerability scanswithout the hassleScan your websites, servers,\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 65, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.244Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "nerability", "scanswithout", "the", "hassleScan", "your", "websites", "servers"]}, {"type": "text", "description": "Unusual word without definition: \"tls\"", "value": "Context: \"P Zap SslyzeAnalyzes your SSL/TLS configuration and detects bad\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 65, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.244Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "P", "Zap", "SslyzeAnalyzes", "your", "SSL", "TLS", "configuration", "and", "detects", "bad"]}, {"type": "text", "description": "Unusual word without definition: \"leverage\"", "value": "Context: \"YOUR ENTIRE ATTACK SURFACELeverage the industry's  most-trusted\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 65, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 6, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.244Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "YOUR", "ENTIRE", "ATTACK", "SURFACELeverage", "the", "industry", "s", "most-trusted"]}, {"type": "text", "description": "Unusual word without definition: \"proven\"", "value": "Context: \"ities and secure your company.PROVEN TOOLSLevel up your company's\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 65, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 7, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.244Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "ities", "and", "secure", "your", "company.PROVEN", "TOOLSLevel", "up", "company", "s"]}, {"type": "text", "description": "Unusual word without definition: \"soc\"", "value": "Context: \"tial for your compliance with SOC 2, ISO 27001, cyber insurance\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 65, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 8, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.244Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "tial", "for", "your", "compliance", "with", "SOC", "ISO", "cyber", "insurance"]}, {"type": "text", "description": "Unusual word without definition: \"iso\"", "value": "Context: \"r your compliance with SOC 2, ISO 27001, cyber insurance, and m\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 65, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 9, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.244Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "r", "your", "compliance", "with", "SOC", "ISO", "cyber", "insurance", "and", "m"]}, {"type": "text", "description": "Unusual word without definition: \"gdpr\"", "value": "Context: \"ilityWith regulations such as GDPR and CCPA, failure to maintain\"", "selector": "#__next", "severity": "warning", "metadata": {"scanDuration": 65, "elementsAnalyzed": 11, "checkSpecificData": {"automationRate": 0.6, "checkType": "content-analysis", "languageAnalysis": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 10, "ruleId": "WCAG-060", "ruleName": "Unusual Words", "timestamp": "2025-07-11T11:39:08.244Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["#__next", "Context", "ilityWith", "regulations", "such", "as", "GDPR", "and", "CCPA", "failure", "to", "maintain"]}], "timestamp": 1752233948244, "hash": "03787089e535f7d9e0dccff0b3d7a7b1", "accessCount": 1, "lastAccessed": 1752233948244, "size": 9135}
{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame '1A24C94F5DC5C4100E3B2E2536AEC912'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A24C94F5DC5C4100E3B2E2536AEC912"], "metadata": {"scanDuration": 22, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T18:03:51.384Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true}}}], "timestamp": 1752257031384, "hash": "8687bba8b605a1d0ec21d74414791f9f", "accessCount": 1, "lastAccessed": 1752257031384, "size": 779, "metadata": {"originalKey": "WCAG-026:WCAG-026:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-026_wcag-026_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752257031384, "version": "1.1", "keyHash": "9e0a7c6b26dbc6722681bb7546e3f852"}}
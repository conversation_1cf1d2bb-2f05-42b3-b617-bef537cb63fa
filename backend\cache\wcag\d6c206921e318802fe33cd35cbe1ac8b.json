{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame 'A362B1B11B79F8E7E1503ABC8B8AFA25'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "A362B1B11B79F8E7E1503ABC8B8AFA25"], "metadata": {"scanDuration": 1, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.85, "checkType": "link-purpose-analysis", "linkAnalysis": true, "contextAnalysis": true, "aiSemanticValidation": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-026", "ruleName": "<PERSON> P<PERSON>", "timestamp": "2025-07-11T18:49:51.684Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true}}}], "timestamp": 1752259791684, "hash": "8e8b532fc1302c561eb33ec6e643c2b2", "accessCount": 1, "lastAccessed": 1752259791684, "size": 779, "metadata": {"originalKey": "WCAG-026:WCAG-026:dGV4dDpUZWNobmljYWwgZXJyb3IgZHVy", "normalizedKey": "wcag-026_wcag-026_dgv4ddpuzwnobmljywwgzxjyb3igzhvy", "savedAt": 1752259791685, "version": "1.1", "keyHash": "9e0a7c6b26dbc6722681bb7546e3f852"}}